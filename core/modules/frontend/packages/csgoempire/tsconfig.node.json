{"extends": "@tsconfig/node18/tsconfig.json", "include": ["vite.config.ts", "vitest.config.ts", "src/**/*.vite.ts", "src/lang/**/*.json"], "compilerOptions": {"composite": true, "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "resolveJsonModule": true, "allowJs": true, "allowImportingTsExtensions": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo"}}