import Btn from '@/components/ui/Btn/Btn.vue';
import ButtonText from '@/components/ui/ButtonText/ButtonText.vue';
import Copy from '@/components/ui/Copy/Copy.vue';
import Currency from '@/components/ui/Currency/Currency.vue';
import Feature from '@/components/ui/Feature/Feature.vue';
import Heading from '@/components/ui/Heading.vue';
import Popover from '@/components/ui/Popover/Popover.vue';
import Tooltip from '@/components/ui/Tooltip/Tooltip.vue';

declare module '@vue/runtime-core' {
    export interface GlobalComponents {
        Btn: typeof Btn;
        ButtonText: typeof ButtonText;
        Copy: typeof Copy;
        Currency: typeof Currency;
        Feature: typeof Feature;
        Heading: typeof Heading;
        Popover: typeof Popover;
        Tooltip: typeof Tooltip;
    }
}
