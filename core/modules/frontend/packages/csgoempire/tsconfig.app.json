{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "global.d.ts", "globalComponents.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.json", "tests/**/*", "tests/**/*.json", ".storybook/**/*.ts", ".storybook/**/*.vue", "tailwind.config.ts"], "exclude": ["tests/matchers/*", "tests/unit/*", "tests/globalSetup.ts", "tests/setup.ts", "tests/setupPlugins.ts", "tests/**/*.json", "src/**/*.spec.js", "src/**/*.spec.ts"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "allowJs": true, "allowImportingTsExtensions": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo"}}