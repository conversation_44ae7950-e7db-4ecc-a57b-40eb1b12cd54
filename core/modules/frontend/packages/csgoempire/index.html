<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta
            http-equiv="X-UA-Compatible"
            content="IE=edge"
        />
        <meta
            name="viewport"
            content="width=device-width,initial-scale=1.0"
        />
        <link
            rel="preload"
            href="/icons/logo-preloader.svg"
            as="image"
            type="image/svg+xml"
        />

        <link
            rel="apple-touch-icon"
            sizes="180x180"
            href="/icons/apple-touch-icon.png?v=5"
        />
        <link
            rel="icon"
            type="image/png"
            sizes="32x32"
            href="/icons/favicon-32x32.png?v=5"
        />
        <link
            rel="icon"
            type="image/png"
            sizes="16x16"
            href="/icons/favicon-16x16.png?v=5"
        />
        <link
            rel="mask-icon"
            href="/icons/safari-pinned-tab.svg?v=5"
            color="#c39714"
        />
        <link
            rel="shortcut icon"
            href="/icons/favicon.ico?v=5"
        />
        <link
            rel="canonical"
            href="https://csgoempire.com/"
        />
        <meta
            name="msapplication-TileColor"
            content="#ffc40d"
        />
        <meta
            name="og:image"
            content="https://csgoempire.com/img/csgoempire-banner.png"
        />

        <script
            src="https://cdn.seondf.com/js/v5/agent.js"
            async
        ></script>
    </head>
    <body>
        <noscript>
            <div
                style="
                    color: #e1b23f;
                    font-family: sans-serif;
                    padding: 20px;
                    text-align: center;
                "
            >
                We're sorry but the site doesn't work properly without
                JavaScript enabled. Please enable it to continue.
            </div>
        </noscript>
        <div
            id="popovers"
            class="relative z-1003"
        ></div>
        <div id="app"></div>
        <style>
            .preloader__container {
                background: #101014;
                font-size: 16px;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                position: fixed;
                z-index: 9999;
                top: 0;
                left: 0;
            }

            #preloader {
                transition: opacity 300ms ease-in-out;
            }

            #preloader.preloader__loaded {
                opacity: 0;
                pointer-events: none;
                transition: opacity 300ms ease-in-out;
            }

            .preloader__item {
                box-sizing: border-box;
                width: 120px;
                height: 120px;
                padding: 10px;
                position: relative;
            }

            .preloader__spinner {
                box-sizing: content-box;
                transform: translateZ(0) rotate(0deg);
                border: 3px solid #e9b10b;
                border-top-color: transparent;
                border-left-color: transparent;
                position: absolute;
                top: -3px;
                left: -3px;
                width: 100%;
                height: 100%;
                border-radius: 100%;
                animation: dice-spinner 950ms infinite linear;
            }

            @media screen and (min-width: 768px) {
                .preloader__item {
                    width: 200px;
                    height: 200px;
                    padding: 20px;
                }

                .preloader__spinner {
                    border: 5px solid #e9b10b;
                    border-top-color: transparent;
                    border-left-color: transparent;
                    top: -5px;
                    left: -5px;
                }
            }

            @keyframes dice-spinner {
                to {
                    transform: translateZ(0) rotate(360deg);
                }
            }

            .preloader__logo {
                height: 100%;
                width: 100%;
            }
        </style>

        <div
            id="preloader"
            class="preloader__container"
        >
            <div class="preloader__item">
                <div class="preloader__spinner"></div>
                <img
                    class="preloader__logo"
                    src="/icons/logo-preloader.svg"
                    alt=""
                />
            </div>
        </div>
        <script
            type="module"
            src="/src/main.js"
        ></script>
    </body>
</html>
