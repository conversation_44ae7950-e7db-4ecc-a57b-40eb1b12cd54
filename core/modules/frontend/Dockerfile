# Get Unbuntu image
FROM ubuntu:22.04 AS base
ENV DEBIAN_FRONTEND=noninteractive

# Upgrade repos and install packages
RUN apt-get -qq -o=Dpkg::Use-Pty=0 update && apt-get -qq -o=Dpkg::Use-Pty=0 -y upgrade \
    && apt-get -qq -o=Dpkg::Use-Pty=0 --assume-yes install \
    bash \
    git  \
    dos2unix \
    software-properties-common \
    libc6

# Add service specific packages
FROM base AS packages
USER root

# Upgrade repos and install packages
RUN apt-get -qq -o=Dpkg::Use-Pty=0 update && apt-get -qq -o=Dpkg::Use-Pty=0 -y upgrade \
    && apt-get -qq -o=Dpkg::Use-Pty=0 --assume-yes install \
    nginx \
    curl

# Install nodejs20
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Service stage
FROM packages AS site-frontend

ARG IS_LOCAL=0
ARG CLUSTER=local
ARG VITE_ENV_CLASS=main
ARG APP=csgoempire

# Create non-root user
RUN adduser appuser --disabled-login --gecos "" --home "/app" --no-create-home \
    && addgroup appgroup && adduser appuser appgroup

# Give non-user ownership of folders
RUN mkdir /app && chown appuser:appgroup /app \
    && chown appuser:appgroup /var/lib/nginx \
    && chmod 775 /var/lib/nginx \
    && mkdir /var/run/nginx && chown appuser:appgroup /var/run/nginx

# Create the directories we will need.
RUN mkdir -p /var/log/nginx && chown appuser:appgroup /var/log/nginx

# Copy the respective nginx configuration files.
COPY nginx_config/nginx.conf /etc/nginx/nginx.conf
COPY nginx_config/conf.d /etc/nginx/conf.d

# Run DOS2UNIX convertion
RUN find /etc/nginx -type f -exec dos2unix {} '+'

USER appuser

RUN git config --global url."https://github.com/".insteadOf **************:
RUN git config --global url."https://".insteadOf git://
RUN git config --global url."https://".insteadOf ssh://

# Install NPM packages in a separate layer
COPY --chown=appuser:appgroup /package.json /tmp/package.json
COPY --chown=appuser:appgroup /package-lock.json /tmp/package-lock.json
COPY --chown=appuser:appgroup /packages/build-config/package.json /tmp/packages/build-config/package.json
COPY --chown=appuser:appgroup /packages/shared/package.json /tmp/packages/shared/package.json
COPY --chown=appuser:appgroup /packages/csgoempire/package.json /tmp/packages/csgoempire/package.json
COPY --chown=appuser:appgroup /packages/duel/package.json /tmp/packages/duel/package.json
RUN cd /tmp && npm ci --no-color --quiet

# Set the directory we want to run the next commands for
WORKDIR /var/www

# Copy our source code into the container
COPY --chown=appuser:appgroup / ./frontend
RUN cp -a /tmp/node_modules ./frontend
COPY --chown=appuser:appgroup /scripts/test.sh ./scripts/test.sh
COPY --chown=appuser:appgroup /scripts/entrypoint.local.sh ./scripts/entrypoint.local.sh

# fix permissions by pre-creating the node_modules folders
RUN mkdir -p ./frontend/packages/csgoempire/node_modules && \
    mkdir -p ./frontend/packages/duel/node_modules && \
    mkdir -p ./frontend/packages/shared/node_modules && \
    mkdir -p ./frontend/packages/build-config/node_modules

# Build FE and StoryBook

USER root
RUN sed -i "s,change-this,$VITE_ENV_CLASS," /etc/nginx/conf.d/default.conf

# Set up symbolic link for the build
RUN ln -sf /var/www/frontend/packages/$APP/build /var/www/httpdocs
USER appuser

RUN --mount=type=secret,id=_env,uid=1000,dst=./frontend/build.env \
    if [ "$IS_LOCAL" != "1" ]; then \
      cd frontend \
      && export $(grep -v '^#' .env | xargs) \
      && export $(grep -v '^#' build.env | xargs) \
      && npm run build:$APP; \
    fi

RUN if [ "$CLUSTER" != "k-01-p" ] && [ "$CLUSTER" != "k-01-duel-prod" ] && [ ! -z $CLUSTER ]; then \
        cd frontend/packages/$APP \
        && npm run build-storybook; \
    fi

# This should be removed in the next optimization cycle
RUN ln -sf /dev/stdout /var/log/nginx/access.log \
    && ln -sf /dev/stderr /var/log/nginx/error.log

EXPOSE 5173

ENTRYPOINT ["nginx", "-g", "daemon off;"]
