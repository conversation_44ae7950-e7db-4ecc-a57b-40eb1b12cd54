// This is a super simple script that removes empty objects from all the translation JSON files and ensures that the files are formatted correctly.
// This can be run after running linting as certain rules don't cleanup the files properly.
// Don't overthink this. It will likely be removed in the future.
const fs = require('fs');
const path = require('path');

const dirPaths = [
    path.join(__dirname, '../packages/csgoempire/src/lang'),
    path.join(__dirname, '../packages/duel/src/lang')
];

const baseLangFile = 'enDev.json'; // Define the base language file

// Function to check and remove empty objects recursively
function removeEmptyObjects(obj) {
    for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
            removeEmptyObjects(obj[key]);
            if (Object.keys(obj[key]).length === 0) {
                delete obj[key];
            }
        }
    }
}

function cleanWhitespaces(data) {
    let fixedContent = data;

    fixedContent = fixedContent
        .replace(/\uFF5B/g, '{')
        .replace(/\uFF5D/g, '}')
        .replace(/\uFF08/g, '(')
        .replace(/\uFF09/g, ')')
        .replace(/\uFF0F/g, '/')
        .replace(/\uFF1B/g, ';')
        .replace(/\uFF1F/g, '?')
        .replace(/\uFF01/g, '!')
        .replace(/\uFF1A/g, ':');

    // Replace all sequences of whitespace characters with a single space
    fixedContent = fixedContent.replace(/[\u200B-\u200D\uFEFF\xA0\u1680\u180e\u2000-\u200a\u2028\u2029\u202f\u205f\u3000]+/g, '');
    fixedContent = fixedContent.replace(/[\uFEFF\xA0]+/g, '');
    fixedContent = fixedContent.replace(/[\s\uFEFF\xA0]+/g, ' ');
    return fixedContent;
}

// Function to extract variables from a string
function extractVariables(str) {
    const variables = new Set();
    str.replace(/\{([\w_]+)\}/g, (_, varName) => {
        variables.add(varName);
    });
    return variables;
}

// Function to validate translation variables
function validateTranslationVariables(enData, langData, langFileName, appName) {
    if (langFileName === baseLangFile) {
        return; // Skip validation for base language file
    }
    let hasErrors = false;

    function checkObject(enObj, langObj, path = '') {
        for (const key in enObj) {
            const enValue = enObj[key];
            const langValue = langObj ? langObj[key] : undefined;
            const currentPath = path ? `${path}.${key}` : key;

            if (typeof enValue === 'object' && enValue !== null) {
                checkObject(enValue, langValue, currentPath);
            } else if (typeof enValue === 'string' && langValue) {
                const enVars = extractVariables(enValue);
                const langVars = langValue ? extractVariables(langValue) : new Set();

                if (!arraysEqual([...enVars], [...langVars])) {
                    console.error(`Variable mismatch for '${currentPath}' in '${langFileName}' (${appName}):\n  Source: ${enValue}\n  Target: ${langValue}\n  Vars expected: ${[...enVars].length ? [...enVars] : "none"}\n  Vars found: ${[...langVars].length ? [...langVars] : "none"}`);
                    hasErrors = true;
                }
            } else if (!langValue && process.env.LIST_MISSING_TRANSLATIONS) {
                console.error(`Missing translation for '${currentPath}' "${enValue}" in '${langFileName}' (${appName})`);
                hasErrors = true;
            }
        }
    }

    checkObject(enData, langData);
    if (hasErrors) {
        console.error(`Validation failed for '${langFileName}' (${appName}).`);
        process.exit(1);
    }
}

function arraysEqual(a, b) {
    return a.length === b.length && a.sort().every((val, index) => val === b.sort()[index]);
}

function cleanDir(dirPath) {
    const enFilePath = path.join(dirPath, baseLangFile);

    let enData;
    try {
        enData = JSON.parse(fs.readFileSync(enFilePath, 'utf8'));
    } catch (err) {
        console.error(`Error reading or parsing '${baseLangFile}': ${err}`);
        return;
    }

    fs.readdir(dirPath, (err, files) => {
        if (err) {
            console.error('Error reading directory:', err);
            return;
        }

        files.forEach((file) => {
            if (path.extname(file) !== '.json') {
                return;
            }

            const filePath = path.join(dirPath, file);
            fs.readFile(filePath, 'utf8', (err, data) => {
                if (err) {
                    console.error(`Error reading file '${file}': ${err}`);
                    return;
                }

                try {
                    const cleanedData = cleanWhitespaces(data);
                    const jsonData = JSON.parse(cleanedData);
                    removeEmptyObjects(jsonData);

                    const appName = dirPath.includes('csgoempire') ? 'csgoempire' : 'duel';
                    if (file !== 'en-PS.json') {
                        validateTranslationVariables(enData, jsonData, file, appName);
                    }

                    fs.writeFile(
                        filePath,
                        JSON.stringify(jsonData, null, 4),
                        (err) => {
                            if (err) {
                                console.error(`Error writing file '${file}': ${err}`);
                            } else {
                                console.log(`Updated '${file}'`);
                            }
                        },
                    );
                } catch (e) {
                    console.error(`Error parsing JSON in '${file}': ${e}`);
                }
            });
        });
    });
}

dirPaths.forEach((dirPath) => {
    console.log('Cleanup directory:', dirPath);
    cleanDir(dirPath);
});
