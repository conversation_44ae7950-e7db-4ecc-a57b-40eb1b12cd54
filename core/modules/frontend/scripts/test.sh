#!/bin/bash

set -e

echo "Run test..."

# source files are in frontend dir in Dockerfile
if [ -d "./frontend" ]; then
    # for pipeline
    cd ./frontend || exit 1
fi

if [ -z "$1" ] || [ -z "$2" ]; then
    npm run type-check:shared >> type_check_test_output.log 2>&1 || exit 1 && npm run type-check:csgoempire >> csgoempire_test_output.log 2>&1 || exit 1 && npm run type-check:duel >> duel_test_output.log 2>&1  || exit 1
    npm run test >> test_output.log 2>&1 || exit 1
else
    if [[ $1 == "1" || $1 == 1 ]]; then
        npm run type-check:shared >> type_check_test_output.log 2>&1 || exit 1 && npm run type-check:csgoempire >> csgoempire_test_output.log 2>&1 || exit 1 && npm run type-check:duel >> duel_test_output.log 2>&1  || exit 1
    fi
    npm run test -- --shard=$1/$2 >> test_output.log 2>&1 || exit 1
fi

echo "Tests done."
