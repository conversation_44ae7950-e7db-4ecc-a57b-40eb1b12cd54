// Using API output from https://api.twitchemotes.com/api/v4/channels/0
const argv = require('minimist')(process.argv.slice(2));

const fs = require('fs');

const json = JSON.parse(fs.readFileSync(argv.in_file));

const { emotes } = json;

const output = {};

emotes.forEach((emote) => {
    output[emote.code] = `https://static-cdn.jtvnw.net/emoticons/v1/${emote.id}/1.0`;
});

fs.writeFileSync(argv.out_file, JSON.stringify(output), 'utf8');
