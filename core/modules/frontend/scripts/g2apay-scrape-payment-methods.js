const argv = require('minimist')(process.argv.slice(2));

const Axios = require('axios');
const axiosCookieJarSupport = require('axios-cookiejar-support').default;
const tough = require('tough-cookie');
const fs = require('fs');

const { token, out_file: outFile } = argv;

axiosCookieJarSupport(Axios);
const cookieJar = new tough.CookieJar();
const axios = Axios.create(
    {
        withCredentials: true,
        jar: cookieJar,
    },
);

main();

async function main() {
    const countriesObj = await getCountries();
    const output = {};
    for (const [countryCode, countryData] of Object.entries(countriesObj)) {
        const locale = countryData.iso;
        const paymentMethods = await getPaymentMethods(locale);
        output[countryCode] = paymentMethods;
    }
    fs.writeFileSync(outFile, JSON.stringify(output), 'utf8');
}

async function getCountries() {
    const response = await axios.get(`https://checkout.pay.g2a.com/index/gateway?token=${token}`);
    const matches = /COUNTRY_LIST: {(.*?)}}/.exec(response.data);
    return JSON.parse(`{${matches[1]}}}`);
}

async function getPaymentMethods(locale) {
    const url = `https://checkout.pay.g2a.com/index/init?set_locale=${locale}&token=${token}`;
    console.log(url);
    try {
        const response = await axios.get(url);
        return response.data.payment_methods.map(method => ({
            key: method.codename,
            name: method.name,
            description: method.description,
        }));
    } catch (err) {
        if (
            err.response
            && err.response.data
            && err.response.data.code
            && err.response.data.code === 'PC0017'
        ) {
            return [];
        }
        console.log(`Failed to get payment methods for ${locale}: ${err}`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return getPaymentMethods(locale);
    }
}
