// https://api.betterttv.net/2/emotes
// Image sizes can be 1x, 2x or 3x
const argv = require('minimist')(process.argv.slice(2));

const fs = require('fs');

const json = JSON.parse(fs.readFileSync(argv.in_file));

const { emotes } = json;

const output = {};

emotes.forEach((emote) => {
    output[emote.code] = `https://cdn.betterttv.net/emote/${emote.id}/1x`;
});

fs.writeFileSync(argv.out_file, JSON.stringify(output), 'utf8');
