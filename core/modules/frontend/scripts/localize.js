const Transfluent = require('./localize/transfluent/index');

if (!process.argv[2]) {
    badCommand();
}

switch (process.argv[2]) {
    case 'authenticate':
        require('./localize/commands/authenticate');
        break;

    case 'upload':
        require('./localize/commands/upload');
        break;

    case 'order':
        require('./localize/commands/order');
        break;

    case 'download':
        require('./localize/commands/download');
        break;

    default:
        badCommand();
}

function badCommand() {
    console.error('Usage: node localize.js <command>\n  Available commands:\n    - authenticate\n    - upload\n    - order\n    - download');
    process.exit(Transfluent.ExitCodes.BadArgument);
}
