const FS = require('fs');
const Path = require('path');
const Pseudolocale = require('pseudolocale');

const EN_SRC_FILE = Path.join(__dirname, '..', 'app', 'src', 'lang', 'en.json');
const EN_DEST_FILE = Path.join(__dirname, '..', 'app', 'src', 'lang', 'en-PS.json');

Pseudolocale.option.prepend = '[!!! ';
Pseudolocale.option.append = ' !!!]';
Pseudolocale.option.startDelimiter = '{';
Pseudolocale.option.endDelimiter = '}';

generatePseudoLocale();
if (process.argv[2] === 'watch') {
    console.log(`Starting watcher for file ${EN_SRC_FILE}`);
    FS.watch(EN_SRC_FILE, (eventType, filename) => {
        if (eventType === 'change') {
            generatePseudoLocale();
        }
    });
}

function generatePseudoLocale() {
    try {
        FS.writeFileSync(EN_DEST_FILE, JSON.stringify(localize(JSON.parse(FS.readFileSync(EN_SRC_FILE).toString('utf8'))), undefined, 4));
        console.log(`Wrote pseudolocale to ${EN_DEST_FILE}`);
    } catch (ex) {
        console.log(`Unable to localize: ${ex.message}`);
    }
}

function localize(obj) {
    if (typeof obj === 'string') {
        // Is this a pluralized string?
        const parts = obj.split(' | ');
        if (parts.length > 1) {
            return parts.map(Pseudolocale.str).join(' | ');
        }

        return Pseudolocale.str(obj);
    }

    for (const i in obj) {
        obj[i] = localize(obj[i]);
    }

    return obj;
}
