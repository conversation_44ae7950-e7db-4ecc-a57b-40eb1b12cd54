const Crypto = require('crypto');
const FS = require('fs');
const HTTPS = require('https');
const Path = require('path');
const QueryString = require('querystring');

module.exports = Transfluent;

function Transfluent() {
    this.config = {};
    this.metadata = {};
    this._token = null;

    try {
        const fileContents = FS.readFileSync(Transfluent.getConfigPath());
        const config = JSON.parse(fileContents.toString('utf8'));
        if (config) {
            this.config = config;
        }

        if (config.transfluentToken) {
            this._token = config.transfluentToken;
        }
    } catch (ex) {
        // doesn't matter
    }

    try {
        const fileContents = FS.readFileSync(Transfluent.getMetadataPath());
        const metadata = JSON.parse(fileContents.toString('utf8'));
        if (metadata) {
            this.metadata = metadata;
        }
    } catch (ex) {
        // doesn't matter
    }

    if (!this.metadata.stringStatus) {
        this.metadata.stringStatus = {};
    }
}

Transfluent.ExitCodes = {
    BadArgument: 1,
    Unauthorized: 2,
};

/**
 * Get the path where the config file is stored.
 * @returns {string}
 */
Transfluent.getConfigPath = function() {
    return Path.join(__dirname, '..', 'localize_config.json');
};

/**
 * Get the path where the metadata file is stored.
 * @returns {string}
 */
Transfluent.getMetadataPath = function() {
    return Path.join(__dirname, '..', 'localize_metadata.json');
};

/**
 * Save the config file to disk.
 */
Transfluent.prototype.saveConfig = function() {
    FS.writeFileSync(Transfluent.getConfigPath(), JSON.stringify(this.config, undefined, 4));
    FS.writeFileSync(Transfluent.getMetadataPath(), JSON.stringify(this.metadata, undefined, 4));
};

/**
 * Return whether this instance has an authentication token.
 * @returns {boolean}
 */
Transfluent.prototype.hasToken = function() {
    return Boolean(this._token);
};

/**
 * Authenticate with Transfluent
 * @param {string} email
 * @param {string} password
 * @returns {Promise<string>}
 */
Transfluent.prototype.authenticate = async function(email, password) {
    const token = await this._apiRequest('POST', '/authenticate', { email, password });
    this._token = token;
    return token;
};

Transfluent.prototype.uploadStrings = async function(minorEdit = false) {
    const path = Path.join(__dirname, '..', '..', '..', 'app', 'src', 'lang', 'en.json');
    const lang = require(path);
    const flattened = flattenObject(lang);

    // We also need to break pluralized strings out
    for (const i in flattened) {
        const parts = flattened[i].split(' | ');
        if (parts.length === 1) {
            continue;
        }

        delete flattened[i];
        if (parts.length === 2) {
            flattened[`${i}.singular`] = parts[0];
            flattened[`${i}.plural`] = parts[1];
        } else {
            flattened[`${i}.zero`] = parts[0];
            flattened[`${i}.singular`] = parts[1];
            flattened[`${i}.plural`] = parts[2];
        }
    }

    const localizationKeys = Object.keys(flattened);
    const stringsToUpload = {};

    // Now find out which strings are new or changed
    localizationKeys.forEach((key) =>{
        const sha1 = sha1str(flattened[key]);

        if (!this.metadata.stringStatus[key] || sha1 !== this.metadata.stringStatus[key].sha1) {
            // it's a new or modified string
            stringsToUpload[key] = flattened[key];
            this.metadata.stringStatus[key] = {
                sha1,
                uploaded: Date.now(),
                ordered: [],
                translated: [],
            };
        }
    });

    if (Object.keys(stringsToUpload).length === 0) {
        return 0;
    }

    const body = {
        group_id: 'csgoempire',
        language: 'en-us',
        texts: stringsToUpload,
        invalidate_translations: minorEdit ? '0' : '1',
    };

    const result = await this._apiRequest('POST', '/v2/texts/', body);
    this.saveConfig();
    if (result.status === 'OK') {
        return Object.keys(stringsToUpload).length;
    } else {
        throw new Error(`Bad status ${result.status}`);
    }
};

Transfluent.prototype.orderTranslations = async function(targetLanguages) {
    let orderIds = [];
    let orderedWordCount = 0;

    for (let i = 0; i < targetLanguages.length; i++) {
        let langCode = targetLanguages[i];

        // Build a list of the keys that haven't yet been ordered for this language
        const keys = [];
        for (const i in this.metadata.stringStatus) {
            const val = this.metadata.stringStatus[i];
            if (!val.ordered.includes(langCode)) {
                keys.push(i);
            }
        }

        if (keys.length === 0) {
            // Nothing to do for this language
            continue;
        }

        // Place an order for this language
        console.log(`Ordering ${langCode} (${keys.length} keys)...`);
        const result = await this._apiRequest('POST', '/v2/texts/translate/', {
            group_id: 'csgoempire',
            level: 'expert',
            expertise: 'Gaming',
            source_language: 'en-us',
            target_languages: [langCode],
            texts: keys.map(k => ({id: k})),
        });

        if (result.status !== 'OK') {
            throw new Error(`Bad status ${result.status}`);
        }

        console.log(result);

        keys.forEach((key) => {
            if (!this.metadata.stringStatus[key].ordered.includes(langCode)) {
                this.metadata.stringStatus[key].ordered.push(langCode);
            }
        });

        orderIds = orderIds.concat(result.response.orders);
        orderedWordCount += result.response.ordered_word_count;
        this.saveConfig();
    }

    return {
        orderIds,
        orderedWordCount,
    };
};

Transfluent.prototype.getLanguagesRequiringDownload = function() {
    const langs = {};
    for (const i in this.metadata.stringStatus) {
        this.metadata.stringStatus[i].ordered.forEach((langCode) => {
            if (!this.metadata.stringStatus[i].translated.includes(langCode)) {
                langs[langCode] = true;
            }
        });
    }

    return Object.keys(langs);
};

Transfluent.prototype.downloadLanguage = async function(langCode) {
    const strings = {};
    let offset = 0;

    try {
        // The loop will be broken by an Error being thrown since that's how transfluent informs us that we're out of strings.
        // Or by returning once we hit a page with less than the limit.
        while (true) {
            const result = await this._apiRequest('GET', '/v2/texts/', {
                group_id: 'csgoempire',
                language: langCode,
                limit: 100,
                offset,
            });

            if (result.status !== 'OK' || !result.response) {
                throw new Error('Malformed transfluent response');
            }

            for (const i in result.response) {
                strings[result.response[i].text_id] = result.response[i].text;
            }

            const numStrings = Object.keys(result.response).length;
            if (numStrings < 100) {
                // this is the last page
                return strings;
            }

            offset += numStrings;
        }
    } catch (ex) {
        if (ex.body && ex.body.error && ex.body.error.type === 'EBackendItemNotFound') {
            return strings;
        }

        throw ex;
    }
};

/**
 * Make an API request.
 * @param {string} method
 * @param {string} urlPath
 * @param {object|string} body
 * @returns {Promise<*>}
 * @private
 */
Transfluent.prototype._apiRequest = async function(method, urlPath, body) {
    if (this._token && typeof body === 'object') {
        body.token = this._token;
    }

    const headers = {};
    if (body && typeof body === 'object' && method !== 'GET') {
        body = JSON.stringify(body);
        headers['content-type'] = 'application/json';
    }

    const options = {
        method: method.toUpperCase(),
        headers,
        host: 'transfluent.com',
        path: urlPath + (method === 'GET' && body ? '?' + QueryString.stringify(body) : ''),
        body: method === 'GET' ? undefined : body,
    };

    return await req(options);
};

function req(options) {
    return new Promise((resolve, reject) => {
        let finished = false;

        const body = options.body || undefined;
        delete options.body;

        const request = HTTPS.request(options, (res) => {
            res.on('error', (err) => {
                if (finished) {
                    return;
                }

                finished = true;
                reject(err);
            });

            let response = '';
            res.on('data', (chunk) => {
                if (finished) {
                    return;
                }

                response += chunk.toString('utf8');
            });

            res.on('end', () => {
                if (finished) {
                    return;
                }

                finished = true;
                let body = null;
                try {
                    body = JSON.parse(response);
                } catch (ex) {
                    if (res.statusCode !== 204 && res.statusCode < 300) {
                        // This was supposedly a successful response
                        return reject(ex);
                    }

                    // if not supposedly successful, we'll handle that below
                }

                if (res.statusCode > 299) {
                    const err = new Error((body && body.message) || `HTTP error ${res.statusCode}`);
                    err.body = body;
                    return reject(err);
                }

                return resolve(body);
            });
        });

        request.on('error', (err) => {
            finished = true;
            reject(err);
        });

        request.end(body);
    });
}

function flattenObject(obj, pathSoFar = [], result = {}) {
    if (typeof obj !== 'object') {
        result[pathSoFar.join('.')] = obj;
    } else {
        for (const i in obj) {
            flattenObject(obj[i], pathSoFar.concat([i]), result);
        }
    }

    return result;
}

function sha1str(string) {
    const hash = Crypto.createHash('sha1');
    hash.update(string);
    return hash.digest('hex');
}
