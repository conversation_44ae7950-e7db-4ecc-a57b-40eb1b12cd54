const Transfluent = require('../transfluent/index');

const tf = new Transfluent();
if (!tf.hasToken()) {
    console.error('Must use authenticate command first');
    process.exit(Transfluent.ExitCodes.Unauthorized);
}

main();
async function main() {
    const isMinor = process.argv[3] === 'minor';
    if (isMinor) {
        console.log('Marking as a minor edit; existing localizations will not be deleted.');
    }

    const numUploadedStrings = await tf.uploadStrings(isMinor);
    if (numUploadedStrings === 0) {
        console.log('No new or modified strings to upload');
    } else {
        console.log(`${numUploadedStrings} new/modified strings uploaded`);
    }
}
