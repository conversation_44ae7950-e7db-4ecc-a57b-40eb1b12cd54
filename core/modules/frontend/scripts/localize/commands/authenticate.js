const FS = require('fs');
const Path = require('path');

const Transfluent = require('../transfluent/index.js');

if (!process.argv[3] || !process.argv[4]) {
    console.error('Usage: node localize.js authenticate <transfluent email> <password>');
    process.exit(Transfluent.ExitCodes.BadArgument);
}

async function main() {
    const tf = new Transfluent();
    const token = await tf.authenticate(process.argv[3], process.argv[4]);
    console.log(`Transfluent token: ${token}`);

    tf.config.transfluentToken = token;
    tf.saveConfig();
    console.log('Token saved in localize_config.json');
}

main().catch((err) => {
    console.error(err);
});
