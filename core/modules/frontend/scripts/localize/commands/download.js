const FS = require('fs');
const Path = require('path');

const Transfluent = require('../transfluent/index');

const tf = new Transfluent();
if (!tf.hasToken()) {
    console.error('Must use authenticate command first');
    process.exit(Transfluent.ExitCodes.Unauthorized);
}

main();
async function main() {
    const languagesToDownload = tf.getLanguagesRequiringDownload();
    console.log(`${languagesToDownload.length} languages need to be downloaded: ${languagesToDownload.join(', ')}`);

    await Promise.all(languagesToDownload.map((langCode) => new Promise(async (resolve) => {
        const strings = await tf.downloadLanguage(langCode);
        for (const i in strings) {
            if (!tf.metadata.stringStatus[i].translated.includes(langCode)) {
                tf.metadata.stringStatus[i].translated.push(langCode);
            }
        }

        let localizations = {};
        const langCodeParts = langCode.split('-');
        const filename = langCodeParts[0] + (langCodeParts[0] !== langCodeParts[1] ? langCodeParts[1].toUpperCase() : '');
        const path = Path.join(__dirname, '..', '..', '..', 'app', 'src', 'lang', `${filename}.json`);
        if (FS.existsSync(path)) {
            localizations = JSON.parse(FS.readFileSync(path).toString('utf8'));
        }

        unflattenObjectToDestination(strings, localizations);
        fixPlurals(localizations);
        FS.writeFileSync(path, JSON.stringify(localizations));
        console.log(`Wrote ${Object.keys(strings).length} strings for language ${filename} to file ${path}`);
        resolve();
    })));

    tf.saveConfig();

    const pendingLanguages = {};
    for (const i in tf.metadata.stringStatus) {
        const val = tf.metadata.stringStatus[i];
        val.ordered.forEach((langCode) => {
            if (!val.translated.includes(langCode)) {
                pendingLanguages[langCode] = pendingLanguages[langCode] || 0;
                pendingLanguages[langCode]++;
            }
        });
    }

    for (const i in pendingLanguages) {
        console.log(`Still awaiting ${pendingLanguages[i]} strings to be translated for ${i}`);
    }
}

function unflattenObjectToDestination(source, destination) {
    for (const i in source) {
        const path = i.split('.');
        let objSoFar = destination;
        path.forEach((pathPart, idx) => {
            if (idx === path.length - 1) {
                // This is the end
                objSoFar[pathPart] = source[i];
            } else {
                if (typeof objSoFar[pathPart] !== 'object') {
                    objSoFar[pathPart] = {};
                }

                objSoFar = objSoFar[pathPart];
            }
        });
    }

    return destination;
}

function fixPlurals(obj) {
    for (const i in obj) {
        if (typeof obj[i] === 'string') {
            // no change to obj[i]
            continue;
        }

        if (typeof obj[i] === 'object' && isPluralObject(obj[i])) {
            obj[i] = (obj[i].zero ? `${obj[i].zero} | ` : '') + `${obj[i].singular} | ${obj[i].plural}`;
            continue;
        }

        fixPlurals(obj[i]);
    }

    function isPluralObject(testObj) {
        let keys = Object.keys(testObj);
        keys.sort();
        keys = keys.join(',');
        return ['plural,singular', 'plural,singular,zero'].includes(keys);
    }
}
