const Transfluent = require('../transfluent/index');

if (!process.argv[3]) {
    console.error('Usage: node localize.js order <comma-separated list of language codes>');
    process.exit(Transfluent.ExitCodes.BadArgument);
}

main().catch((err) => {
    console.error(err);
});

async function main() {
    const languageCodes = process.argv[3].split(',').map((code) => {
        code = code.toLowerCase();
        const parts = code.split('-');
        if (parts.length === 1) {
            return `${code}-${code}`;
        }
        return code;
    });

    const tf = new Transfluent();
    const result = await tf.orderTranslations(languageCodes);

    console.log(`Ordered translations of ${result.orderedWordCount} words. Generated ${result.orderIds.length} order(s).`);
}
