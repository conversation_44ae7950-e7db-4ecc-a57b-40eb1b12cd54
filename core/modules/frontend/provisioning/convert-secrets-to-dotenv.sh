#!/bin/bash

file_location=$1

display_usage() {
  echo "Usage: $0 <file-with-secrets>"
  exit 1
}

if [  $# -le 0 ]; then
  display_usage
fi

if [[ -s "$1" && -z "$(tail -c 1 "$1")" ]]
then
    echo "Newline at end of file!"
else
    echo "Inserting newline at end of file!"
    echo "" >> $file_location
fi

location=header

while IFS= read -r line
do
  if [[ $line == \#* ]]
  then
    continue
  fi

  # remove leading whitespace characters
  line="${line#"${line%%[![:space:]]*}"}"
  # remove trailing whitespace characters
  line="${line%"${line##*[![:space:]]}"}"

  if [ "$location" = header ]
  then
    if [ "$line" = stringData: ]
    then
      location=stringData
    fi
    continue
  fi

  variable=$(echo "$line" | cut -f 1 -d :)
  value=$(echo "$line" | cut -f 2 -d :)

  # remove leading whitespace characters from the value
  value="${value#"${value%%[![:space:]]*}"}"
  value=$(echo -n "$value")

  if [ -z "$variable" ]
  then
    echo 'You must define a variable.'
    break
  fi

  echo "$variable=$value" >> .env
  
done < $file_location
