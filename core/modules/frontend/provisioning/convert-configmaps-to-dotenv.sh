#!/bin/bash

file_location=$1

display_usage() {
  echo "Usage: $0 <file-with-config-maps>"
  exit 1
}

if [  $# -le 0 ]; then
  display_usage
fi

if [[ -s "$1" && -z "$(tail -c 1 "$1")" ]]
then
    echo "Newline at end of file!"
else
    echo "Inserting newline at end of file!"
    echo "" >> $file_location
fi

location=header
dotenv=''
leading_dotenv=100

while IFS= read -r line
do
  echo $line
  if [[ $line == \#* ]]
  then
    continue
  fi

  if [ "$location" = header ]
  then
    if [ "$line" = data: ]
    then
      location=data
    fi
    continue
  fi

  # awk --field-separator='[^ ]' '{print length($1)}'
  # get a variable with spaces only and compared length
  # get the number of leading whitespace characters
  leading_spaces_str="${line%%[![:space:]]*}"
  leading_spaces=${#leading_spaces_str}

  # remove leading whitespace characters
  line="${line#"${line%%[![:space:]]*}"}"
  # remove trailing whitespace characters
  line="${line%"${line##*[![:space:]]}"}"

  if [ -z "$line" ]
  then
    continue
  fi

  if [ $leading_spaces -le $leading_dotenv ]
  then
    dotenvcheck=$(echo "$line" | awk -F ':' '{print $1}' | awk '{print $1}')
    dotenvpipe=$(echo "$line" | awk -F ':' '{print $2}' | awk '{print $1}')
    endofline=$(echo "$line" | awk -F ':' '{print $3}' | awk '{print $1}')
    if [ -z "$dotenvcheck" ]
    then
      echo 'You must declare a dotenv file.'
      break
    elif [ "$dotenvpipe" != '|-' ]
    then
      echo 'After you declare a dotenv file you must use the |- syntax.'
      break
    elif [ -n "$endofline" ]
    then
      echo 'The |- should be the last string on the dotenv line.'
      break
    fi
    dotenv=$dotenvcheck
    leading_dotenv=$leading_spaces
    echo "Writing to $dotenv"
    continue
  fi

  variable=$(echo "$line" | cut -f 1 -d =)

  if [ -z "$variable" ]
  then
    echo 'You must define a variable.'
    break
  fi

  echo "$line" >> "$dotenv"
done < $file_location
