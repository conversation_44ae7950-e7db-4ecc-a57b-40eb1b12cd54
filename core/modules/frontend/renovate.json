{"extends": ["config:base"], "baseBranches": ["staging"], "automerge": false, "labels": ["renovate", "app-csgoempire"], "branchConcurrentLimit": 2, "prHourlyLimit": 1, "prConcurrentLimit": 2, "dependencyDashboard": true, "semanticCommitScope": "package", "semanticCommitType": "chore", "packageRules": [{"groupName": "all dependencies", "groupSlug": "all", "matchPackageNames": ["*"], "separateMajorMinor": false, "separatePatchReleases": false}, {"matchPackageNames": [], "enabled": false}], "lockFileMaintenance": {"enabled": false}, "prBodyNotes": ["# Override autodns namespace\n\n- "]}