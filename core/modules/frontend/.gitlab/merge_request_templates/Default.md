# Description
<!--
Add as detailed as a possible description here.
-->



# Jira
<!--
List Jira ticket(s). Copy fully ticket URL.
If you don't have relevant ticket, add N/A to this section

For example:
- https://moonrailteam.atlassian.net/browse/MP-2
- [Title of ticket](https://moonrailteam.atlassian.net/browse/MP-2)
-->

-

# Related MRs
<!--
List related merge requests here from other repositories.
If you don't have any related merge requests, add N/A to this section.

For example:
- https://gitlab.com/csgoempire/site/-/merge_requests/582
-->

-

# Override autodns namespace
<!--
Override the namespace you want your autodns to be deployed to.

For example:
- DOP-XXX|MP-XXX|DUEL-XXX
-->

- N/A

# DevOps Info / Checklist
<!-- All need to be checked [x]. You can check them from UI afterward. -->
- [ ] Add ~maintenance label if this MR requires maintenance  
- [ ] Validate in dev namespace or add ~"cannot validate" label if not possible  
- [ ] Write a clear description of this MR  
- [ ] List Jira tickets to Jira -section or add N/A if not relevant  
- [ ] List links to related MRs or add N/A if not relevant  
- [ ] List merge request dependencies if some MR needs to be deployed first  
- [ ] Code linted and tests added  
- [ ] Documentation has been updated or added if needed, and knowledge has been shared with the team  
- [ ] I’ve verified that possible UI changes work also on mobile resolution