{"name": "app", "version": "0.1.0", "private": true, "engineStrict": true, "type": "module", "engine": {"node": ">=20.0.0 <21.0.0", "npm": ">=10.0.0"}, "scripts": {"clean-lang": "node scripts/clean-lang.js && sleep 2 && npm run lint-and-fix", "dev:csgoempire": "cd packages/csgoempire && npm run dev", "dev:duel": "cd packages/duel && npm run dev", "dev:shared": "cd packages/shared && npm run dev", "build:csgoempire": "cd packages/csgoempire && npm run build-only", "build:duel": "cd packages/duel && npm run build-only", "build:shared": "cd packages/shared && npm run build-only", "type-check:csgoempire": "cd packages/csgoempire && npm run type-check", "type-check:duel": "cd packages/duel && npm run type-check", "type-check:shared": "cd packages/shared && npm run type-check", "storybook:csgoempire": "cd packages/csgoempire && npm run storybook", "storybook:duel": "cd packages/duel && npm run storybook", "lint": "prettier . --check && eslint . --no-fix --ext .js,.ts,.vue,.json,.cjs --max-warnings 0", "eslint": "eslint . --ext .js,.ts,.vue,.json,.cjs --max-warnings 0", "lint-and-fix": "prettier . --write && eslint . --fix --ext .js,.ts,.vue,.json,.cjs --max-warnings 0", "test": "vitest"}, "workspaces": ["packages/*"], "devDependencies": {"@rushstack/eslint-patch": "^1.7.2", "eslint": "^8.57.0", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "vitest": "^1.6.0"}}