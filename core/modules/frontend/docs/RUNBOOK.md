## Table of contents

- [Description](#description)
- [Technologies](#technologies)
- [Overview](#overview)
  * [Business overview](#business-overview)
  * [Technical overview](#technical-overview)
  * [Service Level Agreements (SLAs)](#service-level-agreements--slas-)
  * [Service owner](#service-owner)
  * [Contributing applications, daemons, services, middleware](#contributing-applications--daemons--services--middleware)
- [System characteristics](#system-characteristics)
  * [Hours of operation](#hours-of-operation)
    + [Hours of operation - core features](#hours-of-operation---core-features)
    + [Hours of operation - secondary features](#hours-of-operation---secondary-features)
  * [Data and processing flows](#data-and-processing-flows)
  * [Infrastructure and network design](#infrastructure-and-network-design)
  * [Resilience, Fault Tolerance (FT) and High Availability (HA)](#resilience--fault-tolerance--ft--and-high-availability--ha-)
  * [Throttling and partial shutdown](#throttling-and-partial-shutdown)
    + [Throttling and partial shutdown - external requests](#throttling-and-partial-shutdown---external-requests)
    + [Throttling and partial shutdown - internal components](#throttling-and-partial-shutdown---internal-components)
  * [Expected traffic and load](#expected-traffic-and-load)
    + [Hot or peak periods](#hot-or-peak-periods)
    + [Warm periods](#warm-periods)
    + [Cool or quiet periods](#cool-or-quiet-periods)
  * [Environmental differences](#environmental-differences)
  * [Tools](#tools)
- [Required resources](#required-resources)
  * [Required resources - compute](#required-resources---compute)
  * [Required resources - other](#required-resources---other)
- [Security and access control](#security-and-access-control)
  * [Password and PII security](#password-and-pii-security)
  * [Ongoing security checks](#ongoing-security-checks)
- [System configuration](#system-configuration)
  * [Configuration management](#configuration-management)
  * [Secrets management](#secrets-management)
- [Monitoring and alerting](#monitoring-and-alerting)
  * [Log aggregation solution](#log-aggregation-solution)
  * [Log message format](#log-message-format)
  * [Events and error messages](#events-and-error-messages)
  * [Metrics](#metrics)
  * [Health checks](#health-checks)
    + [Health of dependencies](#health-of-dependencies)
    + [Health of service](#health-of-service)
- [Operational tasks](#operational-tasks)
  * [Deployment](#deployment)
  * [Routine and sanity checks](#routine-and-sanity-checks)
  * [Troubleshooting](#troubleshooting)
- [Maintenance tasks](#maintenance-tasks)
  * [Patching](#patching)
    + [Normal patch cycle](#normal-patch-cycle)
    + [Zero-day vulnerabilities](#zero-day-vulnerabilities)
  * [Daylight-saving time changes](#daylight-saving-time-changes)
  * [Data cleardown](#data-cleardown)
  * [Log rotation](#log-rotation)
- [Failover and Recovery procedures](#failover-and-recovery-procedures)
  * [Failover](#failover)
  * [Recovery](#recovery)
  * [Troubleshooting Failover and Recovery](#troubleshooting-failover-and-recovery)


## Description
The service is the front-end of the CSGO Empire betting website and is needed for the user interface and communication to the back-end, roulette betting, and match betting.

## Technologies

[Vue.js](https://vuejs.org/)

[NGINX](https://www.nginx.com/)

## Overview

**Service or system name:** Frontend

### Business overview

The site-frontend service provides the front-end of the CSGO Empire website and handles the graphical interface. When the user bets, it goes through the front-end to the back-end.

### Technical overview

The site-frontend service is a Vue.js website front-end with an Nginx server.

### Service Level Agreements (SLAs)

Contractual 99.9% service availability and service level agreement for monitoring and alerting in 15 minutes in case of an outage

### Service owner

The *CSGO Empire* team develops and runs this service.

### Contributing applications, daemons, services, middleware

It is a Vue.js Framework app with an Nginx server.

## System characteristics

If the frontend service is not working, users cannot see the website. They will get Cloudflare errors instead because Cloudflare proxies requests to the frontend service.

During maintenance, Cloudflare is showing a maintenance static HTML page to the users and only proxies requests to the frontend from a list of IP addresses.

### Hours of operation

#### Hours of operation - core features

_00:00-24:00 GMT+0_

#### Hours of operation - secondary features

_00:00-24:00 GMT+0_

### Data and processing flows

> How and where does data flow through the system? What controls or triggers data flows?

Frontend data consists of communication between front-end and back-end with sockets, as well as communication with the roulette and match-betting.

### Infrastructure and network design

The service works in a Docker Container. It is deployed as a Kubernetes Deployment with a Helm Chart on an AWS EKS Kubernetes Cluster. The site-frontend deployment has multiple Kubernetes Pods handling requests.

### Resilience, Fault Tolerance (FT) and High Availability (HA)

Resilience, fault tolerance, and high availability are provided by a deployment on a EKS Kubernetes cluster with multiple nodes in different availability zones.

### Throttling and partial shutdown

> How can the system be throttled or partially shut down e.g. to avoid flooding other dependent systems? Can the throughput be limited to (say) 100 requests per second? etc. What kind of connection back-off schemes are in place?

#### Throttling and partial shutdown - external requests

_(e.g. Commercial API gateway allows throttling control)_

There is a load balancer that is proxied by Cloudflare. Cloudflare can limit the number of requests in case of a denial of service attack.

#### Throttling and partial shutdown - internal components

_(e.g. Exponential backoff on all HTTP-based services + `/health` healthcheck endpoints on all services)_

### Expected traffic and load

> Details of the expected throughput/traffic: call volumes, peak periods, quiet periods. What factors drive the load: bookings, page views, number of items in Basket, etc.

_(e.g. Max: 1000 requests per second with 400 concurrent users - Friday @ 16:00 to Sunday @ 18:00, driven by likelihood of barbecue activity in the neighborhood)_

#### Hot or peak periods

Most customers are from Europe so hot periods are between 19:00 and 01:00 Central European Time.

#### Warm periods

_

#### Cool or quiet periods

Most customers are from Europe so cool periods are between 06:00 and 08:00 Central European Time.

### Environmental differences

Environment variables are set from a config map and are different for each namespace and cluster. Because it is a user-facing Vue.js JavaScript app, all code is visible to end users and there are no secrets. Config maps are compiled in the code so Kubernetes pods do not mount any volumes.

### Tools

> What tools are available to help operate the system?

_(e.g. Use the `queue-cleardown.sh` script to safely cleardown the processing queue nightly)_

## Required resources

> What compute, storage, database, metrics, logging, and scaling resources are needed? What are the minimum and expected maximum sizes (in CPU cores, RAM, GB disk space, GBit/sec, etc.)?

### Required resources - compute

```
    Max:
      cpu:     1
    Min:
      cpu:     200m
      memory:  192Mi
```

### Required resources - other

_(e.g. Min: 10 encryption requests per node per minute. Max: around 100 encryption requests per node per minute)_


## Security and access control

### Password and PII security

> What kind of security is in place for passwords and Personally Identifiable Information (PII)? Are the passwords hashed with a strong hash function and salted?

_(e.g. Passwords are hashed with a 10-character salt and SHA265)_

### Ongoing security checks

> How will the system be monitored for security issues?

_(e.g. The ABC tool scans for reported CVE issues and reports via the ABC dashboard)_

## System configuration

### Configuration management

Managed by a Helm Chart for deployment and Dockerfile for the container/application setup.

### Secrets management

Because it is a user-facing Vue.js JavaScript app, all code is visible to end users and there are no secrets.

## Monitoring and alerting

### Log aggregation solution

Logs are streamed to Cloudwatch and Cloudwatch insights is used for searching.

### Log message format

JSON is used.

### Events and error messages

> What significant events, state transitions and error events may be logged?

_(e.g. IDs 1000-1999: Database events; IDs 2000-2999: message bus events; IDs 3000-3999: user-initiated action events; ...)_

### Metrics

CPU + memory + number of restarts + network

### Health checks

Health checks via AWS container insights

#### Health of dependencies

_(e.g. Use `/health` HTTP endpoint for internal components that expose it. Other systems and external endpoints: typically HTTP 200 but some synthetic checks for some services)_

#### Health of service

`/index.html`

## Operational tasks

### Deployment

> Service is deployed via Jenkins automated pipeline. Rollback is done from the Zetta team directly on the cluster.

### Routine and sanity checks

> What kind of checks need to happen on a regular basis?

_(e.g. All `/health` endpoints should be checked every 60secs plus the synthetic transaction checks run every 5 mins via Pingdom)_

### Troubleshooting

> How should troubleshooting happen? What tools are available?

_(e.g. Use a combination of the `/health` endpoint checks and the `abc-*.sh` scripts for diagnosing typical problems)_

## Maintenance tasks

### Patching

#### Normal patch cycle

Patches are deployed with a hotfix merge request to the master branch.

#### Zero-day vulnerabilities

_(e.g. Use the early-warning notifications from UpGuard plus deployment via Jenkins and Capistrano)_

OWASP security scanning of images is performed on the Jenkins server prior to deployment to prevent vulnerabilities.

### Daylight-saving time changes

> Is the software affected by daylight-saving time changes (both client and server)?

_(e.g. Server clocks all set to UTC+0. All date/time data converted to UTC with offset before processing)_

### Data cleardown

> Which data needs to be cleared down? How often? Which tools or scripts control cleardown? 

_(e.g. Use `abc-cleardown.ps1` run nightly to clear down the document cache)_
 
### Log rotation

Logs are streamed to Cloudwatch periodically, where we have log retention periond.

## Failover and Recovery procedures

> What needs to happen when parts of the system are failed over to standby systems? What needs to during recovery? 

### Failover

Scale down the deployment to 0 pods and set maintenance on with a Slack command.

### Recovery

If needed, rollback the Helm Chart deployment to a previous release. Then scale up the deployment and set maintenance to off with a Slack command.

### Troubleshooting Failover and Recovery

> What tools or scripts are available to troubleshoot failover and recovery operations?

_(e.g. Start with running `SELECT state__desc FROM sys.database__mirroring__endpoints` on the PRIMARY node and then use the scripts in the *db-failover* Git repo)_

