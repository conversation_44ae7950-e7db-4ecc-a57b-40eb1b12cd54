VITE_SOCKET_SERVER_PATH=/s
VITE_ZEN_TESTING=true

# Maximum idle time (in milliseconds) before the app auto-reloads itself to clean up leaked memory
# and load a potentially updated version of the front-end.
# Defaults to 21600000 ms (6 hours) if not specified
VITE_IDLE_RELOAD_TIMEOUT=21600000

# Enable intercom widget, these are intercom test env settings
# VITE_INTERCOM_API_URL=https://oi469peg.intercom-messenger.com
# VITE_INTERCOM_APP_ID=oi469peg

# Specify wether to use eslint vite plugin
VITE_USE_ESLINT_PLUGIN=true

VITE_ZEN_TESTING=true
