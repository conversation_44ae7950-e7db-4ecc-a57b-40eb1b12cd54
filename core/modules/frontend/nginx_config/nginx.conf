pid   /var/run/nginx/nginx.pid;
worker_processes  auto;
worker_rlimit_nofile 8192;

events {
    multi_accept on;
    worker_connections  4096;
}

http {
    charset utf-8;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    log_not_found off;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;

    # Disable nginx signature
    server_tokens off;

    #MIME
    include       mime.types;
    default_type  application/octet-stream;

    # logging
    log_format  main  '$http_x_real_ip - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" "$host" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$http_x_bot_score" "$http_x_env_class"';

    access_log  /dev/stdout  main;
    error_log   /dev/stdout  warn;

    # limits
    #limit_req_log_level warn;
    #limit_req_zone $binary_remote_addr zone=login:10m rate=10r/m;

    # SSL
    ssl_session_timeout 1d;
    #ssl_session_timeout 1h;
    ssl_session_cache shared:SSL:50m;
    #ssl_session_cache shared:SSL:1m;
    ssl_session_tickets off;
    #tweaks
    #ssl_buffer_size 16k;

    # modern configuration
    ssl_protocols TLSv1.2;
    ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256;
    ssl_prefer_server_ciphers on;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ************* ******* valid=60s;
    resolver_timeout 2s;

    #gzip  on;

    include /etc/nginx/conf.d/*.conf;
}
