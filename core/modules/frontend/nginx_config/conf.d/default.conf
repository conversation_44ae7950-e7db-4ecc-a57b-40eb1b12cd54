map $http_host $xframe {
    default "DENY";
    # if $http_host contains the word uat, staging or dev
    # this is needed for storybook as it use iframe
    "~(uat|staging|dev)\.(csgoempire|duel)\.com$" "SAMEORIGIN";
}

map $cookie_env_class $env_class {
    "" "env_class=change-this;Max-Age=300;Path=/";
    default "";
}

server {

  # . files
  location ~ /\.(?!well-known) {
          deny all;
  }

  # gzip
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss application/atimage/svg+xml;

  listen 8080;
  access_log  /dev/stdout  main;
  error_log   /dev/stdout  warn;
  root /var/www/httpdocs;
  index index.html;

  location / {
    try_files $uri $uri/index.html /index.html;

    # see https://stackoverflow.com/a/58200058
    # will only be set if $cookie_env_class is empty
    add_header Set-Cookie $env_class;

    # add header for caching
    add_header Cache-Control "no-cache";
    # add hostname headers
    add_header X-Backend-Server $hostname always;
    # security headers
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    #add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Referrer-Policy 'strict-origin-when-cross-origin' always;
    #add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'" always;
    add_header Content-Security-Policy upgrade-insecure-requests;
    add_header X-Frame-Options $xframe always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    gzip off;
  }

  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff2|ttf|mp3)$ {
    etag off;
    access_log off;
    expires 30d;
    add_header Cache-Control "public";

    # add hostname headers
    add_header X-Backend-Server $hostname always;
    # security headers
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    #add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Referrer-Policy 'strict-origin-when-cross-origin' always;
    #add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'" always;
    add_header Content-Security-Policy upgrade-insecure-requests;
    add_header X-Frame-Options $xframe always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
  }
}
