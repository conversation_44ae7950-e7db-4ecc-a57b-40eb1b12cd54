import { toBoolean } from '../src/helpers';
import {
    getCoinflipRoundResult,
    getHashForValue,
    getRouletteRollResult,
    generateRoulettePrivateSeed,
} from '../src/lib/provably-fair';

describe('Helpers', () => {
    describe('toBoolean()', () => {
        const falseValues = ['f', 'false', 'no', '0', '000'];
        it.each(falseValues)('should be false for %s', (value) => {
            expect(toBoolean(value)).toBe(false);
        });

        it('should cast if not a falsy string', () => {
            expect(toBoolean(1)).toBe(true);
            expect(toBoolean(0)).toBe(false);
            expect(toBoolean('')).toBe(false);
        });
    });
});

describe('ProvablyFair', () => {
    describe('getCoinflipRoundResult()', () => {
        it('should return the expected result for a specific round and block hash', () => {
            const round = {
                id: 1,
                private_seed: 'test',
            };
            const winningSide = getCoinflipRoundResult(round, 'test hash');
            expect(winningSide).toBe(1);
        });
    });

    describe('getHashForValue()', () => {
        it('should return the correct hash for a specific value', () => {
            const seed = 'test';
            const hash = getHashForValue(seed);
            expect(hash).toBe('9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08');
        });
    });

    describe('getHashForValue() with hmac', () => {
        it('should return the correct hash for a specific value', () => {
            const hash = getHashForValue('3228232306-109175', '98353e2e65b71c5b56e1419220c071f7408768699cd7884fdf3d3c9c5a58d494');
            expect(hash).toBe('923c943cf3e2decb14cde321acb1a262bd0c497fab0f524591a177ccb6f77cba');
        });
    });

    describe('getRouletteRollResult()', () => {
        it('should return the expected result for a specific hash', () => {
            const hash = '923c943cf3e2decb14cde321acb1a262bd0c497fab0f524591a177ccb6f77cba';
            const result = getRouletteRollResult(hash);
            expect(result).toBe(8);
        });
    });

    describe('generateRoulettePrivateSeed()', () => {
        it('should return a non-empty result', async () => {
            const seed = await generateRoulettePrivateSeed();
            expect(seed).toBeTruthy();
        });
    });
});
