IS_LOCAL_ENVIRONMENT=1
ENABLE_EVENT_LOOP_MONITORING=1
DB_HOST=${APP_NAME:-csgoempire}-database
DB_HOST_REPLICA=database-replica
DB_USER=${APP_NAME:-csgoempire}
DB_HOST_READONLY_USER=${APP_NAME:-csgoempire}-read
DB_PASSWORD=${APP_NAME:-csgoempire}
DB_PORT=3306
DB_DATABASE=${APP_NAME:-csgoempire}
DB_CONNECTION_LIMIT=3500
REDIS_HOST=${APP_NAME:-csgoempire}-redis
REDIS_PORT=6379
REDIS_HOST_SOCKET=${APP_NAME:-csgoempire}-redis-socket
REDIS_PORT_SOCKET=6379
INTERNAL_API_TOKEN=random-token-for-internal-socket-api-requests
NODE_COUNT=1
DISABLE_STANDBY_MASTER=1
MASTER_ENABLED=true
SLAVE_ENABLED=true
CHAT_ENABLED=true
ROULETTE_ENABLED=true
COINFLIP_ENABLED=true
NOTIFICATIONS_ENABLED=true
MATCHBETTING_ENABLED=true
CASEBATTLE_ENABLED=true
GAMEINTEGRATION_ENABLED=true
TRADE_ENABLED=true
LOAD_TEST_SECURITY_TOKEN=local-testing-security-token
