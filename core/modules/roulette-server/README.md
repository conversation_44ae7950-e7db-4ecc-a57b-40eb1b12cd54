# csgoempire socket-server

# General
Handle realtime backend services for skincras, roulette and chat.

## Dev environment
* Install docker
* Copy `.env.example` and update required fields
* Start: `docker compose up`

## Run tests
Run `npm run test`

If you want to run via docker and same way as we run tests in the pipeline you can do this:

```
docker build -f Dockerfile -t roulette-test .
docker run -it --entrypoint ./scripts/test.sh roulette-test
```


## Run production
* Build: `npm run build`
* Start: `npm run start`
* Shorthand: `npm run build-and-start`

This will start app with nodemon which automatically try to restart app if it crash. If you want to run without nodemon, you will need to start with `node build/socket.js`.

## Logging
Logging is managed by [debug](https://www.npmjs.com/package/debug) package with prefix `socket-server:LOG_LEVEL`. So for logging, `DEBUG` environment variable needs to set with correct prefix.

Examples:

Log all levels:  
`DEBUG=socket-server:*`

Log only errors:  
`DEBUG=socket-server:ERROR:*`

Log only errors and warnings:  
`DEBUG=socket-server:ERROR:*,socket-server:WARN:*`

# Components

## Roulette and chat
Enable by env var: `CHAT_ROULETTE_ENABLED=true`

### Chat logs
Dump chat logs and delete history by command:

`node tools/chat-logs.js`

If you want only dump (not delete) use command

`node tools/chat-logs.js --dump-only`

# Load testing with artillary

See [Load Test Simulations](https://gitlab.com/csgoempire/load-test-simulations/) project.
