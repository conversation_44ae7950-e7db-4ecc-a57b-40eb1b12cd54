{"name": "socket-server", "version": "1.0.0", "description": "Socket server for CSGOEmpire", "main": "build/index.js", "jest": {"testRunner": "jest-jasmine2", "testTimeout": 20000}, "scripts": {"lint": "eslint src", "jest": "jest", "test": "npm run lint && npm run jest", "build": "npx babel src -d build --source-maps inline --copy-files", "start": "node build/index.js", "build-and-start": "npm run build && npm run start", "watch": "(npm run build -- --watch 2>&1 &); sleep 3; npm run start"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "UNLICENSED", "engine": {"node": ">=18.0.0 <19.0.0", "npm": ">=9.0.0"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-export-default-from": "^7.7.4", "@babel/plugin-proposal-export-namespace-from": "^7.5.2", "@babel/plugin-proposal-numeric-separator": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.6.0", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/preset-env": "^7.6.3", "@babel/runtime": "^7.6.3", "babel-eslint": "^10.1.0", "babel-plugin-source-map-support": "^2.1.1", "chokidar": "^3.5.2", "eslint": "^6.8.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^23.18.0", "jest": "^27.5.1", "socket.io-client": "^4.7.5", "source-map-support": "^0.5.16"}, "dependencies": {"@socket.io/redis-adapter": "^8.3.0", "@substreams/core": "^0.16.0", "@substreams/manifest": "^0.15.0", "@substreams/node": "^0.6.2", "0x": "^4.9.1", "agentkeepalive": "^4.1.0", "async": "^3.1.1", "aws-sdk": "^2.1692.0", "bignumber.js": "^9.1.2", "blocked-at": "^1.2.0", "chalk": "^2.4.2", "commander": "^3.0.0", "debug": "^4.1.1", "dotenv": "^8.1.0", "fs-extra": "^8.1.0", "ioredis": "^4.14.0", "ioredis-mock": "^7.4.0", "js-levenshtein": "^1.1.6", "jsonwebtoken": "^9.0.2", "knex": "^0.20.15", "latinize": "^0.4.0", "lodash": "^4.17.20", "moment": "^2.24.0", "moment-timezone": "^0.5.26", "mysql2": "^2.3.3", "newrelic": "^11.15.0", "nodemon": "^1.19.1", "objection": "^2.2.18", "objection-db-errors": "^1.1.2", "objection-visibility": "^1.1.0", "random-js": "^2.1.0", "rate-limiter-flexible": "^5.0.3", "redlock": "^4.0.0", "request": "^2.88.0", "request-promise": "^4.2.4", "socket.io": "^4.7.5", "uid2": "0.0.3"}}