version: '3.8'

# defaults to cs<PERSON><PERSON><PERSON>, override with COMPOSE_PROJECT_NAME
name: csgoempire

networks:
  app:
    name: ${CSGOEMPIRE_DOCKER_NETWORK_NAME:-${APP_NAME:-csgoempire}}

services:
  socket-server:
    container_name: ${APP_NAME:-csgoempire}-socket-server
    image: ${APP_NAME:-csgoempire}/socket/server
    build: .
    volumes:
      - ./src:/app/src:cached
      - ./logs:/app/logs:cached
      - ./test.js:/app/test.js:cached
    entrypoint: "./entrypoint.sh"
    environment:
      DEBUG: socket-server:*,knex:*
      FORCE_COLOR: 1
      FIRST_BACKEND_PORT: 7001
      APP_NAME: "${APP_NAME:-csgoempire}"
    # these are needed for inspecting the code with chrome dev tools, uncomment if needed
    # ports:
    #     - "9230:9230"
    #     - "9231:9231"
    #     - "9232:9232"
    #     - "9233:9233"
    #     - "9234:9234"
    #     - "9235:9235"
    #     - "9236:9236"
    #     - "9237:9237"
    env_file:
      - dev.env
      - dev.${APP_NAME:-csgoempire}.env
      - .env
    networks:
      app:
        aliases:
          - socket-server

  trade-socket-server:
    profiles: [csgoempire]
    container_name: ${APP_NAME:-csgoempire}-trade-socket-server
    image: ${APP_NAME:-csgoempire}/socket/server
    build: .
    volumes:
      - ./src:/app/src:cached
      - ./.babelrc:/app/.babelrc:cached
      - ./logs:/app/logs:cached
      - ./test.js:/app/test.js:cached
    entrypoint: "./entrypoint.sh"
    environment:
      DEBUG: socket-server:*,knex:*
      FORCE_COLOR: 1
      FIRST_BACKEND_PORT: 7101
      MASTER_ENABLED: "false"
      SLAVE_ENABLED: "true"
      CHAT_ENABLED: "false"
      ROULETTE_ENABLED: "false"
      COINFLIP_ENABLED: "false"
      NOTIFICATIONS_ENABLED: "false"
      MATCHBETTING_ENABLED: "false"
      CASEBATTLE_ENABLED: "false"
      GAMEINTEGRATION_ENABLED: "false"
      TRADE_ENABLED: "true"
      APP_NAME: "${APP_NAME:-csgoempire}"
    # these are needed for inspecting the code with chrome dev tools, uncomment if needed
    # ports:
    #     - "9330:9330"
    #     - "9331:9331"
    #     - "9332:9332"
    #     - "9333:9333"
    #     - "9334:9334"
    #     - "9335:9335"
    #     - "9336:9336"
    #     - "9337:9337"
    env_file:
      - dev.env
      - dev.trade.env
      - .env
    networks:
      app:
        aliases:
          - trade-socket-server

  proxy:
    container_name: ${APP_NAME:-csgoempire}-socket-proxy
    image: ${APP_NAME:-csgoempire}/socket/proxy
    build:
      context: .
      dockerfile: Dockerfile.proxy
    volumes:
        - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:cached
    ports:
      - "${EXPOSED_SOCKET_PROXY_PORT:-7000}:7000"
      - "${EXPOSED_TRADE_SOCKET_PROXY_PORT:-7100}:7100"
    networks:
      app:
        aliases:
          - socket-proxy
          - trade-socket-proxy
