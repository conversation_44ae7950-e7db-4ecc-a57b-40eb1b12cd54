# Base image
FROM ubuntu:22.04 AS base
ENV DEBIAN_FRONTEND=noninteractive

# Upgrade repos and install packages
RUN apt-get -qq -o=Dpkg::Use-Pty=0 update \
    && apt-get -qq -o=Dpkg::Use-Pty=0 --assume-yes install \
    bash \
    git  \
    dos2unix \
    software-properties-common

# Add service specific packages
FROM base AS packages
USER root

# Upgrade repos and install packages
RUN apt-get -qq -o=Dpkg::Use-Pty=0 update && apt-get -qq -o=Dpkg::Use-Pty=0 -y upgrade \
    && apt-get -qq -o=Dpkg::Use-Pty=0 --assume-yes install \
    curl \
    dirmngr \
    apt-transport-https \
    lsb-release \
    ca-certificates \
    jq \
    gettext-base

# Install nodejs
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs

# Service image
FROM packages AS csgoempire-roulette-server

# Create non-root user
RUN adduser appuser --disabled-login --gecos ""  --home "/app"  --no-create-home \
    && addgroup appgroup && adduser appuser appgroup \
    && mkdir /app && chown appuser:appgroup /app

# Create directory for New Relic logs
RUN mkdir -p /var/log/newrelic && chown -R appuser:appgroup /var/log/newrelic

# Copy newrelic config file
COPY --chown=appuser:appgroup newrelic.js .

# Change to working dir
WORKDIR /app
USER appuser

# Copy package files code
COPY --chown=appuser:appgroup package.json .
COPY --chown=appuser:appgroup package-lock.json .

# Copy socket conf
COPY ./60-socket.conf /etc/sysctl.d

# install packages
RUN npm ci --no-color --quiet

# Copy source code
COPY --chown=appuser:appgroup . .

# build the application
RUN npm run build

# Export Port
ENV PORT=7001

RUN find . -type f -not -path "./node_modules/*" -print0 | xargs -0 dos2unix

# default entrypoint is for cluster
ENTRYPOINT ["node", "build/socket.js"]
