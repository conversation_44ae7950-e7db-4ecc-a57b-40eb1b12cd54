#!/bin/bash

file_location=$1
kms_key=$2

display_usage() {
  echo "Usage: $0 <file-with-secrets>"
  exit 1
}

if [  $# -le 0 ]; then
  display_usage
fi

location=header

while IFS= read -r line
do
  if [[ $line == \#* ]]
  then
    continue
  fi

  if [ "$location" = header ]
  then
    if [ "$line" = data: ]
    then
      location=data
    fi
    echo "$line"
    continue
  fi

  variable=$(echo "$line" | cut -f 1 -d :)

  if [ -z "$variable" ]
  then
    continue
  fi

  value=$(echo "$line" | cut -f 2- -d :)
  # remove leading whitespace characters
  value="${value#"${value%%[![:space:]]*}"}"
  # remove trailing whitespace characters
  value="${value%"${value##*[![:space:]]}"}"
  if [ -z "$value" ]
  then
    continue
  fi

  if type shush >/dev/null 2>&1
  then
    encoded_value=$(echo -n "$value" | base64 -w 0)
    echo "$variable: $encoded_value"
  else
    echo "Base64 is not installed."
    break
  fi
done < $file_location
