# Secrets

To decrypt an encrypted secret, you have to set up the AWS command line interface (CLI) for CSGO Empire, install Shush via https://github.com/realestate-com-au/shush, add them to your PATH, and execute the following command in your POSIX compatible terminal (bash, zsh, etc.):

`secrets/feed-encrypted-secrets-to-shush-output-secrets.sh <file-with-encrypted-secrets>.yaml > <file-with-secrets>.yaml`

To generate an encrypted secret, you have to set up the AWS command line interface (CLI) for CSGO Empire, install Shush via https://github.com/realestate-com-au/shush, add them to your PATH, and execute the following command in your POSIX compatible terminal (bash, zsh, etc.):

`secrets/feed-secrets-to-shush-output-secrets.sh <file-with-secrets>.yaml arn:aws:kms:us-east-2:366897636402:key/b0799e50-ef01-4484-8374-6667a2282ad1 > <file-with-encrypted-secrets>.yaml`
