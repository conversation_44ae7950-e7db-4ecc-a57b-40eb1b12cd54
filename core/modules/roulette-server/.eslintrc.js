module.exports = {
    parser: 'babel-eslint',
    env: {
        es6: true,
        node: true,
        "es2020": true,
        'jest/globals': true
    },
    extends: [
        'airbnb-base',
    ],
    plugins: ['jest'],
    rules: {
        'indent': ['error', 4, { 'SwitchCase': 1 }],
        'max-len': [
            'error', {
                'code': 150,
                'ignoreStrings': true,
                'ignoreTemplateLiterals': true,
                'ignoreComments': true,
            }],
        'linebreak-style': 0,
        'arrow-parens': [2, 'as-needed', { 'requireForBlockBody': true }],
        'no-plusplus': ['error', { 'allowForLoopAfterthoughts': true }],
    },
    parserOptions: {
        ecmaFeatures: {
            classes: true,
        },
    },
};
