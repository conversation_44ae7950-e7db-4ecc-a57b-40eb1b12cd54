{"plugins": ["@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-transform-runtime", "@babel/plugin-proposal-optional-chaining", "@babel/plugin-proposal-export-default-from", "@babel/plugin-proposal-numeric-separator", "source-map-support"], "presets": [["@babel/preset-env", {"exclude": ["@babel/plugin-transform-dynamic-import"], "targets": {"node": true}}]]}