apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-trade-server-configs
  namespace: default
data:
  DEBUG: "socket-server:ERROR:*,socket-server:WARN:*,socket-server:INFO:*,socket-server:DEBUG:*"
  MIN_BET: "0"
  REDIS_HOST: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST }}'
  REDIS_PORT: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT }}'
  REDIS_HOST_SOCKET: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST_SOCKET }}'
  REDIS_PORT_SOCKET: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT_SOCKET }}'
  REDIS_HOST_SOCKET_READONLY: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST_SOCKET_READONLY }}'
  REDIS_PORT_SOCKET_READONLY: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT_SOCKET_READONLY }}'
  ENABLE_EVENT_LOOP_MONITORING: "true"
  MASTER_ENABLED: "false"
  SLAVE_ENABLED: "true"
  SERVICE_ENABLED: "trade"
  NOTIFICATION_NAMESPACE: "trade"
  SOCKET_REDIS_KEY: "empire.trade-socket-server"
  NEW_RELIC_ENABLED: "true"
  NOTIFICATION_CHANNEL: '{{ required "Value is missing" .Values.CentralizedConfigs.NOTIFICATION_CHANNEL }}'
