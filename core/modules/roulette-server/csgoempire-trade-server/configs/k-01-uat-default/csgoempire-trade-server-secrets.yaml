apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-trade-server-secrets
    namespace: default
type: Opaque
stringData:
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:s15eiDrH4bCjRILq/ewmLXRZIbQ=,iv:1+qfGjKxPyW8ratGwBvwSZlStfZU3dKSY2fkf4RnQ1g=,tag:KaiXYz5hHM7WUkP48xyJGQ==,type:str]
    SOCKET_JWT_SECRET: ENC[AES256_GCM,data:bET7X5kNOxX5mt06C12aDp3F5yjMSYYSOaP90NWqEvLBw0Hu2rnzMwuJJwY=,iv:Ui4f1gPXMVWbRTDphBkV7h6xK/SbdqUPOZKM+lkisu0=,tag:OP/e01qTOJ6poL9nsXrYgQ==,type:str]
    NEW_RELIC_LICENSE: ENC[AES256_GCM,data:GIeosTh5C6YIuxIAuVzY2EYAqwgL6Ej7UpuaTpeaOsK+8cH2OMwqlZibgdBpYH8G/uCwntEe3QCOcepBku4D3HjLFuS+ZV05wlGvDmQ=,iv:wKhyq7wdFWxzHHd3ZHkBmGZ2qQwW7Sb5+18IiwzijMU=,tag:2J9pdNCgIqESLWnrgwqCBg==,type:str]
    SLACK_OAUTH_TOKEN: ENC[AES256_GCM,data:tWs1zOO2Muwd/Ta6CcGdYZ2oG+zC8nhX+ZkuqJPuJLhAzsooRXBuwHrXFUPX5+exEEDdK1adFcn5k2oPbSrs4P5bLKvq5EWDr4CPTpUz,iv:2761NeRWBArdXNlu0WRRJoS0wSKLTmFcdxgTiCaV2gE=,tag:6kwPV2QtX1aY5qdszEEZ6g==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/106cc3d0-52ce-4fff-8ce2-d5de6b4b495b
          created_at: "2023-11-24T15:01:01Z"
          enc: AQICAHgSoJfeQ7p7KnFbDOWNBH4uL/4umzn0G6+2tUTrQDfSXgHzJobAfBLQoM5EgEsGuvF6AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMWXr316aoi5La1Xy4AgEQgDu1erW39bISTgGDtKt6fkNJ4RbKwTlQogXUBIfZ5Wsj9eSImv96JDoECMtQsCwXgf0cOXMqZxaSum0JlQ==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-04-10T08:31:28Z"
    mac: ENC[AES256_GCM,data:QIOc9jrVkdbi2/IZCyldzkZii+4ZATiRvGuuFwJBYT/Zz3ixyxJm1mHtYn7tYooV0pZQSK4JSwKPsj4FdG69l5T2b4Lzno1iURjY82JNhk8Ak+ZOS2bgGuoMTaQyJy5OVtLrpaMFgF/wWabOOaH361O6IN2fB/bii3JKKUCSdg4=,iv:Zh5xxPEQn2+V5HfMj2cpy6IsxA4KnR9QzLUE+dCT2rQ=,tag:nbmZDdmEwsYTgRrk0RChYg==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.9.0
