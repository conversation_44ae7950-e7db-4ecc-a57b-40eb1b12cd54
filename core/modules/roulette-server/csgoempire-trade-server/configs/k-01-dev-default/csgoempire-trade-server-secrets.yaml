apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-trade-server-secrets
    namespace: default
type: Opaque
stringData:
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:T0eCREKd6gEsXEYkUIlys/WzESY=,iv:Oh5xOARFWbgPgBJs9GPiWBgit454BEAtX48tS1gHhYw=,tag:oR6CzXNBdbioymXsraFA+g==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/042a6a43-65a2-44a3-abe7-9f17ff1b7677
          created_at: "2023-09-19T13:24:26Z"
          enc: AQICAHhCoM3M4b9kgj3WXOlXYGSpssDPTNBbrRl46kn/pVYd9wGr2oAYFqasUyhZIpYFiB/3AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMOuVwBw27JzGzMfWRAgEQgDue1OlMUrxcBclt1zObdIWCKyeb251BPgLUuMG9w+tR4HCH15qiR2A5bvu4txhisuf+CPtgN2MteT62mA==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-04-10T08:32:23Z"
    mac: ENC[AES256_GCM,data:H9hxILa8uVX6xa9DMme7uFW1+Shcbkw+Zg3zqyirnlmDlLcxjnP3TXwrX2eWdydeUOPc3feKTwaGUr9Zlo9p6RUUEeZ2l/ANws29WM97707HHhzp+3duo/eytb0+hyvdf0P9BoH1ma/0e6uJmoxYHXtUQx8fBu2rYUKCye9G/1k=,iv:/wvH1ZkD+qvm9j9YVA752fd+psXVOb4ztfjJPi/SQsQ=,tag:NkWKzWiVIoI87LDL1Au0jw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.9.0
