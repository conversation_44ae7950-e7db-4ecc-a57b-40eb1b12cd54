apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-trade-server-secrets
    namespace: default
type: Opaque
stringData:
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:VGBbYoB+l5SrtCkPYCbT9sB1SqA=,iv:EpC8UvmYK+q8BLrxBEBopy621QCNYzQLVCaEBI70/a4=,tag:XFYXCrDhLjLf5CppA+expA==,type:str]
    SOCKET_JWT_SECRET: ENC[AES256_GCM,data:4nQ4i+QqpD4IGFePyDeyZxPbvCsnUJOJvP5MXinUM5AWkg==,iv:KmElCw5iQmrjmL/xdlSjftkqe4qJQGoA+olonZwriDk=,tag:Z0zdF7A4mhq6BWnVHxMOxQ==,type:str]
    NEW_RELIC_LICENSE: ENC[AES256_GCM,data:NL40Oh+GSqMlmx8lDvuCTOEtXSwpgDLcXAlBwfvsyo1UIy01A4vhbYLAKIfVoecFxA0Je+r4Xtgb/8Yy/hY9QJq5iPELXHiVtzeyxsQ=,iv:y/z7WdE9eb0qVANnPBLq5RWuj4DvXHW5ZRRxJWyfUic=,tag:evcSBcXHMMaoR8rlh65/vA==,type:str]
    SLACK_OAUTH_TOKEN: ENC[AES256_GCM,data:fmSi436+hrBEN7EJMvx1K9C6xRPdvGYn4CDmvVy++6TiD/R3H6JL8Ss0TUu4tKYPlLcjnPqzF80GtT9QRJ5ab0hD9tex+hGRWHYLQVyl,iv:aFqfn8uJAO9X+YE6f4aOdjWVlYn4eg/ILxPbU+E+Gkc=,tag:NzlAFDX1EycY75s184uBbg==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/03266ac7-13ee-424f-96b8-dd4cbe41d2f8
          created_at: "2023-09-19T13:24:43Z"
          enc: AQICAHgmVjCyicfe8YqMEaVgyxFqQVY/nh5ybHaA2JtK4enosAEr3xbwo8EUA3qtrWeipJ86AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMgh2ST8zWEX1Q5dKnAgEQgDtnh0h4VEFC3admecxaai6T1LCp2qo5KXXDe6p/WK1kJCodvSbYfR0ZudDAGTdtr/Zq2nqE6qzhpgx5Ag==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-04-10T08:31:53Z"
    mac: ENC[AES256_GCM,data:RaRbrVBb5goN/ThyEXjw9unXQo0PXIll+ifS8t+BdYTQGwpbt1upXojbTYuNf4euMJpmFbad4BXHfXemUHQ9PIC8f5JBTgV1yQwfl8uhbMYsnWTaOh5ubydCGQjMvxHcbkuhitwXNsVAtd7mtrVpAjEpLikXLfnvT1nop7VNqLg=,iv:VfyxA/G6F3HX8q/pubxzTR0CKlnFvgMuGzQalaEGZLw=,tag:jyfH16o9YIKJvFvBYZjGLg==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.9.0
