apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-trade-server-configs
  namespace: default
data:
  NODE_OPTIONS: "--max_old_space_size=2048"
  DEBUG: "socket-server:ERROR:*,socket-server:WARN:*,socket-server:INFO:*"
  MIN_BET: "0"
  REDIS_HOST: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST }}'
  REDIS_PORT: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT }}'
  REDIS_HOST_SOCKET: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST_SOCKET }}'
  REDIS_PORT_SOCKET: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT_SOCKET }}'
  REDIS_HOST_SOCKET_READONLY: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST_SOCKET_READONLY }}'
  REDIS_PORT_SOCKET_READONLY: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT_SOCKET_READONLY }}'
  ENABLE_EVENT_LOOP_MONITORING: "false"
  MASTER_ENABLED: "false"
  SLAVE_ENABLED: "true"
  SERVICE_ENABLED: "trade"
  NOTIFICATION_NAMESPACE: "trade"
  SOCKET_REDIS_KEY: "empire.trade-socket-server"
  RATE_LIMITER_DISABLED: "true"
  RATE_LIMITER_IN_MEMORY_DISABLED: "false"
  RATE_LIMITER_PING_IN_MEMORY_DISABLED: "false"
  RATE_LIMITER_POINTS_PING: "10"
  RATE_LIMITER_DURATION_PING: "10"
  IDENTIFIER_CHECK_DISABLED: "false"
  JWT_SOCKET_TOKEN_DISABLED: "false"
  NEW_RELIC_ENABLED: "true"
  APP_NAME: '{{ required "Value is missing" .Values.CentralizedConfigs.APP_NAME }}'
