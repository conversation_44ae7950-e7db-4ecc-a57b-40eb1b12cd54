apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-trade-server-secrets
    namespace: default
type: Opaque
stringData:
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:aqMydYwgtd057IbBsDQ38TK0sas=,iv:58CramkmWRs1bJYqxkvzfEWM/pkNJukJ3HVZfhVIcpk=,tag:ymCxA3GC7t5VIyjnn6wGYg==,type:str]
    SOCKET_JWT_SECRET: ENC[AES256_GCM,data:XU1neyjlzCFgtkbgPKukuKKIOcPEc8SX9fDbCp4BHPHQbu8=,iv:dGEyiPPvFduua9omC6xlnzYyEyCOV4YJiJk0RzkelNM=,tag:KHTvLJNzMwP2hLMOdsuDdA==,type:str]
    NEW_RELIC_LICENSE: ENC[AES256_GCM,data:cpub5w33Rx7d37tgImGHEy0O8vd3/k4bwbjSN3FI99SupUXpy+1GTfaO4Ymqjm/xmpjx+7NUDgh/Ig67518wgzoo0JUyYNUuGLx/nNk=,iv:PG6u7DczNTKw3IP1rVDwIssx6JBgVWNm7+DqQ2us4qU=,tag:aCQZqefLbgPd8Qsm66IeJw==,type:str]
    SLACK_OAUTH_TOKEN: ENC[AES256_GCM,data:7U1hBQKqHDi3gTFUznoMfVv0M/P7PdsVHPRQrouZNaD/qNMeZqwpRPWKSyEUi5mD9CfEqHQwB9v8w7JzUWGcRu1oRI1u5Ps1tIhR8dxx,iv:Kxt0akcmCVXQcgM1Fw3PqVWQS4obLlTpZefhQDBNpOE=,tag:ShVcrTuvQYPHVvwWuhPlhw==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/6d139d49-31a8-4df6-af2d-911ba3806029
          created_at: "2023-09-19T13:24:36Z"
          enc: AQICAHjeW8/jhqmxODoAy9IWX/R+cwYAT2/DbsAVhbPusZX8NAF2fy4hjf/yYbnB1NErckUwAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMCaY5MKOWIS9FO2FfAgEQgDvD1nROQtbjzjMjbg2geenIUQypvC+16IRIRAqhO/821B+gnYhX44vBB47bncWJbQ+IIqHQq2nr0hJr/w==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-04-10T08:32:09Z"
    mac: ENC[AES256_GCM,data:e7JKi3y98Y7ra7dYDslridHN0mpw/S5pMa2cnRqJQipffrx/f3WVYC/5uoKu18sbg1AwE2nwnqxHlfwX6tSrOZ04bstJSMj2H3c8zEJZT/0VyCm1qEx7DNBGpSk/pT7GnJ779TclAqUNCsf9hGla9NJUp53jd6I2aucKE+Nv0aA=,iv:Jf46GRiWa54V9GiMKQ4h4t9obNiLTJMFDjBihutQi74=,tag:7LqwNSHuZMtbY7NbRoALXg==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.9.0
