/* eslint-disable */
'use strict'

/**
 * New Relic agent configuration.
 *
 * See lib/config/default.js in the agent distribution for a more complete
 * description of configuration variables and their potential values.
 */
exports.config = {
/**
   * Array of application names.
   */
    app_name: process.env.NEW_RELIC_APP_NAME,
    /**
   * Your New Relic license key.
   */
    agent_enabled: process.env.JEST_WORKER_ID ? false : (process.env.NEW_RELIC_ENABLED || false),
    license_key: process.env.NEW_RELIC_LICENSE,
    labels: process.env.NEW_RELIC_LABELS,
    logging: {
    /**
     * Level at which to log. 'trace' is most useful to New Relic when diagnosing
     * issues with the agent, 'info' and higher will impose the least overhead on
     * production applications.
     */
        level: 'error',
        filepath: process.env.JEST_WORKER_ID ? '/dev/null' : '/var/log/newrelic/nodejs_agent.log'
    },
    /**
   * When true, all request headers except for those listed in attributes.exclude
   * will be captured for all traces, unless otherwise specified in a destination's
   * attributes include/exclude lists.
   */
    allow_all_headers: true,
    attributes: {
    /**
     * Prefix of attributes to exclude from all destinations. Allows * as wildcard
     * at end.
     *
     * NOTE: If excluding headers, they must be in camelCase form to be filtered.
     *
     * @name NEW_RELIC_ATTRIBUTES_EXCLUDE
     */

        exclude: [
            'request.headers.cookie',
            'request.headers.authorization',
            'request.headers.proxyAuthorization',
            'request.headers.setCookie*',
            'request.headers.x*',
            'response.headers.cookie',
            'response.headers.authorization',
            'response.headers.proxyAuthorization',
            'response.headers.setCookie*',
            'response.headers.x*',
        ]
    },
    transaction_tracer: {
        enabled: true
    },
    transaction_events: {
        enabled: false
    },
    span_events: {
        enabled: false
    },
    application_logging: {
        forwarding: {
            log_level: 'error'
        }
    },
};
