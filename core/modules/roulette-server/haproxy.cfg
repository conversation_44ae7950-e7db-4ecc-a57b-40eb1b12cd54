defaults
  log global
  mode http
  option log-health-checks
  option httplog
  option http-server-close
  option dontlognull
  option redispatch
  option contstats
  retries 3
  backlog 10000
  timeout check            5s
  timeout client          35s
  timeout connect          5s
  timeout server          35s
  timeout tunnel        3600s
  timeout http-keep-alive  1s
  timeout http-request    15s
  timeout queue           30s
  timeout tarpit          60s
  default-server inter 3s rise 2 fall 3
  option forwardfor


resolvers docker_resolver
    nameserver dns 127.0.0.11:53

listen socket
  bind *:7000
  default_backend socket_nodes

listen trade-socket
  bind *:7100
  default_backend trade_nodes

backend socket_nodes
  option httpchk HEAD /ping
  http-check expect status 200
  cookie io prefix indirect nocache # using the `io` cookie set upon handshake
  server app01 socket-server:7001 check cookie app01 resolvers docker_resolver init-addr none
  server app02 socket-server:7002 check cookie app02 resolvers docker_resolver init-addr none
  server app03 socket-server:7003 check cookie app03 resolvers docker_resolver init-addr none

backend trade_nodes
  option httpchk HEAD /ping
  http-check expect status 200
  cookie io prefix indirect nocache # using the `io` cookie set upon handshake
  server app01 trade-socket-server:7101 check cookie app01 resolvers docker_resolver init-addr none
  server app02 trade-socket-server:7102 check cookie app02 resolvers docker_resolver init-addr none
  server app03 trade-socket-server:7103 check cookie app03 resolvers docker_resolver init-addr none
