creation_rules:
    - path_regex: k-01-p-default/.*
      kms: 'arn:aws:kms:us-east-2:404599132715:key/6d139d49-31a8-4df6-af2d-911ba3806029'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-staging-default/.*
      kms: 'arn:aws:kms:us-east-2:404599132715:key/03266ac7-13ee-424f-96b8-dd4cbe41d2f8'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-dev-default/.*
      kms: 'arn:aws:kms:us-east-2:404599132715:key/042a6a43-65a2-44a3-abe7-9f17ff1b7677'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-uat-default/.*
      kms: 'arn:aws:kms:us-east-2:404599132715:key/106cc3d0-52ce-4fff-8ce2-d5de6b4b495b'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-duel-dev-default/.*
      kms: 'arn:aws:kms:us-east-2:404599132715:key/f3da1b54-6df1-450f-8b30-260a02cb9b2c'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-duel-staging-default/.*
      kms: 'arn:aws:kms:us-east-2:404599132715:key/c56c823c-fdb2-441f-ac9d-050a377258a3'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-duel-uat-default/.*
      kms: 'arn:aws:kms:eu-west-1:404599132715:key/mrk-36e72924a7ec4161bcab202e6f4115d8'
      encrypted_regex: '^(data|stringData)$'

    - path_regex: k-01-duel-prod-default/.*
      kms: 'arn:aws:kms:eu-west-1:404599132715:key/mrk-9da20166b9a84c89a71a54c6b2fb88e1'
      encrypted_regex: '^(data|stringData)$'
