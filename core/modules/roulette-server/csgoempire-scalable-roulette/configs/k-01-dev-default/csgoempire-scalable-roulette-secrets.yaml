apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-scalable-roulette-secrets
    namespace: default
type: Opaque
stringData:
    DB_PASSWORD: ENC[AES256_GCM,data:vzIlGUb1QLqisbQginao1a7aGAN3GcKJlWx8jOaD7DQSDJR7w86NtpzJ82usxdH4LLa0QIYBCY+kyYaJww/qIK+A/whoJPfo,iv:iI12D43kM6Dyz3sGdo5lG7H/oADPZCb3FupxamRGtng=,tag:UVaYRHeunuOII/Z/EQzavA==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:O1jcgK4MrXjGcxWPJJl9hRkmU0Q=,iv:RiFyVE5uttQGsH/oDtihskYHk9gpnMd1htfPjGxKxMI=,tag:fo17jy7SadsImT8c7DVr+g==,type:str]
    LOAD_TEST_SECURITY_TOKEN: ENC[AES256_GCM,data:TDdjnyIWRccOBzRzjA==,iv:7RuMfE6m/+Rvd9wmUTi743fWwBzpj+f0RJJekSys1SM=,tag:FbImE5UPf8QF5vGtLBVsZA==,type:str]
    SOCKET_DATA_ENCRYPT_SECRET: ENC[AES256_GCM,data:lwISX922kL2n9m1l5EoRBRhyCYxzNPZrxVYavLnjzt6j7cUP3zVB/6a0jmBKAa07q9QhD4c9eTcKUfE/pSUFqyj61iB8conYiVF8Fd3284G4unTqSzyT,iv:CMBIdvvxfH9GTt2a4spkpzehe6jvMNTfaUXgQGdz18U=,tag:s3zcyyEo5TWZPL4dhWwoKg==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/042a6a43-65a2-44a3-abe7-9f17ff1b7677
          created_at: "2023-09-20T06:32:58Z"
          enc: AQICAHhCoM3M4b9kgj3WXOlXYGSpssDPTNBbrRl46kn/pVYd9wEl+rxhvW3qYXlgPLS3XLJYAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQML2DDAHGccowXlI4kAgEQgDskk3UfIjALoM8uydiklK+/eJp8CcQexFoFeHEfwrtBQ2owlQvfcliLLY6xK9wxdb8Ar8DBcVts90Rn+Q==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-16T13:47:40Z"
    mac: ENC[AES256_GCM,data:7FhnHi3FTBhKR35v2AzrvM7rtQnX3EEzZn3xZbyxbBKoCbgx3s+/h40POtTAmjEgDABxsd6c3C6cf5V8XqRCmqow1VGwMcWpJ4Mh+yHmejraKiG3UglYXo1aEpBbxNXJ8Pc6jgGGoaD5QRqPTOIqVSc8wOLs5mzQCtmj5RMITg4=,iv:YplFmW+CFBkRnGF8pIRxhZ9oRijtoEbVpnRTpBzRrAc=,tag:FpXgfpwQFDLltKhEu90vbw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.8.1
