apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-roulette-master-configs
data:
  DEBUG: "socket-server:ERROR:*,socket-server:WARN:*,socket-server:INFO:*"
  ROLL_TIMER: "15"
  ROULETTE_ANIMATION_LENGTH: "6"
  ROULETTE_END_PERIOD: "3"
  SERVICE_ENABLED: "roulette"
  MASTER_ENABLED: "true"
  SLAVE_ENABLED: "false"
  NEW_RELIC_ENABLED: "true"
  NODE_ENV: "production"
  USE_KMS: "true"
  KMS_KEY_ID: "arn:aws:kms:us-east-2:404599132715:key/mrk-6bc1803e20764950a3a82da91a15e36f"
