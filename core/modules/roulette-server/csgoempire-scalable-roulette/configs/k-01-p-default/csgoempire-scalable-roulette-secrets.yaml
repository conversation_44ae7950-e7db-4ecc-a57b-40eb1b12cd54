apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-scalable-roulette-secrets
type: Opaque
stringData:
    DB_PASSWORD: ENC[AES256_GCM,data:WMQDQV+Ew5cKl3AYgclASobdVVVctEWFWctyQI4TuECVN1+w22+aJJiRKZ/K70qvhFrjkygFOt3pRf13yNvuttxj5aQZ4/C2,iv:TdTi902/UyxT6Rl3CF5/Rymz93r9BCAlsTvdSvfgxyQ=,tag:Ed2p6QuQ+0Wky0IFAv9VdQ==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:+IsAnKIVhlNZJK8vB7jE6Ul0ACo=,iv:RsKYG0/tl6TeCZHNOfEsaW82hRhTcna4jB6n4//Et5o=,tag:YxcPZHz4fJq9cps+VEi5Yw==,type:str]
    DFUSE_API_KEY: ENC[AES256_GCM,data:gGPZQFEmRiD0861tv5mKHrE/cLApeR1OXZrSCVs2uOXlKwAy/wXSzSHwd+jAiZ8aFovYsTcA6eWlkXjggPKQ2e5pCRqBbYnZtoQ=,iv:I7RwPnMtuhtK3stqZoeyj85KkYnV+EH7XQd4pcQ0rRk=,tag:ouRlTh2A3omi7Of/rYVzBg==,type:str]
    SOCKET_JWT_SECRET: ENC[AES256_GCM,data:s3af8ibVI0HgdHQ+VXb8/BLtcuOBw2voVWTHr8J3jgKJCX0=,iv:agusy50+Yk4mzY9dex3zVfLxF9uNLKJPHipdlBzq0FE=,tag:0R6PuE4W1Dje/q83sCw0Ag==,type:str]
    NEW_RELIC_LICENSE: ENC[AES256_GCM,data:SfVRObIMRxajzNxOtEZIAmMvFqxK2N1vPG7QoeFVBs0nkphjctSmmX9Q8cjJxP4+3Vq8pc67IoswkdsvsooKujLbpXfb+85yYVWL5EI=,iv:pv/WIVqea1o6fJtyoscRHBdobECzWfr9IEOLFesSkH4=,tag:0qKWJ64HJOavEZjYfsQqgA==,type:str]
    SUBSTREAMS_API_KEY: ENC[AES256_GCM,data:bZcT+c3JDDYDUHSVtgK7/oK4/zK5ozFkpKF8PVQuvNcc902IzXkAf/IAZk7+qzg7,iv:OsoNc8rf5Rr/FwJ/4OAmjOtT9dSa54hSv3gjnLB0UbI=,tag:ZXve1hzqhn8znvsWvdSUSQ==,type:str]
    SLACK_OAUTH_TOKEN: ENC[AES256_GCM,data:OYPsGtJZR0lDDGkgT+i/Rv+bOw7ANS72PPxwUk6HhPZbLMCYW+fdviCyEerIaieKdPgeJ2PxVrEkfi4mSvU4UQWxKxSIuJKQi4QKolJF,iv:5A5LrVg0R1/BDgG6NaM13BNunK6ZNoLLYiASsYC6Cdc=,tag:jW3iXxA7LurbSr5yzrGECw==,type:str]
    SOCKET_DATA_ENCRYPT_SECRET: ENC[AES256_GCM,data:14q4cxH2xOzstPcOtxKybvfz4eOK2wYh5NM1sg6oYGHTdORpTMuMiI3FutARMHUCSCGxlUKfKummbYHRoR7hIf3gVy6GHyQ0UN8yutUJ6Q0tqIyzqkyg,iv:NxxHJD65+ld/ZClSNdb+FqA4Y4MSzkiMD4tf4X7AXE0=,tag:CVFY1M+LewU7G23UBHT1SA==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/6d139d49-31a8-4df6-af2d-911ba3806029
          created_at: "2023-09-20T12:55:23Z"
          enc: AQICAHjeW8/jhqmxODoAy9IWX/R+cwYAT2/DbsAVhbPusZX8NAHz/ciwGe8xdheJAHEXbRuYAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMZ+3SnjjPJ2+EDHCkAgEQgDt9VmLCM89ij55BFmalgTObCPNIPYtJWY/2Gv94zap+rrn7rdGS3pQMwkXdybQMAD865OerETmeFlbcZQ==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-16T13:49:48Z"
    mac: ENC[AES256_GCM,data:GACP10GUfgrv6/zGuJ9jgn7rUHdyfI/eThcw/IZVikr5n/wCGRxY79ewcndbobktyE8HEbmrN6Nbis0h+jFgMsSFLUARkLR1MWs65DAFc/33hlJNpiG9vRy2B5WO+3eUbg0jWSxWNc4m2LyCUaGJ346ry+GHaiJaBlwdi7E18Q0=,iv:39Ub26Y8YOeyORiCc+59cRaD82zNp9B1VRssO66CIXc=,tag:4sO1uYFlXiNGIF76YCt9hA==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.9.0
