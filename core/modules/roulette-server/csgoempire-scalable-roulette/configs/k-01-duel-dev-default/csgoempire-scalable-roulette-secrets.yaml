apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-scalable-roulette-secrets
    namespace: default
type: Opaque
stringData:
    DB_PASSWORD: ENC[AES256_GCM,data:Q6YUhfm8555vaJqtGIjOXlDxxnrgn7S/ph/k9+HVdjO8u4RDewcX3EYTtAGuS1mmn7YXPPcOpIOMMMdUMBRUM7N3Czf1dvb0,iv:I8rKUU3ocRPE0BOu1SGP8PHpZCLARzP6Z0WaE/a7doY=,tag:A6pJPssNFwdhRjkvPdvRkw==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:l9xLNlcqQQz1DO2YoX1Fp3iENaE=,iv:JN+W++uTcq+asVklD5zej0Bl5WOcdqVDSbGhWoBBaWQ=,tag:eUnFWO//4YkbdbEj+NxkjQ==,type:str]
    LOAD_TEST_SECURITY_TOKEN: ENC[AES256_GCM,data:1VdBv9NxtUU14hFvog==,iv:cvZTfBL18xFB6uVdRtRbivce8ucKJXy9PBOeZ9AVNx8=,tag:uY6CrWJQDfU7mbGp+jflLg==,type:str]
    SOCKET_DATA_ENCRYPT_SECRET: ENC[AES256_GCM,data:dgqvT1IjM3Ua7pH73vAYEnm/sx2EXzmacNVpv5Vn7XtMJsvT+5L5YD/VUTztWpuQRB/4yMxAc2SYrAw1cgHUZyVcev0ewZG505woZO6y+rs6C6hq90KW,iv:H74SHZYTJWMs18y0V28ikDD92yjfuP1awxPfow7uSdA=,tag:zsrjGrOIet4kwQxEnibJzw==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/f3da1b54-6df1-450f-8b30-260a02cb9b2c
          created_at: "2024-05-21T05:54:13Z"
          enc: AQICAHiJjn9kpQNLYpDeSjFp4jbggFzbZaVdzzI6PFv07GCe0gG+DfdKrK+f9yvj5oPl22bCAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMnvNz8O38n7X9bfJcAgEQgDvbGFTSf5JVq1irWm1tycLo+GdseArFZxV2LKMrMJFnXn1Vb3fKHv6h1Diq2S0Nwf/n4j+cJ6psoafRQA==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-16T13:48:11Z"
    mac: ENC[AES256_GCM,data:wVEXSjk2FxUzSSJpb+bYy71wT4tPFifoVNM54qai/tM/Dto3oG6u17tZBhML++Q+Oa1e8y0rdPwKY6G9xwFzq3ELF9gG8MOop6Y7U0GoAJw6vrmedWeEC1egp6iUHfXROK8AkDZ8ODkSMovCmcSPqRMKAbHy8Eb87XYZTUt1Ajg=,iv:Cwi+4e/nAeCfWXx3INi70O/1q8YgR71pTAqkdo589t0=,tag:63JaRqCI0KdUL2i0GHhPaw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
