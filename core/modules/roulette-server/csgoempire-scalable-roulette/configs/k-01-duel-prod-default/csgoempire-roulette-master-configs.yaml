apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-roulette-master-configs
data:
  DEBUG: "socket-server:ERROR:*,socket-server:WARN:*,socket-server:INFO:*"
  ROLL_TIMER: "15"
  ROULETTE_ANIMATION_LENGTH: "6"
  ROULETTE_END_PERIOD: "3"
  SERVICE_ENABLED: "roulette"
  MASTER_ENABLED: "true"
  SLAVE_ENABLED: "false"
  NEW_RELIC_ENABLED: "true"
  NODE_ENV: "production"
  KMS_KEY_ID: "arn:aws:kms:eu-west-1:404599132715:key/mrk-38d2fb608ec8473c84afe19bac46a94f"
  KMS_REGION: "eu-west-1"
