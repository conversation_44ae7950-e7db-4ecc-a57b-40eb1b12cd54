apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-scalable-roulette-configs
  namespace: default
data:
  DB_CONNECTION_LIMIT: "3500"
  DB_PORT: '{{ required "Value is missing" .Values.CentralizedConfigs.DB_PORT }}'
  MIN_BET: "0"
  REDIS_PORT: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT }}'
  REDIS_PORT_SOCKET: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_PORT_SOCKET }}'
  IS_LOCAL_ENVIRONMENT: "0"
  DISABLE_CHAT_LIMITS: "0"
  ENABLE_EVENT_LOOP_MONITORING: "true"
  TRADE_ENABLED: "false"
  REDIS_HOST: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST }}'
  REDIS_HOST_SOCKET: '{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST_SOCKET }}'
  DB_DATABASE: '{{ required "Value is missing" .Values.CentralizedConfigs.DB_DATABASE }}'
  DB_HOST: '{{ required "Value is missing" .Values.CentralizedConfigs.DB_HOST }}'
  DB_HOST_REPLICA: '{{ required "Value is missing" .Values.CentralizedConfigs.DB_HOST_REPLICA }}'
  DB_USER: '{{ required "Value is missing" .Values.CentralizedConfigs.DB_USER }}'
  INTERNAL_API_URL: http://csgoempire-roulette-backend/api/v2/socket
  NOTIFICATION_CHANNEL: '{{ required "Value is missing" .Values.CentralizedConfigs.NOTIFICATION_CHANNEL }}'
