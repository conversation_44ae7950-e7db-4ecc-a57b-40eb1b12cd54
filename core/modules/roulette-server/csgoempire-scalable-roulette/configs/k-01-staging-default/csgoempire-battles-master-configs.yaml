apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-battles-master-configs
data:
  DEBUG: "socket-server:ERROR:*,socket-server:WARN:*,socket-server:INFO:*"
  MASTER_ENABLED: "true"
  SLAVE_ENABLED: "false"
  INTERNAL_API_ACTIVE_BATTLES: "/case-battle/active"
  ROUND_DELAY: "6"
  SERVICE_ENABLED: "casebattle"
  NEW_RELIC_ENABLED: "true"
  NOTIFICATION_CHANNEL: '{{ required "Value is missing" .Values.CentralizedConfigs.NOTIFICATION_CHANNEL }}'
