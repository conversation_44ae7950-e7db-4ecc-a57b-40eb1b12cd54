apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-slave-rcc-configs
data:
  DEBUG: "socket-server:ERROR:*"
  ROLL_TIMER: "15"
  ROULETTE_ANIMATION_LENGTH: "6"
  ROULETTE_END_PERIOD: "3"
  CHAT_ENABLED: "true"
  ROULETTE_ENABLED: "true"
  COINFLIP_ENABLED: "true"
  NOTIFICATIONS_ENABLED: "true"
  MATCHBETTING_ENABLED: "false"
  MASTER_ENABLED: "false"
  SLAVE_ENABLED: "true"
  CASEBATTLE_ENABLED: "false"
  GAMEINTEGRATION_ENABLED: "true"
  NEW_RELIC_ENABLED: "true"
  USE_NATIVE_CURRENCIES : "true"
  NOTIFICATION_CHANNEL: '{{ required "Value is missing" .Values.CentralizedConfigs.NOTIFICATION_CHANNEL }}'
