apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-scalable-roulette-secrets
type: Opaque
stringData:
    DB_PASSWORD: ENC[AES256_GCM,data:o15dmDQu9jv3k4psAprA89Six8xHl1crGjb0gko+IhjZO/GcmwaEgxSm7htil9ccOG3rsa0P+FUYevgi8Zcs2S4Y+jJaQ3bf,iv:iq6EdyEcADy39xja3NTcOkNciniOqv4Ogkz8aSQYB7U=,tag:1OQ8VuCYGuRqccb0PShPOQ==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:9AwMCZfPj99UDIbrAToG6iTc5W4=,iv:bvCDUSlWiOwUtsKP5GFed3u9M+bn9tm+SR8O1f91Wok=,tag:JfPTMYRaz8GdjGhF6PYRmw==,type:str]
    LOAD_TEST_SECURITY_TOKEN: ENC[AES256_GCM,data:nmP5u0jqAgJwuJAVEg==,iv:nH19Jtazq582M0td6/3kFU1i/D6Pb5cHU/twsfLSrsg=,tag:5yyQwdVgrG69fZlKxkMnCQ==,type:str]
    SOCKET_JWT_SECRET: ENC[AES256_GCM,data:P943nJPSVliEQQ0lGP8H9frUCL+475Jm9RRk8hqt7OUvjLyVdvkusUZAJmhy4TPutnX30VfR7bLHqPSR5QYAvb2dANvTzlObBvutr0md,iv:yVERqItVmbqT5SXWMxjNtWsmaOZl/9w45aF1u6dk0uQ=,tag:7xQBwUHzeY82TWqK3BeKqQ==,type:str]
    NEW_RELIC_LICENSE: ENC[AES256_GCM,data:IL2TLy4t6PXi3mmWT8n9WVHqPN0VxI47v7cgjXSz21NLNkfFlhgvLio3nNwFzlTEikj93ZXSeJs+HSevaRtDE2w5GRN6m6ujail4/lo=,iv:OHEkV6NBMCy784i8521vsDat7oivNZI/X5lFQ+EmpPc=,tag:GozttDZgmHFNdJSNIMYTPA==,type:str]
    SUBSTREAMS_API_KEY: ENC[AES256_GCM,data:k/9Du6yQwD0mTPeY0DpPQO4tVfo399L9oZkKzBaxDHmK1S5mbrHhdlTtVIhSQlia,iv:abvU2y8haXYyzAkRao+mOa9X1RtvgCjKXS+2UBQ/dOc=,tag:6y9C4cVp15rgXxj72yH/fA==,type:str]
    SOCKET_DATA_ENCRYPT_SECRET: ENC[AES256_GCM,data:zSgqUzTWiXdrPv2HNjcZySenN5qdjbtacHsBcreeMjRieNxBe/cKyV6vx2uzTRZSNAiQbhNQQi1NBPwRDjhfUjAvYLLRQ0DaBIkzzoKDvkNgFywYIr7J,iv:5ZIlv8sMq2GQwaFET1yRiifJFWiva/bwwi6W6eGTuFk=,tag:fhLaOkYlZlSor8DS2gjbQw==,type:str]
    SLACK_OAUTH_TOKEN: ENC[AES256_GCM,data:sBRs5287eUW58j09qg3iFGjsD+IVPRSvvLWdt4UATwzDsPO/D9vOgLSWxg0rAQxpiWbbt+GkAqmPWxUZEFOWsx2hGCqcS2kpxwqwdzk6,iv:AV0nbmRjpngkn9v+RkqLxGlOHEQKCXcmD4ujCle9SUI=,tag:aho6CopN9EimOpddl3nb7w==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:eu-west-1:404599132715:key/mrk-36e72924a7ec4161bcab202e6f4115d8
          created_at: "2024-09-30T10:19:59Z"
          enc: AQICAHgtwVGCQJvbW7kGrQVL2zhTfnJlT28MWG2sMftw714pfwH+3ZrRPij6SLljcDJuoBKkAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMOWMHOz2LE7BLuAFuAgEQgDs4gg9KXDBZZf4Iw0J3mCXoFKSYkkbnVXQwCUL5CoYaD7Rnmm8JE/Sq6/OH+kW0lKYfKUr5mXi5IYgZBQ==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-03-05T09:55:23Z"
    mac: ENC[AES256_GCM,data:Fcc1orraCe/2DWXscDTkkMUrmx748rpgcAFZYFrCX/cotoG3yql7yv3poWeYg0JnOSs7qQ31dtaueW5/HuX/qMYwf51+GV56kpxsU8FlomqT/Cs/qALVro6FM1nJPz59VMYzUMV3Urw8XXAHqAZkEKhnFcHg0ZIYfkWc1/OW570=,iv:nSqKEggrA+RUHh6spPXR+zquyc8yFrOGzMwrluCGyMM=,tag:+pnhi/vTcd4Q+UecfRg22Q==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.9.0
