import SocketUser from '../../lib/user';

export default class CaseBattleUser extends SocketUser {
    constructor(socket, server) {
        super(socket, server);
        this.socket = socket;
        this.onStartSync = this.onStartSync.bind(this);
        this.onStopSync = this.onStopSync.bind(this);
        this.onJoinBattle = this.onJoinBattle.bind(this);
        this.onLeaveBattle = this.onLeaveBattle.bind(this);
    }

    listView = false;

    secondaryRooms = [];

    attachListeners() {
        super.attachListeners();
        this.attachListener('start_sync', this.onStartSync);
        this.attachListener('stop_sync', this.onStopSync);
        this.attachListener('join_battle', this.onJoinBattle);
        this.attachListener('leave_battle', this.onLeaveBattle);
    }

    async onAfterIdentify() {
        await super.onAfterIdentify();

        if (!this.isAuthenticated) {
            return;
        }

        this.joinUserRoom();
    }

    async clear() {
        await super.clear();
        this.leaveUserRoom();
    }

    async onStartSync() {
        this.listView = true;
        this.joinUserRoom();
        this.socket.join('battles:public');
    }

    async onStopSync() {
        this.listView = false;
        this.leaveUserRoom();
        this.socket.leave('battles:public');
    }

    async onJoinBattle(battleId) {
        // Join the socket battle room
        this.socket.join(`battle:${battleId}`);

        // Add the battle room to secondaryRooms
        if (!this.secondaryRooms.includes(`battle:${battleId}`)) {
            this.secondaryRooms.push(`battle:${battleId}`);
            await this.refresh();
        }
    }

    async onLeaveBattle(battleId) {
        // Leave the socket battle room first
        this.socket.leave(`battle:${battleId}`);

        // Remove the battle room from secondaryRooms
        this.secondaryRooms = this.secondaryRooms.filter(room => room !== `battle:${battleId}`);
        await this.refresh();
    }

    joinUserRoom() {
        if (this.isAuthenticated && this.listView) {
            this.socket.join(`battles:private:${this.user.user_hash}`);
        }
    }

    leaveUserRoom() {
        if (this.isAuthenticated) {
            this.socket.leave(`battles:private:${this.user.user_hash}`);
        }
    }
}
