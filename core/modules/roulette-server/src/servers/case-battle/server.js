// CaseBattleServer.js
import Server from '../../lib/server';
import CaseBattleMaster from './lib/CaseBattleMaster';
import CaseBattleWorker from './lib/CaseBattleWorker';

export default class CaseBattleServer extends Server {
    WorkerModuleClass = CaseBattleWorker;

    MasterModuleClass = CaseBattleMaster;

    enableOnlineCounts = true

    constructor(nsp) {
        super(nsp);

        const opts = {
            timeout: 100,
            maxCount: 20,
            flatten: true,
            auto: true,
        };
        this.createThrottler('eos_block_created', opts).onPush(this.onEosBlockCreated);
    }

    onEosBlockCreated = (data) => {
        const eosBlockData = { ...data };
        if (this.masterModule) {
            this.masterModule.parseActiveBattle(eosBlockData.battle_data);
        }
        delete eosBlockData.battle_data;
        return eosBlockData;
    }
}
