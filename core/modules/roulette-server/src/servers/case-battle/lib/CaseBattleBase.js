import ServerModule from '../../../lib/server-module';
import client from '../../../lib/redis';

export default class CaseBattleBase extends ServerModule {
    logSuffix = 'lib:CaseBattleBase';

    redisActiveBattlesKey = process.env.REDIS_ACTIVE_BATTLES_KEY || 'casebattle:active';

    async parseActiveBattle(battleEntry) {
        const battle = typeof battleEntry === 'string' ? JSON.parse(battleEntry) : battleEntry;

        // Add the battle to the list
        this.log.debug('parseActiveBattle: Added battle', battle.id, battle.slug);
        const multi = client.multi();
        multi.rpush(this.redisActiveBattlesKey, battle.slug);
        multi.setex(`${this.redisActiveBattlesKey}:${battle.slug}:data`, 3600, JSON.stringify(battle));
        await multi.exec();
    }
}
