import CaseBattleBase from './CaseBattleBase';
import { internalApiRequest, setImmediatePromise } from '../../../helpers';
import { createClient, redlock } from '../../../lib/redis';

const ROUND_DELAY = process.env.ROUND_DELAY || 6;
const FAST_ROUND_DELAY = process.env.FAST_ROUND_DELAY || 3;
const INTERNAL_API_UPDATE_BATTLE_VIEWS = process.env.INTERNAL_API_UPDATE_BATTLE_VIEWS || '/case-battle/views';

class CaseBattleWorker extends CaseBattleBase {
    logSuffix = 'lib:CaseBattleWorker';

    activeBattles = [];

    static async create(server) {
        const casebattle = new CaseBattleWorker(server);
        // blocking client for blpop operation
        casebattle.client = createClient({}, 'casebattle:worker');
        casebattle.startCheckingRounds();
        casebattle.loadActiveBattles();
        return casebattle;
    }

    loadActiveBattles = async () => {
        if (this.server.isShuttingDown) return;

        this.loadingActiveBattles = true;
        try {
            const battleString = await this.client.blpop(this.redisActiveBattlesKey, 5);
            const battleSlug = battleString && battleString[1] ? battleString[1] : null;
            const battleData = battleSlug ? await this.client.get(`${this.redisActiveBattlesKey}:${battleSlug}:data`) : null;
            if (battleData) {
                const battle = JSON.parse(battleData);
                battle.lock = await redlock.lock(`${this.redisActiveBattlesKey}:${battle.slug}:lock`, 3600);

                // server closed meanwhile
                if (this.server.isShuttingDown) {
                    await this.pushBackToQueue(battle);
                    return;
                }

                this.activeBattles[battle.slug] = battle;
                this.log.debug('loadActiveBattles: Added battle', battle.id, battle.slug);
            }
        } catch (error) {
            this.log.error('loadActiveBattles: Failed to load active battles', error);
        } finally {
            this.loadingActiveBattles = false;
        }

        await setImmediatePromise();
        this.loadActiveBattles();
    }

    startCheckingRounds = (usedTime = null) => {
        if (this.server.isShuttingDown) return;

        const timeout = usedTime !== null ? Math.max(0, 250 - usedTime) : 250;
        this.checkBattleRoundInterval = setTimeout(this.checkBattleRound, timeout);
    }

    checkBattleRound = async () => {
        if (this.server.isShuttingDown) return;

        this.checkingRounds = true;
        const startTime = Date.now();
        this.currentTime = Math.floor(startTime / 1000);
        const onlineCounts = await this.server.presence.getAndUpdateOnlineCount();
        const roomCounts = onlineCounts?.roomCounts || {};
        const viewerUpdates = [];

        const battles = Object.entries(this.activeBattles);
        await Promise.all(battles.map(async ([slug, battle]) => {
            // unblock the event loop
            await setImmediatePromise();

            const activeBattle = { ...battle };

            // Skip if the battle hasn't actually started
            if (!activeBattle.started_at) {
                return;
            }

            if (!activeBattle.lastBroadcastTime) {
                activeBattle.lastBroadcastTime = activeBattle.started_at;
            }

            if (!activeBattle.totalDelay) {
                activeBattle.totalDelay = 0;
            }

            // Initialize missing fields
            if (activeBattle.activeRound === undefined) {
                activeBattle.activeRound = this.calculateActiveRound(activeBattle);
                this.log.debug('checkBattleRound: Calculated active round', activeBattle.slug, activeBattle.activeRound);
            }

            const elapsedTime = this.currentTime - activeBattle.lastBroadcastTime;
            const roundCount = activeBattle.rounds.length;
            const hasTiebreaker = !!activeBattle.tiebreaker;
            const totalRounds = roundCount + (hasTiebreaker ? 1 : 0);
            const roundDelay = battle.is_fast ? FAST_ROUND_DELAY : ROUND_DELAY;

            /**
             * 1) If we still have rounds (or a tiebreaker) to broadcast decide if we can broadcast based on elapsedTime and gold_spin-based delay.
             * 2) If nextRound is within roundCount, broadcast a normal round.
             * 3) If nextRound == totalRounds and we have a tiebreaker, broadcast tiebreaker.
             */
            if (activeBattle.activeRound < totalRounds) {
                const nextRound = activeBattle.activeRound + 1;
                const isTiebreakerRound = hasTiebreaker && nextRound === totalRounds;

                // Check if the previous round had a gold spin
                let previousHadGoldSpin = false;
                if (activeBattle.activeRound > 0 && activeBattle.activeRound <= roundCount) {
                    const prevRoundIndex = activeBattle.activeRound - 1;
                    const previousRoundData = activeBattle.rounds[prevRoundIndex];
                    if (previousRoundData.players.some(p => p.gold_spin)) {
                        previousHadGoldSpin = true;
                    }
                }

                // If the previous round had gold_spin, double the ROUND_DELAY + animation delay (2)
                const effectiveDelay = previousHadGoldSpin ? (roundDelay * 2) + 2 : roundDelay;

                // First round starts immediately OR enough time has passed for next round
                if (nextRound === 1 || elapsedTime >= effectiveDelay) {
                    activeBattle.activeRound = nextRound;
                    activeBattle.lastBroadcastTime = this.currentTime;

                    // If we just had a gold spin, track the extra delay
                    if (previousHadGoldSpin) {
                        activeBattle.totalDelay += roundDelay;
                    }

                    if (isTiebreakerRound) {
                        this.broadcastTiebreaker(slug, activeBattle.tiebreaker, activeBattle.total_unboxed);
                    } else {
                        const roundData = activeBattle.rounds[nextRound - 1];

                        // Broadcast the normal round
                        this.broadcastRoundBattle(
                            slug,
                            activeBattle.is_private,
                            activeBattle.players,
                            roundData,
                            nextRound,
                        );

                        // Check if current round has gold spin
                        const currentHasGoldSpin = roundData.players.some(player => player.gold_spin);
                        if (currentHasGoldSpin) {
                            this.broadcastGoldSpin(slug, nextRound);
                        }
                    }

                    // If that was the *final* round (normal or tiebreaker) set justBroadcastedFinalRound for extra round buffer
                    if (activeBattle.activeRound === totalRounds) {
                        activeBattle.justBroadcastedFinalRound = true;
                    }
                }
            }

            // totalElapsedTime since battle started
            const totalElapsedTime = this.currentTime - activeBattle.started_at;

            // For all rounds + possible tiebreaker
            const adjustedTotalTime = totalRounds * roundDelay + activeBattle.totalDelay;

            // Remove the battle if:
            // 1) Its not justBroadcastedFinalRound on this iteration
            // 2) We've passed the total time threshold
            // 3) The current activeRound >= totalRounds
            if (
                !activeBattle.justBroadcastedFinalRound
                && totalElapsedTime > adjustedTotalTime
                && activeBattle.activeRound >= totalRounds
            ) {
                // Before removing, record final viewer counts
                const battleUserCount = roomCounts[`battle:${slug}`] || 0;
                viewerUpdates.push({
                    slug,
                    count: battleUserCount,
                });

                // Remove Battle from activeBattles
                delete this.activeBattles[slug];
            } else {
                // If we did broadcast final round, let it persist for another iteration
                if (activeBattle.justBroadcastedFinalRound) {
                    delete activeBattle.justBroadcastedFinalRound;
                }
                this.activeBattles[slug] = activeBattle;
            }
        }));

        // unblock the event loop
        await setImmediatePromise();

        this.startCheckingRounds(Date.now() - startTime); // set next check

        // Update battles views batch
        await this.batchUpdateBattleViews(viewerUpdates);
        this.checkingRounds = false;
    };

    broadcastRoundBattle(slug, isPrivate, players, roundData, currentRound) {
        const battleRound = { ...roundData, slug };
        const listData = { slug, currentRound };

        // Get player info for the new round
        const playersInfo = battleRound.players.map((roundPlayer) => {
            // Default to an empty object if playerData is not found
            const playerData = players.find(p => p.position === roundPlayer.position) || {};

            // Check if playerData is not the default empty object
            if (playerData?.user_id) {
                // Rename user_id to id and remove user_hash and bot_profile
                playerData.id = playerData.user_id;
                delete playerData.user_id;
                delete playerData.user_hash;
                delete playerData.bot_profile;
            }

            return {
                ...roundPlayer,
                ...playerData,
            };
        });

        battleRound.players = playersInfo;
        // Emit to the specific battle room with round data
        this.server.socket.to(`battle:${slug}`).emit('new_round', battleRound);

        // If battle is private emit to the user listing room
        if (isPrivate) {
            players.forEach((player) => {
                // if player is not a bot, send to the private listing room
                if (!player.bot_profile) {
                    this.server.socket.to(`battles:private:${player.user_hash}`).emit('new_round', listData);
                }
            });
            return;
        }
        // Battle nor private, Emit to the public battles:list room
        this.server.socket.to('battles:public').emit('new_round', listData);
    }

    broadcastTiebreaker(slug, tiebreakerData, totalUnboxedAmounts) {
        // Emit to the specific battle room with tiebreaker data
        const tiebreaker = { ...tiebreakerData, slug };
        this.server.socket.to(`battle:${slug}`).emit('tiebreaker', { tiebreaker, total_unboxed: totalUnboxedAmounts });
    }

    broadcastGoldSpin(slug, round) {
        // Emit to the specific battle room with gold_spin data and round number
        this.server.socket.to(`battle:${slug}`).emit('gold_spin', { gold_spin: true, round });
    }

    calculateActiveRound(battle) {
        const { currentTime } = this;

        const roundDelay = battle.is_fast ? FAST_ROUND_DELAY : ROUND_DELAY;

        const GOLD_SPIN_DELAY = (roundDelay * 2) + 2;

        const elapsed = currentTime - battle.started_at;
        const rounds = battle.rounds || [];
        const total = rounds.length + (battle.tiebreaker ? 1 : 0);

        let time = 0;
        let active = 0;
        let gold = false;

        for (let i = 0; i < total; i++) {
            if (i > 0) {
                const delay = gold ? GOLD_SPIN_DELAY : roundDelay;
                time += delay;
            }
            if (time > elapsed) break;
            active = i + 1;
            gold = (i < rounds.length) ? rounds[i].players.some(p => p.gold_spin) : false;
        }

        return active - 1;
    }

    waitForRoundCheckToComplete = async () => {
        if (!this.checkingRounds && !this.loadingActiveBattles) return;

        const maxWaitTime = 10000; // milliseconds how long to wait for round checking to complete

        const startTime = Date.now();
        // Wait for any in-progress round checking to complete
        await new Promise((resolve) => {
            const interval = setInterval(() => {
                const timeout = Date.now() - startTime >= maxWaitTime;
                if (timeout) {
                    this.log.warn('waitForRoundCheckToComplete: Timeout waiting for round check to complete', this.checkingRounds, this.loadingActiveBattles);
                }

                if ((!this.checkingRounds && !this.loadingActiveBattles) || timeout) {
                    clearInterval(interval);
                    resolve();
                }
            }, 100);
        });
    }

    async pushBackToQueue(battle) {
        try {
            // release the lock
            await battle.lock.unlock();
        } catch (error) {
            this.log.error('stop: Failed to unlock battle', error);
        }

        const activeBattle = { ...battle };
        delete activeBattle.lock;
        delete activeBattle.activeRound;

        // remove the battle from the queue if already there so we get new data
        await this.client.lrem(this.redisActiveBattlesKey, 0, battle.slug);
        // push back to the queue so another worker can pick it up
        await this.parseActiveBattle(activeBattle);
    }

    async stop() {
        await super.stop();
        clearInterval(this.checkBattleRoundInterval);

        await this.waitForRoundCheckToComplete();

        if (!Object.keys(this.activeBattles).length) {
            return;
        }

        // release all locks from active battles and push back to the queue so another worker can pick it up
        await Promise.all(Object.values(this.activeBattles).map(battle => this.pushBackToQueue(battle)));
    }

    async batchUpdateBattleViews(viewerUpdates) {
        try {
            if (viewerUpdates.length === 0) return;
            // Batch Update views
            await internalApiRequest(INTERNAL_API_UPDATE_BATTLE_VIEWS, viewerUpdates);
        } catch (error) {
            this.log.error('batchUpdateBattleViews: Failed to update battle views', error);
        }
    }
}

module.exports = CaseBattleWorker;
