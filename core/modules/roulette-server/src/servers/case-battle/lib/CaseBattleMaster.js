import CaseBattleBase from './CaseBattleBase';
import { handleException, internalApiRequest } from '../../../helpers';

const INTERNAL_API_ACTIVE_BATTLES = process.env.INTERNAL_API_ACTIVE_BATTLES || '/case-battle/active';

export default class CaseBattleMaster extends CaseBattleBase {
    logSuffix = 'lib:CaseBattleMaster';

    checkBattleRoundInterval = null;

    static async create(server) {
        const casebattle = new CaseBattleMaster(server);
        try {
            await casebattle.loadActiveBattles();
        } catch (err) {
            await handleException(err, 'CRITICAL', 'casebattle.create() failed.');
            throw err;
        }
        return casebattle;
    }

    async loadActiveBattles() {
        const response = await internalApiRequest(INTERNAL_API_ACTIVE_BATTLES);

        if (!response.success || !response.data) {
            return;
        }

        await Promise.all(response.data.map(battleEntry => this.parseActiveBattle(battleEntry)));
    }
}
