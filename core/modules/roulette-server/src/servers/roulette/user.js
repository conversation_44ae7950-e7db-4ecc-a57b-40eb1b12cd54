import mapKeys from 'lodash/mapKeys';
import isEqual from 'lodash/isEqual';
import BigNumber from 'bignumber.js';
import Bet from './bet';
import BetUser from '../../lib/bet-user';
import client from '../../lib/redis';
import { setImmediatePromise, toBoolean } from '../../helpers';
import { getPrizeAmount } from './lib/RouletteBase';

export default class RouletteUser extends BetUser {
    BetClass = Bet;

    emitRouletteRacePositionTimeout = null;

    userRacePositions = {};

    last_bet = { // eslint-disable-line camelcase
        round: 0,
        coin: '',
        amount: 0,
        time: Date.now(),
    };

    setLastBet({ round, coin, amount }) {
        this.last_bet.round = round || 0;
        this.last_bet.coin = coin || '';
        this.last_bet.amount = amount || 0;
        this.last_bet.time = Date.now();
    }

    async clear() {
        await super.clear();
        clearTimeout(this.emitRouletteRacePositionTimeout);
    }

    async afterBetCommit() {
        await super.afterBetCommit();
        await this.emitRouletteRacePosition();
    }

    async onAfterIdentify() {
        await super.onAfterIdentify();

        try {
            await setImmediatePromise(); // go to the next tick to make sure socket is available

            this.emitRouletteLeaderBoard();

            if (!this.isAuthenticated) {
                return;
            }

            await this.emitRouletteRacePosition();
        } catch (error) {
            this.log.error('Failed to handle user roulette identify', error);
        }
    }

    emitRouletteLeaderBoard() {
        const activeRouletteRaces = this.server.races;

        this.log.debug('Emitting roulette leader board for user', this.userId, activeRouletteRaces);

        // Emit even if races is empty, otherwise frontend cannot distinguish between waiting for races
        // and races not existing.
        // TODO: Should get fixed with https://moonrailteam.atlassian.net/browse/MP-4929
        this.socket.emit('rouletteRaceLeaders', { races: activeRouletteRaces });
    }

    async emitRouletteRacePosition() {
        try {
            const activeRouletteRaces = this.server.races;
            if (!activeRouletteRaces || !activeRouletteRaces.length) {
                return;
            }
            clearTimeout(this.emitRouletteRacePositionTimeout);

            const userId = this.user.id;
            const raceData = await Promise.all(activeRouletteRaces.map(async ({ race_id: raceId, race_config: config }) => {
                const raceKey = `roulette-race:${raceId}`;

                const [userRank, userScore] = await Promise.all([client.zrevrank(raceKey, userId), client.zscore(raceKey, userId)]);
                let userWager = (userScore === null) ? 0 : Number(userScore);
                const useNativeCurrencies = toBoolean(process.env.USE_NATIVE_CURRENCIES || false);
                if (userWager > 0 && useNativeCurrencies) {
                    userWager = new BigNumber(userWager);
                    userWager = Number(userWager.dividedBy(100).toFixed(6));
                }
                const userPosition = (userRank === null) ? 0 : (userRank + 1);

                const {
                    prize_percentages: prizePercentages,
                    prize: prizePool,
                } = config;
                const userPrize = getPrizeAmount(prizePool, prizePercentages, userPosition);
                return {
                    race_id: raceId,
                    position: userPosition,
                    wager: userWager,
                    prize: userPrize,
                };
            }));

            const hasChanges = raceData.some(race => race.wager
                && race.position
                && !isEqual(this.userRacePositions[race.race_id], race));

            this.userRacePositions = mapKeys(raceData, 'race_id');

            if (!hasChanges) {
                return;
            }

            const emitData = { races: raceData };

            const eventName = 'rouletteRaceUser';
            this.log.debug(`${eventName} emit data: `, emitData);
            this.socket.emit(eventName, emitData);
        } catch (error) {
            this.log.warn('Errors while emitting user race data in method emitRouletteRacePosition', '', error);
        } finally {
            this.emitRouletteRacePositionTimeout = setTimeout(() => this.emitRouletteRacePosition(), 10000);
        }
    }
}
