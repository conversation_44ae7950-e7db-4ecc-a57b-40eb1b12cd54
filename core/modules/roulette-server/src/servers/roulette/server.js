import Server from '../../lib/server-game';
import Roulette<PERSON>orker from './lib/RouletteWorker';
import RouletteMaster from './lib/RouletteMaster';
import { coins } from './constants';

export default class RouletteServer extends Server {
    WorkerModuleClass = RouletteWorker

    MasterModuleClass = RouletteMaster

    get roulette() {
        return this.slaveModule;
    }

    get rouletteMaster() {
        return this.masterModule;
    }

    get keys() { // eslint-disable-line class-methods-use-this
        return coins;
    }

    async next() {
        this.readyToAcceptConnections();

        this.log.debug('round prepared...');

        if (this.isMaster) {
            this.log.debug('Start round...');
            await this.rouletteMaster.start();
        } else {
            this.log.debug('Wait round...');
            await this.roulette.waitRound();
        }

        this.log.debug('running this.next() done');
    }

    async promoteToMaster() {
        await super.promoteToMaster();

        if (!this.roulette || !this.roulette.onRolled) {
            this.log.debug('Promoted to master without active slave game.');
            return;
        }

        this.log.info('Promoted to master, but I have slave game. End it now.');
        this.roulette.onRolled();
        this.roulette.onRolled = null;
    }

    getCurrentState(user) {
        const state = super.getCurrentState(user);

        state.timer = this.module?.timer;
        state.animationTimer = this.module?.animationTimer;
        state.races = Object.values(this.module?.racesLeaderBoards || {});

        return state;
    }

    get state() {
        return this.roulette?.state;
    }
}
