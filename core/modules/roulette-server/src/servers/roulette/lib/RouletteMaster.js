import mapValues from 'lodash/mapValues';
import mapKeys from 'lodash/mapKeys';
import uniq from 'lodash/uniq';
import isEqual from 'lodash/isEqual';
import RouletteBase, {
    ROULETTE_STATE_SPIN,
    ROULETTE_STATE_ROLL,
    ROULETTE_STATE_ROLLING,
    ROULETTE_STATE_END,
    getPrizeAmount,
} from './RouletteBase';
import { knex } from '../../../database';
import {
    internalApiRequest, handleException, sleep,
} from '../../../helpers';
import User from '../../../database/User';
import RouletteSeed from './RouletteSeed';
import { coins } from '../constants';

/**
 * Should not be instantiated directly, but rather be called via the static `create()` function
 */
export default class RouletteMaster extends RouletteBase {
    logSuffix = 'lib:RouletteMaster';

    emitRouletteTopListTimeout = null;

    racesLeaderBoards = {};

    cachedUserNames = {};

    cachedUserNamesHistory = 0;

    constructor(server) {
        super(server);

        this.log.debug('creating RouletteMaster');

        this.round = 0;
        this.nextRound = 0;
        this.races = [];
        this.rolls = [];

        this.seed = new RouletteSeed(server);
    }

    static async create(server) {
        const roulette = new RouletteMaster(server);
        await roulette.restore();
        return roulette;
    }

    async restore() {
        this.log.info('Restoring roulette round...');

        let rows;
        try {
            const rouletteRollsRowsPromise = knex.raw('SELECT id, roll FROM roulette_rolls ORDER BY id DESC LIMIT 100');

            const [rouletteRollsRows] = await rouletteRollsRowsPromise;
            this.round = rouletteRollsRows[0].id + 1;
            rouletteRollsRows.reverse();
            for (let i = 0; i < rouletteRollsRows.length; i++) {
                this.rolls.push(rouletteRollsRows[i].roll);
            }

            this.log.info('Roulette round', this.round);
            this.log.info('Load pending bets for the round...');
            [rows] = await knex.raw('SELECT users.id as uid, bets.coin as coin, bets.amount as amount FROM users INNER JOIN roulette_bets AS bets ON users.id = bets.user_id WHERE bets.round = ?', [this.round]);
            this.log.info('Total pending bets found', rows.length);
        } catch (err) {
            await handleException(err, 'CRITICAL', 'Roulette.create() failed.');
            throw err;
        }

        const addBetPromise = async (row) => {
            const { uid, coin, amount } = row;
            try {
                const user = await User.findOne({ id: uid });

                this.addBet({
                    ...user.toPublicJSON(),
                    uid: user.id,
                    coin,
                    amount,
                });
                this.log.info(`Loaded archived bet: user #${user.id} / coin "${coin}" / amount ${amount}`);
            } catch (err) {
                this.log.error(`Error loading archived bet: user #${uid} / coin "${coin}" / amount ${amount}\n${err}`);
                throw err;
            }
        };

        const addBetPromises = [];
        for (let i = 0; i < rows.length; ++i) {
            addBetPromises.push(addBetPromise(rows[i]));
        }

        await Promise.all(addBetPromises);
    }

    async emitState(state, data = {}) {
        this.log.info(`===============STATE: ${state.toUpperCase()}===============`);

        if (state === ROULETTE_STATE_ROLLING && this.nextRound) {
            this.log.debug('update round', this.round, '->', this.nextRound);
            this.round = this.nextRound;
        }

        this.state = state;
        this.stateData = {
            round: this.round,
            // ts: state === ROULETTE_STATE_ROLL ? this.stateData.ts : Date.now(),
            ts: Date.now(),
            rolls: this.rolls,
            ...data,
        };

        this.stateData.timer = this.timer;
        this.stateData.animationTimer = this.animationTimer;
        this.stateData.endTimer = this.endTimer;

        this.server.socket.emit(this.state, this.stateData);

        const stateData = { state, data: this.server.getCurrentState() };
        try {
            await this.server.internalRequest.slave('rouletteState', stateData);
        } catch (error) {
            this.log.warn('Errors while sending rouletteState to slaves', stateData, error);
        }
    }

    async start() {
        await this.emitState(ROULETTE_STATE_ROLLING);
        this.log.info(`===============Started countdown ${this.timer / 1000}s for new round (#${this.round})...===============`);
        await sleep(this.timer);
        clearTimeout(this.emitRouletteTopListTimeout);
        await this.spin();
    }

    async stop() {
        clearTimeout(this.emitRouletteTopListTimeout);
        await super.stop();
    }

    async spin() {
        await this.emitState(ROULETTE_STATE_SPIN);

        const rollResult = await this.seed.getRoll(this.round);
        const { seedId, pfDisabled } = rollResult;
        const { roll } = rollResult;

        let totalBet = 0;
        let totalBetCount = 0;
        coins.forEach((coin) => {
            const bets = Object.values(this.server.bets[coin]);
            totalBetCount += bets.length;
            bets.forEach((bet) => {
                totalBet += bet.amount;
            });
        });

        await this.server.waitPendingBets();

        const delay = Math.round(this.animationTimer / 1000);

        let result;
        this.log.info('Inserting roll result into database', 'delay_payout=', delay, 'round=', this.round, 'roll=', roll, 'totalBetCount=', totalBetCount, 'totalBetAmount=', totalBet);
        try {
            result = await internalApiRequest('roulette/roll', {
                roll,
                // Bonus bot removed. If you need it back, check from git
                // Gitlab MR: https://gitlab.com/csgoempire/roulette-server/-/merge_requests/333
                // Basecamp: https://3.basecamp.com/4276093/buckets/13510524/todos/3918753408
                bonusPotValue: 0,
                currentRound: this.round,
                totalBet,
                seedId: pfDisabled ? Math.round(Math.random() * 100000) : seedId,
                delay,
            });

            this.log.debug('Round inserted', result);
        } catch (err) {
            if (err.data?.message_localized?.key?.includes('this_feature_is_currently_disabled')) {
                this.log.warn('Roulette is disabled');
                return;
            }

            await handleException(err, 'CRITICAL', 'Roulette.spin(): Failed to insert new row into roulette_rolls.', {
                round: this.round,
                totalBet,
                roll,
                seed_id: seedId,
            });
            throw err;
        }

        this.log.info('Roll results inserted.');

        // set races and emit race leaders
        this.setRaces(result.data.races); // do not wait for this by purpose

        this.nextRound = (result.id || result.data.round.id) + 1;

        const coin = RouletteBase.getCoin(roll);
        await this.emitState(ROULETTE_STATE_ROLL, {
            winner: roll,
            coin,
            nextRound: this.nextRound,
            bets: mapValues(this.bets, bets => Object.values(bets)),
        });

        this.log.debug('Sleep', this.animationTimer / 1000, 'seconds before end state');
        await sleep(this.animationTimer);

        this.rolls.push(roll);
        this.rolls.shift();
        await this.emitState(ROULETTE_STATE_END, { coin, winner: roll });
        this.log.debug('Sleep', this.endTimer / 1000, 'seconds before start rolling next round');
        await sleep(this.endTimer);
    }

    addBet(bet) {
        const {
            coin,
            uid,
            amount,
        } = bet;

        this.log.info(`[Round #${this.round}] Added bet of ${amount} on ${coin} from user #${uid} into list.`);
    }

    async setRaces(races) {
        if (!races.length) {
            if (this.races.length) {
                this.races = [];
                await this.sendRacesToSlaves(null);
            }

            return;
        }

        this.cachedUserNamesHistory++;

        // reset cached usernames every 20 rounds which is approx 10 minutes
        if (this.cachedUserNamesHistory > 20) {
            this.cachedUserNamesHistory = 0;
            this.cachedUserNames = {};
        }

        this.races = races;
        await this.emitRouletteTopList();
    }

    async emitRouletteTopList() {
        try {
            clearTimeout(this.emitRouletteTopListTimeout);
            // get leaders userIds and leader information array
            const leaders = await this.getLeaderList();
            this.log.debug('leaders', leaders);

            const { races, userIds } = leaders;

            if (!userIds.length) {
                await this.sendRacesToSlaves(null);
                return;
            }

            let newLeadersUserIds = userIds;

            // get cached [userId : username] array
            const cachedUserNames = { ...this.cachedUserNames };
            const cachedUserIds = Object.keys(cachedUserNames);

            // check is there a new user in leader list by comparing with cache data,
            if (cachedUserIds.length) {
                newLeadersUserIds = userIds.filter(element => !cachedUserIds.includes(element));
                const notInLeaderUserIds = cachedUserIds.filter(element => !userIds.includes(element));
                notInLeaderUserIds.forEach((userId) => {
                    delete cachedUserNames[userId];
                });
            }

            this.log.debug('roulette races newLeadersUserIds', newLeadersUserIds);

            // get username for new users in leaderlist
            if (newLeadersUserIds.length > 0) {
                const users = await knex.select('id', 'steam_name as username').from('users').whereIn('id', newLeadersUserIds);
                if (users) {
                    users.forEach((user) => {
                        cachedUserNames[user.id] = user.username;
                    });
                }
            }

            this.cachedUserNames = { ...cachedUserNames };

            // put username to leaderlist from list
            const newRaces = await Promise.all(races.map(async (raceData) => {
                this.log.debug('newRaces::raceData', raceData);
                const { id, config, leadersDetails } = raceData;

                this.log.debug('newRaces::config', config);

                const {
                    prize_percentages: prizePercentages,
                    prize: prizePool,
                } = config;
                config.number_of_prize = prizePercentages.length;

                // create leader list
                const newLeadersList = leadersDetails.map((leaderDetails) => {
                    const leader = { ...leaderDetails };
                    leader.prize = getPrizeAmount(prizePool, prizePercentages, leader.position);
                    leader.username = cachedUserNames[leader.user_id];
                    return leader;
                });
                return {
                    race_id: id,
                    race_leaders_list: newLeadersList,
                    race_config: config,
                };
            }));

            const hasChanges = newRaces.some(race => !isEqual(race, this.racesLeaderBoards[race.race_id]));

            if (!hasChanges) {
                return;
            }

            this.racesLeaderBoards = mapKeys(newRaces, 'race_id');

            const newLeaders = {
                races: newRaces,
            };

            const eventName = 'rouletteRaceLeaders';
            this.log.debug(`${eventName} leader list:`, newLeaders);
            this.server.socket.emit(eventName, newLeaders);

            await this.sendRacesToSlaves(newRaces);
        } catch (error) {
            this.log.warn('Errors while emitting race data', '', error);
        } finally {
            this.emitRouletteTopListTimeout = setTimeout(() => this.emitRouletteTopList(), 10000);
        }
    }

    async getLeaderList() {
        const activeRouletteRaces = this.races;
        const userIds = [];
        const raceLeaderLists = await Promise.all(activeRouletteRaces.map(async ({ id, config }) => {
            let result;
            try {
                result = await internalApiRequest(`/roulette/races/${id}`, {
                    per_page: 10,
                    page: 1,
                });
            } catch (err) {
                await handleException(err, 'CRITICAL', 'Roulette.getLeaderList(): Failed to get roulette list.', {
                    round: this.round,
                    id,
                });
                throw err;
            }
            // get leaders userIDs to get usernames from DB
            const leadersDetails = result.data.list.reduce((returnArray, value) => {
                if (value.user_id !== '-') {
                    userIds.push(value.user_id);
                    returnArray.push({
                        position: value.position,
                        wager: value.wager,
                        user_id: value.user_id,
                    });
                }
                return returnArray;
            }, []);

            return {
                id,
                leadersDetails,
                config,
            };
        }));

        return {
            races: raceLeaderLists,
            userIds: uniq(userIds),
        };
    }

    async sendRacesToSlaves(newLeaders) {
        try {
            await this.server.internalRequest.slave('rouletteRaces', newLeaders ? { races: newLeaders } : null);
        } catch (error) {
            this.log.error('Failed to send races to slaves', error);
        }
    }
}
