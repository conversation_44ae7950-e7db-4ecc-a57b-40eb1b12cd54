import RouletteBase, { ROULETTE_STATE_ROLLING } from './RouletteBase';
import { sleep } from '../../../helpers';

export default class RouletteWorker extends RouletteBase {
    logSuffix = 'lib:RouletteWorker';

    setStateTimer = null;

    constructor(server) {
        super(server);
        this.stateTimer = null;
    }

    get round() {
        return this.stateData.round;
    }

    static async create(server) {
        const roulette = new RouletteWorker(server);
        await roulette.ready();
        return roulette;
    }

    async ready() {
        const reply = await this.server.internalRequest.getMaster('getCurrentState');
        if (!reply || !reply.state || !reply?.round || reply?.round <= 0) {
            if (this.server.isMaster && !this.server.rouletteMaster) {
                throw new Error('Seems that I should be master, but master instance is not initialized.');
            }

            if (reply?.initializing === true) {
                this.log.info('Master is initializing, waiting...');
            } else {
                this.log.info('Empty response from master, waiting...');
            }
            await sleep(300);
            await this.ready();
            return;
        }

        this.log.debug('reply from rouletteInit', reply);
        this.updateState(reply.state, reply);
        this.setupMessageHandlers();
        await sleep(100);
    }

    waitRound() {
        return new Promise((resolve, reject) => {
            this.onRolled = async () => {
                clearTimeout(this.stateTimer);
                this.setStateTimer = null;
                await this.server.throttler('bets').flush();
                resolve();
            };

            this.setStateTimer = () => {
                this.log.debug('Reset state timer.');
                clearTimeout(this.stateTimer);
                this.stateTimer = setTimeout(() => {
                    this.onRolled = null;
                    reject(new Error('Did not get new state in 30 seconds'));
                }, 30000);
            };
        });
    }

    stop() {
        clearTimeout(this.stateTimer);
        this.server.onRouletteState = null;
    }

    setupMessageHandlers() {
        this.server.onRouletteState = (from, { state, data }) => {
            if (!state || !data) {
                return;
            }
            this.updateState(state, data);
        };
    }

    updateState(state, data) {
        if (this.setStateTimer) {
            this.setStateTimer();
        }

        this.state = state;
        this.stateData = data;
        this.server.races = data?.races || null;

        this.log.debug('state updated to', state, 'with round', data.round, 'and races', this.server.races);

        if (this.state === ROULETTE_STATE_ROLLING && this.onRolled) {
            this.onRolled();
        }
    }

    onRouletteRaces(from, data) {
        this.server.races = data?.races || null;
    }
}
