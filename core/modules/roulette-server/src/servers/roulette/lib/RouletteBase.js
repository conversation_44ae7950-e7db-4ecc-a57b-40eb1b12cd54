import ServerModule from '../../../lib/server-module';

export const ROULETTE_STATE_SPIN = 'spin';
export const ROULETTE_STATE_ROLL = 'roll';
export const ROULETTE_STATE_ROLLING = 'rolling';
export const ROULETTE_STATE_END = 'end';

const ROLL_TIMER = parseInt(process.env.ROLL_TIMER || 20) * 1000;
const ROULETTE_ANIMATION_LENGTH = parseInt(process.env.ROULETTE_ANIMATION_LENGTH || 6) * 1000;
const ROULETTE_END_PERIOD = parseInt(process.env.ROULETTE_END_PERIOD || 3) * 1000;

export default class RouletteBase extends ServerModule {
    logSuffix = 'lib:RouletteBase';

    constructor(server) {
        super(server);

        this.state = false;
        this.stateData = {};

        this.interval = ROLL_TIMER;
        this.animationLength = ROULETTE_ANIMATION_LENGTH;
        this.endPeriod = ROULETTE_END_PERIOD;
    }

    static getCoin(winner) {
        if (winner === 0) {
            return 'bonus';
        }

        if (winner < 8) {
            return 't';
        }

        return 'ct';
    }

    get animationTimer() {
        const timer = (this.stateData.ts + this.animationLength) - Date.now();
        return timer > 0 ? timer : this.animationLength;
    }

    get timer() {
        return (this.stateData.ts + this.interval) - Date.now();
    }

    get endTimer() {
        return (this.stateData.ts + this.endPeriod) - Date.now();
    }
}

export function getPrizeAmount(prizePool, prizePercentages, position) {
    const percentages = prizePercentages;
    const prizeInPercent = prizePool / 100;
    const percent = position > 0 && position <= percentages.length ? percentages[position - 1] : 0;

    return prizeInPercent * percent;
}
