import AWS from 'aws-sdk';
import { knex } from '../../../database';
import ServerModule from '../../../lib/server-module';
import {
    handleException,
    toBoolean,
    IS_PRODUCTION,
} from '../../../helpers';
import {
    getRouletteRollResult,
    getHashForValue,
    generateRoulettePublicSeed,
    generateRoulettePrivateSeed,
} from '../../../lib/provably-fair';

const USE_KMS = IS_PRODUCTION || toBoolean(process.env.USE_KMS);

const KMS_KEY_ID = process.env.KMS_KEY_ID || '1234abcd-12ab-34cd-56ef-1234567890ab';

const defaultKmsEndpoint = process.env.KMS_KEY_ID ? null : 'http://localstack:4566';
const defaultKmsAccessKeyId = process.env.KMS_KEY_ID ? null : 'test';
const defaultKmsSecretAccessKey = process.env.KMS_KEY_ID ? null : 'test';

const kms = USE_KMS ? new AWS.KMS({
    endpoint: process.env.KMS_ENDPOINT || defaultKmsEndpoint,
    region: process.env.KMS_REGION || 'us-east-2',
    accessKeyId: process.env.KMS_ACCESS_KEY_ID || defaultKmsAccessKeyId,
    secretAccessKey: process.env.KMS_SECRET_ACCESS_KEY || defaultKmsSecretAccessKey,
}) : null;

export default class RouletteSeed extends ServerModule {
    logSuffix = 'lib:RouletteSeed';

    seedId;

    seedDate;

    privateSeed;

    publicSeed;

    currentRound;

    async getRoll(currentRound) {
        const result = await this.getHash(currentRound);
        result.roll = getRouletteRollResult(result.hash);
        return result;
    }

    async getHash(currentRound) {
        if (!this.seedId) {
            await this.reload();
        }

        if ((new Date()).toDateString() !== this.seedDate.toDateString()) {
            this.log.info('set new day seed', (new Date()).toDateString(), 'vs', this.seedDate.toDateString());
            await this.setNewSeed();
        }

        const hash = getHashForValue(`${this.publicSeed}-${currentRound}`, this.privateSeed);
        return { hash, seedId: this.seedId };
    }

    async reload() {
        this.log.info('reload seed from the database');

        const [rouletteSeedsRows] = await knex.raw('SELECT id, date, server_seed, public_seed, server_seed_blob FROM roulette_seeds ORDER BY id DESC LIMIT 1');
        const [rouletteSeedsRow] = rouletteSeedsRows;

        const {
            id,
            date,
            server_seed_blob: blob,
            server_seed: serverSeed,
            public_seed: publicSeed,
        } = rouletteSeedsRow;

        let decryptedServerSeed = null;
        if (blob && USE_KMS) {
            this.log.info('Decrypting server seed with KMS', KMS_KEY_ID, blob);
            const decryptedPrivateSeed = await kms.decrypt({
                KeyId: KMS_KEY_ID,
                CiphertextBlob: blob,
            }).promise();

            decryptedServerSeed = decryptedPrivateSeed.Plaintext.toString('utf-8');
        }

        // THIS BLOCK CAN BE REMOVED AFTER KMS IS IN USE IN PRODUCTION
        if (!blob && USE_KMS) {
            this.log.info('Encrypt current server seed with KMS', KMS_KEY_ID);
            const encryptedPrivateSeed = await kms.encrypt({
                KeyId: KMS_KEY_ID,
                Plaintext: Buffer.from(serverSeed),
            }).promise();

            const hashedPrivateSeed = getHashForValue(serverSeed); // hashed version

            await knex('roulette_seeds')
                .where('id', id)
                .update({
                    server_seed: hashedPrivateSeed, // previous seed is public now
                    server_seed_blob: encryptedPrivateSeed.CiphertextBlob,
                });
        }

        this.seedId = id;
        this.seedDate = new Date(date);
        this.privateSeed = decryptedServerSeed || serverSeed;
        this.publicSeed = publicSeed;
    }

    async setNewSeed() {
        const publicSeed = generateRoulettePublicSeed();
        const privateSeed = await generateRoulettePrivateSeed();

        let seedId;
        try {
            const hashedPrivateSeed = getHashForValue(privateSeed); // hashed version

            const encryptedPrivateSeed = USE_KMS ? await kms.encrypt({
                KeyId: KMS_KEY_ID,
                Plaintext: Buffer.from(privateSeed),
            }).promise() : privateSeed;

            if (USE_KMS) {
                this.log.info('Encrypted private seed with KMS', KMS_KEY_ID);
            }

            await knex.transaction(async (trx) => {
                const [id] = await trx('roulette_seeds').insert({
                    date: trx.raw('CURDATE()'),
                    server_seed: hashedPrivateSeed,
                    public_seed: publicSeed,
                    server_seed_blob: USE_KMS ? encryptedPrivateSeed.CiphertextBlob : null,
                });
                seedId = id;
                await trx('roulette_seeds')
                    .where('id', this.seedId)
                    .update({
                        server_seed: this.privateSeed, // previous seed is public now
                        server_seed_blob: null,
                    });
            });
        } catch (err) {
            await handleException(err, 'CRITICAL', 'Roulette.setNewSeed(): Failed to insert new row into roulette_seeds.');
            throw err;
        }

        this.seedId = seedId;
        this.seedDate = new Date();
        this.privateSeed = privateSeed;
        this.publicSeed = publicSeed;
    }
}
