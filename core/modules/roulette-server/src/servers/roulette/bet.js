import Bet from '../../lib/bet';
import { BetError } from '../../errors/client';
import { coins } from './constants';
import { ROULETTE_STATE_ROLLING } from './lib/RouletteBase';

export default class RouletteBet extends Bet {
    coin = null;

    round = null;

    get key() {
        return this.coin;
    }

    get apiRoute() {
        return `/roulette/${this.round}`;
    }

    get roundKey() {
        return `${this.coin}:${this.round}`;
    }

    setBetData(data) {
        super.setBetData(data);

        const { round, coin } = data;

        this.coin = coin;

        this.round = round;

        if (!round || typeof round !== 'number' || !coin || !coins.includes(coin)) {
            throw new BetError('Incorrect coin or round.');
        }

        if (this.server.state !== ROULETTE_STATE_ROLLING) {
            throw new BetError('It\'s too late to place a bet on this round (1).');
        }
    }
}
