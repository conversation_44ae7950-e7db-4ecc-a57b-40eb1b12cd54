import { internalApiRequest, handleException } from '../../helpers';
import Server from '../../lib/server';

const MATCH_STATUS_LIVE = 'live';
const MATCH_STATUS_SCHEDULED = 'scheduled';
const MATCH_ACTIVE_STATUSES = [MATCH_STATUS_LIVE, MATCH_STATUS_SCHEDULED];

export const gameGroups = {
    TYPE_ESPORT: 'E-Sports',
    TYPE_SPORT: 'Sports',
};

const providers = {
    1: gameGroups.TYPE_ESPORT,
    2: gameGroups.TYPE_SPORT,
};

const objects = ['matches', 'markets', 'teams', 'selections'];
const objectTypes = {};
objects.forEach((object) => {
    objectTypes[object] = {};
});

/**
 * How many server rounds should pass before we sync data with backend.
 * One round is ~5 seconds, so eg. 10 rounds means that we sync data approx every 50 seconds.
 * Or 360 rounds means that we sync data every 30 minutes.
 *
 * This is safety mechanism to prevent outdated data or memory leaks.
 */
const DATA_SYNC_ROUNDS = 360;

const dataObject = () => ({
    [gameGroups.TYPE_ESPORT]: {
        ...objectTypes,
    },
    [gameGroups.TYPE_SPORT]: {
        ...objectTypes,
    },
});

export default class MatchBettingServer extends Server {
    data = dataObject();

    objects = objects;

    pendingDataUpdate = {};

    initialDataLoaded = false;

    initialLoading = true;

    dataLoading = false;

    roundsSinceLastDataSync = 0;

    roundCheckTimer = null;

    constructor(nsp) {
        super(nsp);

        const opts = {
            timeout: 1000,
            maxCount: 20,
            flatten: true,
            auto: true,
        };

        this.createThrottler('match_created', opts).onPush(this.onMatchCreated);
        this.createThrottler('match_updated', opts).onPush(this.onDataUpdated('matches'));

        this.createThrottler('market_created', opts).onPush(this.onMarketCreated);
        this.createThrottler('market_updated', opts).onPush(this.onDataUpdated('markets'));

        this.createThrottler('selection_updated', opts).onPush(this.onDataUpdated('selections'));
    }

    getGroupById(object, id) {
        if (this.data[gameGroups.TYPE_ESPORT][object][id]) {
            return gameGroups.TYPE_ESPORT;
        }
        if (this.data[gameGroups.TYPE_SPORT][object][id]) {
            return gameGroups.TYPE_SPORT;
        }

        return null;
    }

    getById(object, id) {
        return this.data[this.getGroupById(object, id)]?.[object][id];
    }

    deleteById(object, id) {
        const group = this.getGroupById(object, id);

        if (!group) {
            return;
        }

        delete this.data[group][object][id];
    }

    updateById(object, data) {
        const { id } = data;
        const group = this.getGroupById(object, id);

        if (!group) {
            return;
        }

        this.data[group][object][id] = { ...this.data[group][object][id], ...data };
    }

    onDataUpdated = object => (payload) => {
        (Array.isArray(payload) ? payload : [payload]).forEach((data) => {
            if (object === 'matches' && data.status && !MATCH_ACTIVE_STATUSES.includes(data.status)) {
                this.deleteMatch(data.id);
                return;
            }

            this.updateData(object, data);
        });
    }

    updateData(object, data, retries = 0) {
        const current = this.getById(object, data.id);
        const updateKey = `${object}_${data.id}`;

        if (this.pendingDataUpdate[updateKey]) {
            clearTimeout(this.pendingDataUpdate[updateKey]);
            delete this.pendingDataUpdate[updateKey];
        }

        const isMarketOrSelection = ['markets', 'selections'].includes(object);

        // matchid is always included to market and selection
        const match = isMarketOrSelection ? this.getById('matches', data.match_id) : current;

        // if it is selection there is market id, otherwise id is market id
        const marketId = isMarketOrSelection ? (data.market_id || data.id) : null;

        if (!match || !current) {
            if (retries >= 5) {
                this.log.info('Updated data not found from object', object, data.id, data);
                return;
            }

            if (isMarketOrSelection && match && marketId !== match.primary_market_id) {
                if (current) {
                    this.deleteById(object, data.id);
                }
                // update for non primary market, ignore
                return;
            }

            // in some cases updated event before we have the data
            this.pendingDataUpdate[updateKey] = setTimeout(() => this.updateData(object, data, retries + 1), 1000);
            return;
        }

        if (isMarketOrSelection && match.primary_market_id !== marketId) {
            this.deleteById(object, data.id);
            return;
        }

        if (object === 'matches' && data.primary_market_id !== undefined && data.primary_market_id !== current.primary_market_id) {
            this.deleteUselessMarkets(data.id, data.primary_market_id);
        }

        this.log.debug('Data updated', object, data);
        this.updateById(object, data);
    }

    deleteUselessMarkets(matchId, primaryMarketId) {
        const group = this.getGroupById('matches', matchId);

        if (!group) {
            return;
        }

        Object.values(this.data[group].markets).forEach(({ id, match_id: marketMatchId, selections }) => {
            if (marketMatchId !== matchId || id === primaryMarketId) {
                return;
            }

            (selections || []).forEach((selectionId) => {
                this.deleteById('selections', selectionId);
            });

            delete this.data[group].markets[id];
        });
    }

    onMatchCreated = (payload) => {
        const {
            team1, team2, id, provider_id: providerId,
        } = payload;

        const group = providers[providerId];

        this.log.debug('Match created', group, payload);

        if (team1 !== null) {
            this.data[group].teams[team1.id] = team1;
        }
        if (team2 !== null) {
            this.data[group].teams[team2.id] = team2;
        }
        this.data[group].matches[id] = payload;
    }

    onMarketCreated = (payload) => {
        (Array.isArray(payload) ? payload : [payload]).forEach(this.marketCreated);
    }

    marketCreated = (data, retries = 0) => {
        const { id, selections, match_id: matchId } = data;

        const match = this.getById('matches', matchId);

        if (!match || match.primary_market_id !== id) {
            if (retries >= 5) {
                return;
            }

            // in some cases match created event is not arrived yet so we retry it to make sure market will be created properly
            // match primary market id might be also missing so we retry a couple of times
            setTimeout(() => this.marketCreated(data, retries + 1), 1000);
            return;
        }

        const group = providers[match.provider_id];

        this.log.debug('Market created', group, data);

        const selectionIds = [];
        (selections || []).forEach((selection) => {
            this.data[group].selections[selection.id] = selection;
            selectionIds.push(selection.id);
        });

        this.data[group].markets[id] = { ...data, selection_ids: selectionIds };
        delete this.data[group].markets[id].selections;
    }

    deleteMatch(matchId) {
        const match = this.getById('matches', matchId);

        if (!match) {
            return;
        }

        const group = providers[match.provider_id];
        const market = this.data[group].markets[match.primary_market_id];
        if (market) {
            market.selection_ids.forEach((selectionId) => {
                if (this.data[group].selections[selectionId]) {
                    delete this.data[group].selections[selectionId];
                }
            });

            delete this.data[group].markets[match.primary_market_id];
        }

        const teams = [match.team1?.id, match.team2?.id].filter(team => team);
        delete this.data[group].matches[matchId];
        this.clearTeams(teams, group);
    }

    clearTeams(teamIds, group) {
        teamIds.forEach((teamId) => {
            if (!Object.values(this.data[group].matches).some(match => match.team1?.id === teamId || match.team2?.id === teamId)) {
                delete this.data[group].teams[teamId];
            }
        });
    }

    async stop() {
        clearTimeout(this.roundCheckTimer); // stop timer just in case
        await super.stop();
    }

    async simpleNext() {
        clearTimeout(this.roundCheckTimer);
        this.roundCheckTimer = setTimeout(async () => {
            this.log.error('Matchbetting round is stuck, no round run in 60 seconds.');
        }, 60000);

        this.loadMatchData(); // run in background, do not use await
        await super.simpleNext();

        this.logDataMemoryFootprint();
    }

    logDataMemoryFootprint(level = 'info') {
        Object.entries(this.data).forEach(([group, data]) => {
            const params = [
                'group=', group,
            ];
            this.objects.forEach((object) => {
                params.push(`${object}=`, Object.keys(data[object]).length);
            });

            this.log[level]('Data memory size:', ...params);
        });
    }

    async loadMatchData(retry = 0) {
        this.log.debug('loadMatchData', 'this.roundsSinceLastDataSync=', this.roundsSinceLastDataSync, 'this.initialDataLoaded=', this.initialDataLoaded, 'this.dataLoading=', this.dataLoading);
        if (!retry && ((this.initialDataLoaded && this.roundsSinceLastDataSync < DATA_SYNC_ROUNDS) || this.dataLoading)) {
            this.roundsSinceLastDataSync += 1;

            if (this.roundsSinceLastDataSync > DATA_SYNC_ROUNDS) {
                this.log.error('Matchbetting pod still loading matches', this.roundsSinceLastDataSync, 'rounds done while max rounds should be', DATA_SYNC_ROUNDS);
            }

            return;
        }

        try {
            const startedAt = Date.now();
            this.log.warn('Syncing match betting data', 'retry=', retry);

            this.roundsSinceLastDataSync = 0;
            this.dataLoading = true;
            this.data = dataObject();
            await Promise.all(Object.keys(providers).map(provider => this.fetchData(1, provider)));
            this.initialDataLoaded = true;
            this.initialLoading = false;
            this.dataLoading = false;

            this.log.warn('Syncing match betting data done in ', Date.now() - startedAt, 'ms');
            this.logDataMemoryFootprint('warn');
        } catch (err) {
            this.loadMatchData(retry + 1); // retry immediately, but without await
            this.log.error('FAILED TO LOAD MATCH BETTING DATA', err?.message, err?.stack);
            await handleException(err, 'Matchbetting namespace init', 'Failed to load match betting data');
        }
    }

    async fetchData(page, provider) {
        const providerKey = providers[provider];
        this.log.info('Loading match betting data', 'page=', page, 'provider=', provider, 'providerKey=', providerKey);

        const result = await internalApiRequest(`/match-betting/flattened?per_page=500&page=${page}&provider_id=${provider}`, null, null, { method: 'GET' });
        const { data, last_page: lastPage } = result;

        this.objects.forEach((object) => {
            this.data[providerKey][object] = { ...this.data[providerKey][object], ...data[object] };
        });

        if (page !== 1 || lastPage === page) {
            return;
        }

        // if there are more than 1 page, load rest in parallel
        const pages = [];
        for (let i = page + 1; i <= lastPage; i++) {
            this.log.info('Loading page ', i, ', total', lastPage, 'provider=', providerKey);
            pages.push(this.fetchData(i, provider));
        }

        await Promise.all(pages);
    }
}
