import SocketUser from '../../lib/user';

export default class MatchBettingUser extends SocketUser {
    attachListeners() {
        super.attachListeners();
        this.attachListener('sync', this.onSync);
    }

    onSync = (gameGroup) => {
        if (!this.server.data[gameGroup]) {
            this.socket.emitError('Invalid game group');
            return;
        }

        if (this.server.dataLoading) {
            this.socket.emit('data', { loading: true, gameGroup });
            return;
        }

        this.socket.emit('data', this.server.data[gameGroup]);
    }
}
