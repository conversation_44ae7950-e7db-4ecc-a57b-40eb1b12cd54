import SocketUser from '../../lib/user';

export default class NotificationsUser extends SocketUser {
    itemDelayTimers = [];

    async onAfterIdentify() {
        await super.onAfterIdentify();

        if (!this.isAuthenticated) {
            return;
        }

        if (this.socket.pendingNotifications
            && Array.isArray(this.socket.pendingNotifications)
            && this.socket.pendingNotifications.length > 0
        ) {
            this.socket.pendingNotifications.forEach(({ e, data, broadcast }) => {
                this.log.debug('emit pending notification', e, 'broadcast=', broadcast);
                if (broadcast) {
                    this.socket.notification.broadcast.emit(e, data);
                } else {
                    this.socket.notification.emit(e, data);
                }
            });

            delete this.socket.pendingNotifications;
        }
        this.socket.emit('balance', {
            balance: this.user.balance,
            balances: this.user.balances,
        });
    }
}
