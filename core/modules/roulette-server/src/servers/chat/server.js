import Server from '../../lib/server';
import ChatMaster from './lib/ChatMaster';
import Chat<PERSON><PERSON><PERSON> from './lib/ChatWorker';

export default class ChatServer extends Server {
    WorkerModuleClass = ChatWorker

    MasterModuleClass = ChatMaster

    enableOnlineCounts = true

    get chat() {
        return this.slaveModule;
    }

    get chatMaster() {
        return this.masterModule;
    }

    async onRemoveExpiredChatRoom(data) {
        const { roomHash } = data;
        await this.chat.roomManager.destroyRoom(roomHash);
    }

    getCurrentState(user) {
        let room = this.chat.roomManager.getRoomByHash(user.room);
        if (!room) {
            room = this.chat.roomManager.getRoomByHash('0'); // Fallback to the default room
        }
        const { minBetToChat } = room;
        const chatHistoryForRoom = room.getHistory();

        return {
            ...super.getCurrentState(user),
            chat: chatHistoryForRoom,
            min_bet_to_chat: minBetToChat,
        };
    }

    onGetChatServerState() {
        return (this.isInitialized || this.isMaster)
            ? { blockedWords: this.module?.blockedWords }
            : null;
    }
}
