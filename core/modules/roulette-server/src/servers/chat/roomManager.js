import { internalApiRequest, logger } from '../../helpers';
import Room from './room';

const INTERNAL_API_GET_CHAT_ROOMS = 'chat-rooms';
const INTERNAL_API_POST_GET_CHAT_ROOM = 'get-chat-room';

class RoomManager {
    constructor(chatBase) {
        this.chatManager = chatBase;
        this.log = logger('room-manager');
        this.roomsByHash = new Map();
    }

    async initializeRooms() {
        try {
            const response = await internalApiRequest(INTERNAL_API_GET_CHAT_ROOMS);
            const rooms = response.data;

            await Promise.all(rooms.map(async (roomData) => {
                const room = new Room({
                    ...roomData,
                    minBetToChat: roomData.min_bet_to_chat,
                    minDepositToChat: roomData.min_deposit_to_chat,
                });
                await room.loadChatHistory();
                this.roomsByHash.set(room.hash, room);
                this.createRoomThrottler(room.hash);
            }));
            this.log.info('Rooms have been initialized and cached.');
            return true;
        } catch (error) {
            this.log.error('Failed to initialize rooms:', error);
            return false;
        }
    }

    removeMessageFromRooms(id) {
        this.roomsByHash.forEach((room) => {
            room.removeMessageById(id);
        });
    }

    getAllRooms() {
        return Array.from(this.roomsByHash.values());
    }

    getRoomByHash(hash) {
        return this.roomsByHash.get(hash);
    }

    async fetchChatRoomByHash(hash) {
        try {
            const response = await internalApiRequest(INTERNAL_API_POST_GET_CHAT_ROOM, { hash });
            const roomData = response.data;
            const room = new Room({
                ...roomData,
                minBetToChat: roomData.min_bet_to_chat,
                minDepositToChat: roomData.min_deposit_to_chat,
            });
            await room.loadChatHistory();
            this.roomsByHash.set(room.hash, room);
            this.createRoomThrottler(room.hash);
            return true;
        } catch (error) {
            this.log.error('Failed to fetch chat room:', error);
            return false;
        }
    }

    async destroyRoom(hash) {
        const room = this.getRoomByHash(hash);
        if (room) {
            this.roomsByHash.delete(hash);
            room.deleteHistory();
            await this.chatManager.destroyThrottler(`room${hash}`);
            this.log.info(`Room with id ${hash} has been destroyed.`);
            // TODO: kick user out of the room: https://moonrailteam.atlassian.net/browse/MP-4742
        } else {
            this.log.info(`Room with id ${hash} does not exist.`);
        }
    }

    createRoomThrottler(hash) {
        this.chatManager.createThrottler(`room${hash}`, this.chatManager.getMessageQueueFlusher(hash), { timeout: 150, maxCount: 20 });
    }

    addMessageToRoom(hash, message) {
        const room = this.getRoomByHash(hash);
        if (room) {
            room.addMessage(message);
        }
    }

    async addToHistory(hash, message) {
        const room = this.getRoomByHash(hash);
        if (room) {
            room.addToHistory(message);
        }
    }
}

module.exports = RoomManager;
