import map from 'async/map';
import client from '../../lib/redis';
import { handleException, setImmediatePromise } from '../../helpers';

class Room {
    constructor({
        hash,
        name,
        history = [],
        minBetToChat = [],
        minDepositToChat = [],
    }) {
        this.hash = hash;
        this.name = name;
        this.history = history;
        this.minBetToChat = minBetToChat;
        this.minDepositToChat = minDepositToChat;
    }

    addMessage(message) {
        this.history.push(message);
        if (this.history.length > 20) {
            this.history.shift();
        }
    }

    async addToHistory(message) {
        try {
            await setImmediatePromise();
            await client.rpush(`chat:history:${this.hash}`, JSON.stringify(message));
        } catch (err) {
            await handleException(err, 'Room.addToHistory()');
        }
    }

    getHistory() {
        return this.history;
    }

    setHistory(newHistory) {
        this.history = newHistory;
    }

    deleteHistory() {
        this.history = [];
    }

    removeMessageById(msgId) {
        const filteredHistory = this.history.filter(msg => msg.id !== msgId);
        const messageRemoved = filteredHistory.length !== this.history.length;
        this.history = filteredHistory;
        return messageRemoved;
    }

    async loadChatHistory() {
        try {
            const messages = await client.lrange(`chat:history:${this.hash}`, -100, -1);
            await this.processHistoryMessages(messages);
        } catch (err) {
            await handleException(err, 'Room.loadChatHistory()');
        }
    }

    async processHistoryMessages(messages) {
        await setImmediatePromise();
        await map(messages, async (rawMessage) => {
            await setImmediatePromise();
            const message = JSON.parse(rawMessage);
            const isMuted = Boolean(await client.get(`csgoempire_chat_mute_${message.uid}`));
            const isRemoved = Boolean(await client.sismember('csgoempire_chat_removed', message.id));
            if (isMuted || isRemoved || message.filtered) {
                return;
            }

            this.addMessage(message);
        });
    }
}

module.exports = Room;
