import ServerModule from '../../../lib/server-module';
import RoomManager from '../roomManager';
import client from '../../../lib/redis';

export default class ChatBase extends ServerModule {
    blockedWords = [];

    constructor(server) {
        super(server);
        this.roomManager = new RoomManager(this);
    }

    static async create(server) {
        const chat = new this(server);
        if (!await chat.roomManager.initializeRooms()) {
            throw new Error('Failed to initialize chat rooms.');
        }
        chat.slowmode = !!parseInt(await client.get('chat:slowmode'));
        return chat;
    }

    addToHistory(room, msg) {
        this.roomManager.addMessageToRoom(room, msg);
    }

    onAddToHistory(from, messages) {
        messages.forEach(({ room, msg }) => {
            this.addToHistory(room, msg);
        });
    }

    onMuteUserFromHistory(from, uid) {
        const rooms = this.roomManager.getAllRooms();
        rooms.forEach((room) => {
            const chatHistory = room.getHistory();
            const filteredHistory = chatHistory.filter(chatMsg => chatMsg.uid !== uid);
            room.setHistory(filteredHistory);
        });
    }

    getMessageQueueFlusher = hash => async (messages) => {
        try {
            this.log.debug('Flush messages', messages.length, ' from room', hash);
            this.server.socket
                .to(hash)
                .emit('batch', messages);
            await this.server.internalRequest.send('addToHistory', messages.map(msg => ({ room: hash, msg })));
        } catch (error) {
            this.log.error('Failed to flush message queue', error);
        }
    }

    updateBlockedWordsList(blockedWordsList) {
        this.blockedWords = blockedWordsList;
    }

    onBlockedWordsUpdate(sender, data) {
        this.updateBlockedWordsList(data);
    }
}
