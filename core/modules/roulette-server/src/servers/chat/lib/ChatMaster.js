import { subscriber } from '../../../lib/redis';
import ChatBase from './ChatBase';
import { handleException, internalApiRequest } from '../../../helpers';

const INTERNAL_API_GET_BLOCKED_WORDS = process.env.INTERNAL_API_GET_BLOCKED_WORDS || '/get-chat-blocked-words';

export default class ChatMaster extends ChatBase {
    logSuffix = 'lib:ChatMaster';

    constructor(server) {
        super(server);
        this.log.debug('creating ChatMaster');

        subscriber.on('message', async (channel, message) => {
            if (channel !== 'site:broadcast') {
                return;
            }

            // legacy code, used backend scripts
            this.server.socket.emit('site:broadcast', message);
        });
        subscriber.subscribe('site:broadcast');
    }

    static async create(server) {
        // Call the parent class's static create method
        const chat = await super.create(server);
        try {
            const blockedWords = await this.fetchBlockedWordsList();
            if (blockedWords) {
                chat.updateBlockedWordsList(blockedWords);
            }
        } catch (err) {
            await handleException(err, 'CRITICAL', 'chat.fetchBlockedWordsList() failed on master.');
            throw err;
        }
        return chat;
    }

    async stop() {
        await super.stop();
        subscriber.unsubscribe('site:broadcast');
    }

    static async fetchBlockedWordsList() {
        const response = await internalApiRequest(INTERNAL_API_GET_BLOCKED_WORDS, null);

        if (!response.success || !response.data) {
            return null;
        }
        return response.data;
    }
}
