import { logger } from '../../../../helpers';

const log = logger('chat:words:blockedWords');

const containsBlockedWords = (message, blockedWordsList) => {
    if (!message) {
        return false;
    }

    // List of blocked words or patterns
    const blockedWords = blockedWordsList || [];

    log.debug('blockedWords.js: Current list of blocked words', blockedWords);

    // Check if any blocked word is in the message
    return blockedWords.some(({ word, is_regex: isRegex }) => {
        const pattern = isRegex ? new RegExp(word, 'i') : null;
        return pattern ? pattern.test(message) : message.toLowerCase().includes(word.toLowerCase());
    });
};

export default containsBlockedWords;
