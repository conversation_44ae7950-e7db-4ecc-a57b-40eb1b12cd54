import latinize from 'latinize';
import moment from 'moment-timezone';
import { Random, MersenneTwister19937 } from 'random-js';
import { knex } from '../../../database';
import client, { subscriber } from '../../../lib/redis';
import {
    handleException,
    toBoolean,
    setImmediatePromise,
    slack,
    sleep,
    getAppCurrency,
} from '../../../helpers';
import ChatBase from './ChatBase';
import User from '../../../database/User';
import { ClientError } from '../../../errors/client';
import findEmbedLinkData, { MESSAGE_EMBED_TYPE } from './links/embed';
import findTrustedLinks from './links/trustedLinks';
import containsBlockedWords from './words/blockedWords';

const random = new Random(MersenneTwister19937.autoSeed());

const SPAM_TYPES = {
    STEAM_ID: 'steam_id',
};

/* eslint-disable */
// noinspection NonAsciiCharacters
latinize.characters = Object.assign(latinize.characters, {
    'ℂ': 'C',
    'ί': 'i',
    'Ό': 'O',
    'Ҏ': 'P',
    'Ꮢ': 'R',
    'ℝ': 'R',
    'Р': 'P',
    'р': 'p',
    '5': '5',
    'і': 'i',
});
/* eslint-enable */

const getChatNotAllowedError = (user, userImmuneToLimits, minBetAmount, minDepositAmount) => {
    if (userImmuneToLimits) {
        return null;
    }

    const userReachedBetLimit = user.total_bet >= minBetAmount * 100;
    const userReachedDepositLimit = user.total_deposit >= minDepositAmount * 100;
    const requireBet = minBetAmount > 0;
    const requireDeposit = minDepositAmount > 0;
    // vars for message
    const minBet = minBetAmount.toFixed(2);
    const moreBetRequired = (((minBetAmount * 100) - user.total_bet) / 100).toFixed(2);
    const minDeposit = minDepositAmount.toFixed(2);
    const moreDepositRequired = (((minDepositAmount * 100) - user.total_deposit) / 100).toFixed(2);

    const { env, symbol, name } = getAppCurrency();

    if (requireBet && requireDeposit) {
        return userReachedBetLimit || userReachedDepositLimit
            ? null
            : `You must bet at least ${symbol}${minBet} or deposit at least ${symbol}${minDeposit} ${name} total in order to chat.${env !== 'duel' ? ` You need to bet ${symbol}${moreBetRequired} or deposit ${symbol}${moreDepositRequired} more ${name} to chat.` : ''}`;
    }

    if (requireBet) {
        return userReachedBetLimit
            ? null
            : `You must bet at least ${symbol}${minBet} ${name} total in order to chat.${env !== 'duel' ? ` You need to bet ${symbol}${moreBetRequired} more ${name} to chat.` : ''}`;
    }

    if (requireDeposit) {
        return userReachedDepositLimit
            ? null
            : `You must deposit at least ${symbol}${minDeposit} ${name} total in order to chat.${env !== 'duel' ? ` You need to deposit ${symbol}${moreDepositRequired} more ${name} to chat.` : ''}`;
    }

    return 'You are not allowed to chat.'; // technically this should not happening
};

/**
 * Should not be instantiated directly, but rather be called via the static `create()` function
 */
export default class ChatWorker extends ChatBase {
    logSuffix = 'lib:ChatWorker';

    constructor(server) {
        super(server);

        // legacy subscribers
        subscriber.on('message', async (channel, message) => {
            if (channel !== 'update:changeChatStatus') {
                return;
            }

            try {
                message = JSON.parse(message);
            } catch (err) {
                this.log.error(`Parsing JSON message errored: "${message}"`, err.message);
            }

            if (channel === 'update:changeChatStatus') {
                this.onChangeChatStatus(message);
                // purposely don't return
            }
            this.server.socket.local.emit(channel, message);
        });
        subscriber.subscribe('update:changeChatStatus');

        subscriber.on('pmessage', (pattern, channel, message) => {
            if (pattern !== 'live-ticker:*' || channel.startsWith(server.socket.adapter.prefix)) return;

            this.server.socket.local.emit(channel, message);
        });
        subscriber.psubscribe('live-ticker:*');
    }

    static async create(server) {
        // Call the parent class's static create method
        const chat = await super.create(server);
        await chat.ready();
        return chat;
    }

    async ready() {
        const reply = await this.server.internalRequest.getMaster('getChatServerState');
        if (!reply || reply.blockedWords === null || reply.blockedWords === undefined) {
            this.log.info('Empty response from master, waiting...');
            await sleep(300);
            await this.ready();
            return;
        }

        this.updateBlockedWordsList(reply.blockedWords);
    }

    async stop() {
        await super.stop();
        subscriber.unsubscribe('update:changeChatStatus');
        subscriber.punsubscribe('live-ticker:*');
    }

    /**
     *
     * @param {string} data - A string representing an integer for global rooms, or a hash for other rooms.
     * @returns {Promise<void>}
     */
    async onChangeRoom(data) {
        /** @var {string} room */
        const { room } = data;

        const roomHashExistsInCache = this.chat.roomManager.getAllRooms().some(roomObj => roomObj.hash === room);

        if (!roomHashExistsInCache) {
            const roomExists = await this.chat.roomManager.fetchChatRoomByHash(room);
            if (!roomExists) {
                this.socket.emitError(`The room does not exist: ${room}`, false);
                return;
            }
        }

        if (this.room === room || this.secondaryRooms.includes(room)) {
            // Already in this room
            return;
        }
        const isGlobalRoom = /^\d+$/.test(room);
        const { socket } = this;

        if (isGlobalRoom) {
            // Global rooms are always limited to 1 at a time.
            socket.leave(this.room);
            socket.join(room);
            this.room = room;
        } else if (this.secondaryRooms.length >= 10) {
            socket.emitError('You may only join up to 10 temporary rooms at a time.', true);
            return;
        } else {
            socket.join(room);
            this.secondaryRooms.push(room);
        }

        this.refresh();
        // Inform the frontend of the chat history for this room
        const roomHistory = this.chat.roomManager.getRoomByHash(room)?.getHistory();
        socket.emit('room', {
            room,
            log: roomHistory || [],
        });
    }

    async onLeaveRoom(data) {
        const { room } = data;
        const { socket } = this;

        if (this.room === room) {
            // Global rooms are only left when joining a different global room.
            socket.emitError('You cannot manually leave a global room.', false);
            return;
        }

        if (this.secondaryRooms.includes(room)) {
            socket.leave(room);
            this.secondaryRooms = this.secondaryRooms.filter(r => r !== room);
            this.refresh();
        }
    }

    async onUpdateUsername() {
        await this.getUpdatedUsername();
    }

    async onChatSend(data) {
        // this = User
        if (this.last_msg_time > Date.now() - 500) {
            this.log.info(`Filtering too soon sent message by user #${this.id}`);
            return Promise.resolve();
        }
        this.last_msg_time = Date.now();
        let { msg } = data;
        // Default to global room if a room target was not specified.
        const room = data.room ?? this.room;

        if (!msg || typeof msg !== 'string') return Promise.resolve();
        // Replace character intended to be combined with another character (e.g. accents, umlauts, enclosing boxes, etc.)
        msg = msg.replace(/\p{M}/gu, '').trim();
        if (msg.length === 0) return Promise.resolve();
        const { socket } = this;
        if (msg.startsWith('/') || msg.startsWith('!')) {
            const chatCommands = [
                'slowmode',
                'adminrefresh',
                'disable',
                'enable',
            ];
            const commandMatches = /([!/])(\w+)( ?)/g.exec(msg);
            if (commandMatches) {
                const command = commandMatches[2].toLowerCase();
                if (!chatCommands.includes(command)) {
                    return socket.emitError('This chat command is currently not supported.', false);
                }
                switch (command) {
                    case 'slowmode': {
                        if (!this.mod) {
                            return socket.emitError('You don\'t have the required permissions to use this command.', false);
                        }
                        this.chat.slowmode = !parseInt(await client.get('chat:slowmode'));
                        await client.set('chat:slowmode', this.chat.slowmode ? 1 : 0);
                        await this.server.internalRequest.send('setSlowmodeState', { slowmode: this.chat.slowmode });
                        return socket.emit('sys', {
                            msg: `Slow mode is now ${(this.chat.slowmode ? 'on' : 'off')}.`,
                        });
                    }
                    case 'adminrefresh':
                        if (!this.admin) {
                            return socket.emitError('You don\'t have the required permissions to use this command.', false);
                        }
                        socket.notification.broadcast('refresh');
                        return socket.emit('sys', {
                            msg: 'Broadcasted refresh event to all users.',
                        });
                    case 'disable':
                        if (!this.admin) {
                            return socket.emitError('You don\'t have the required permissions to use this command.', false);
                        }
                        await client.set('chat:disabled', 1);
                        return socket.emit('sys', {
                            msg: 'Chat disabled.',
                        });
                    case 'enable':
                        if (!this.admin) {
                            return socket.emitError('You don\'t have the required permissions to use this command.', false);
                        }
                        await client.del('chat:disabled');
                        return socket.emit('sys', {
                            msg: 'Chat enabled.',
                        });
                    default:
                        return Promise.resolve();
                }
            }
            return socket.emitError('This chat command is currently not supported.', false);
        }

        let userImmuneToLimits = this.hasPrivilegedRole;
        if (!userImmuneToLimits && (this._chat_disabled === true || await client.exists('chat:disabled'))) {
            if (this._chat_disabled !== true) {
                this._chat_disabled = true;
                setTimeout(() => {
                    delete this._chat_disabled;
                }, 10 * 1000);
            }
            return socket.emitError('Chat is temporarily disabled.', false);
        }

        const disableLimits = toBoolean(process.env.DISABLE_CHAT_LIMITS);

        if (disableLimits) {
            this.log.warn('Chat limits are disabled');
        }

        const roomDetails = this.chat.roomManager.getRoomByHash(room);
        if (!roomDetails) {
            return socket.emitError('Room not found.');
        }

        if (!disableLimits) {
            const userLastMessageTime = this.lastMessageTimestamps[room];
            if (userLastMessageTime && Date.now() - userLastMessageTime < (60 * 60 * 1000)) {
                // If this user successfully sent a message to this room in the past hour, exempt them from bet/deposit requirements.
                userImmuneToLimits = true;
            }

            const minBetAmount = roomDetails.minBetToChat;
            const minDepositAmount = roomDetails.minDepositToChat;

            const chatNotAllowedError = getChatNotAllowedError(this, userImmuneToLimits, minBetAmount, minDepositAmount);

            if (chatNotAllowedError) {
                return socket.emitError(chatNotAllowedError, false);
            }
        }

        const chatHistory = this.chat.roomManager.getRoomByHash(room)?.getHistory();
        if ((!this.muted || this.muted.mutedUntil < Math.round(Date.now() / 1000)) && chatHistory) {
            const lastUserMessages = chatHistory.slice(-10).filter(message => this.id === message.uid);
            if (lastUserMessages.length > 6 && Math.round(Date.now() / 1000) - lastUserMessages[0].timestamp < 15) {
                return this.chat.onChatMute.call(this, {
                    uid: this.id,
                    duration: 15,
                    reason: 1,
                    room_hash: room,
                });
            }
        }

        if (this.muted && this.muted.mutedUntil > Math.round(Date.now() / 1000)) {
            const muteDate = new Date(this.muted.mutedUntil * 1000);
            const muteTimeFromNow = moment(muteDate).fromNow();

            const messageLocalized = {
                key: this.muted.muteReason ? 'modals.mute.errors.title' : 'modals.mute.errors.title_no_reason',
                params: {
                    reason: this.muted.muteReason,
                    rawExpiresAt: muteDate,
                    timeLeftMessage: muteTimeFromNow,
                },
            };

            const reasonText = this.muted.muteReason ? ` Reason: "${this.muted.muteReason}"` : '';
            const error = new ClientError(`Your mute expires ${muteTimeFromNow}.${reasonText}`)
                .setLocalizedMessage(messageLocalized.key, messageLocalized.params);

            return socket.emitError(error);
        }
        if (this.referral_code && msg.toLowerCase().includes(this.referral_code.toLowerCase())) {
            return socket.emitError('You may not send a message with your referral code.', false);
        }

        if (this.chat.slowmode && !(this.mod || this.super_mod || this.admin)) {
            if (this.messageTimestamps.length > 0) {
                const lastTimestamp = this.messageTimestamps[this.messageTimestamps.length - 1];
                if (Date.now() - lastTimestamp <= 3000) {
                    return socket.emitError('Slow mode is on. You can only send 1 message every 3 seconds.', false);
                }
            }
        }

        msg = msg.substring(0, 200);

        try {
            this.log.debug('Checking for blocked words', this.server.module?.blockedWords);
            // Check if any of the words the user typed are blacklisted
            const hasBlockedWords = containsBlockedWords(msg, this.server.module?.blockedWords);
            if (hasBlockedWords) {
                return socket.emitError('Your message contains prohibited texts or phrases that violate our chat rules.', false);
            }
        } catch (error) {
            this.log.error('Error while checking for blocked words', error);
        }

        let mentions = [];
        const mentionMatches = msg.match(/@([0-9]{0,11})/g);
        if (mentionMatches) {
            const userIds = mentionMatches.map(mention => mention.replace('@', ''));

            const users = await User.query()
                .select('id', 'steam_name', 'avatar')
                .whereIn('id', userIds);

            mentions = users.map(({ id: uid, steam_name: steamName, avatar }) => ({
                uid,
                // Trim csgoempire.com from the names.
                // Also replace | and - and / on the start and end of the strings
                // since name | csgoempire.com looks stupid without csgoempire.com.
                // Some usernames are just csgoempire.com, they will be left empty. Fallback those to "----"
                steam_name: steamName
                    .replace(/csgoempire\.com/i, '')
                    .trim()
                    .replace(/^\|+|\|+$/g, '')
                    .replace(/^-+|-+$/g, '')
                    .replace(/^\/+|\/+$/g, '') || '----',
                avatar,
            }));
        }

        await this.loadLevels();
        await setImmediatePromise();

        const msgObj = {
            id: random.uuid4(),
            room,
            uid: this.id,
            name: this.steam_name,
            avatar: this.avatar,
            helper_mod: this.helper_mod,
            mod: this.mod,
            qa: this.qa,
            super_mod: this.super_mod,
            mod_manager: this.manager_mod,
            admin: this.admin,
            verified: this.verified,
            hide_verified_icon: this.hide_verified_icon,
            timestamp: Math.round(Date.now() / 1000),
            msg,
            lvl: this.lvl,
            badge_text: this.badge_text,
            badge_text_localized: this.badge_text_localized,
            badge_color: this.badge_color,
            hide_rank: this.hide_rank,
            mentions,
            encuid: this.encuid || null,
            badges: this.badges,
        };

        try {
            const embed = await findEmbedLinkData(msg);

            if (embed) {
                msgObj.embed_link = embed;

                if (embed.type === MESSAGE_EMBED_TYPE.ITEM && !disableLimits) {
                    // TODO: Add room type for Trading room: https://moonrailteam.atlassian.net/browse/MP-5572
                    const isTradingRoom = roomDetails.name.toLowerCase().includes('trading');
                    if (!isTradingRoom) {
                        return socket.emitError('You can only advertise item links in the Trading Room.', false);
                    }
                }
            }
        } catch (error) {
            // Catch errors so item gets sent always
            this.log.error('Error while finding embed data', error);
        }

        try {
            // Find all links from the message and find the ones that are from trusted domains
            const trustedLinks = await findTrustedLinks(msg);

            if (trustedLinks.length > 0) {
                msgObj.trusted_links = trustedLinks;
            }
        } catch (error) {
            this.log.error('Error while finding trusted links', error);
        }

        if (this.lastMessage === msg) {
            this.log.info(`Filtering identical last message by user #${this.id}: "${msgObj.msg}"`);
            return Promise.resolve();
        }

        this.lastMessage = msg;

        await setImmediatePromise();

        const spamFilterReason = this.hasPrivilegedRole ? null : ChatWorker.checkChatMsgForSpam(msgObj.msg);
        if (spamFilterReason) {
            const logMsg = `Filtering message by user #${this.id} [${this.ip}]: "${msgObj.msg}" (${spamFilterReason.message})`;
            this.log.info(logMsg);
            await slack(logMsg);

            if (spamFilterReason.reason === SPAM_TYPES.STEAM_ID) {
                const errorOptions = {
                    data: {
                        reason: 'spam',
                        type: spamFilterReason.reason,
                    },
                    popup: false,
                };
                const error = new ClientError('You are not allowed to paste your SteamID64. View the chat rules: https://csgoempire.com/chat-rules', 400, errorOptions);

                return socket.emitError(error);
            }

            // Send the message only for the user who sent it if there is no matching reason for spam flagging
            await this.server.internalRequest.slave('emitToIp', {
                ip: this.ip,
                event: 'msg',
                data: msgObj,
            });
        } else {
            await this.chat.roomManager.addToHistory(room, msgObj);
            this.chat.throttler(`room${room}`).push(msgObj);
        }

        return Promise.resolve();
    }

    /**
     * @param {string} message
     * @returns {null | { type: string; reason: string }} Reason for filtering as a string, or null if shouldn't filter
     */
    static checkChatMsgForSpam(message) {
        const match = message.match(/(76561\d{12}|\[U:1:\d+]|STEAM_[01]:[01]:\d+)/i);
        if (match) {
            return { message: `Message contains SteamID: ${match[0]}`, reason: SPAM_TYPES.STEAM_ID };
        }

        return null;
    }

    async onChangeChatStatus(data) {
        this.log.debug('onChangeChatStatus', data);

        const userId = data.uid;
        const { muteReason, mutedUntil, duration } = data;
        // Duration is received as seconds
        const durationInMin = duration / 60;

        if (!userId || typeof userId !== 'number' || typeof mutedUntil !== 'number') return;

        if (!this.server.isUserConnected(userId)) return;

        this.onMuteUser({
            targetId: userId,
            durationInMin,
            mutedUntil,
            muteReason,
        });
    }

    async onChatMute(data) {
        // this = User
        if (this.last_mute > Date.now() + (60 * 1000)) return Promise.resolve(); // minimum mute time is 1 minute

        const MUTE_REASONS = [
            'Other',
            'Spamming',
            'Begging',
            'Advertising',
            'Predictions only in predictions room',
            'Incorrect chat usage',
            'Support inquiries in public chat',
        ];

        const { uid: userId, reason } = data;

        if (!userId || typeof userId !== 'number' ||
            typeof reason !== 'number') return Promise.resolve();

        this.last_mute = Date.now();

        // TODO: Don't increment right away, just get current value and only increment after mute is successful.
        // TODO: Lastly, don't increment if the user is already muted.
        const numMutes = parseInt(await client.incr(`num-mutes:${userId}`));
        const maxMute = 525600; // 365 days in minutes
        const ttl = maxMute * 60; // TTL is max mute time in seconds
        await client.expire(`num-mutes:${userId}`, ttl);
        const multiplier = 10; // 10 min
        // Increase mute duration based on number of previous mutes
        const durationInMin = Math.min(multiplier * (2 ** (numMutes - 1)), maxMute);
        const durationInSec = durationInMin * 60;

        const mutedUntil = Math.min(durationInSec + Math.round(Date.now() / 1000), (2 ** 31) - 1);
        const muteReason = MUTE_REASONS[reason];

        this.log.debug('onChatMute numMutes=', numMutes, 'duration=', durationInMin, 'mutedUntil=', mutedUntil, 'muteReason=', muteReason);
        await this.server.chat.onMuteUser({
            user: this,
            targetId: userId,
            durationInMin,
            mutedUntil,
            muteReason,
        });

        const isMutedInRoom = this.chat.roomManager.getAllRooms().map(room => room.hash).includes(data.room_hash);
        this.log.debug('isMutedInRoom', isMutedInRoom, data);
        if (isMutedInRoom > -1) {
            const room = data.room_hash;
            let chatChecked = 0;
            let userChatChecked = 0;
            let modChatChecked = 0;
            const logs = {
                userLog: [],
                modLog: [],
            };

            const roomData = this.chat.roomManager.getRoomByHash(room);
            // Inverse for loop as we only want to use check 100 latest
            for (let i = roomData.getHistory().length; i--;) {
                const msg = roomData.getHistory()[i];
                if (msg.uid === userId && userChatChecked < 10) {
                    // Push to userLog
                    logs.userLog.push(msg);
                    userChatChecked++;
                }
                if (msg.uid === this.id && modChatChecked < 10) {
                    // Push to modLog
                    logs.modLog.push(msg);
                    modChatChecked++;
                }
                chatChecked++;
                // Break if we found 10 chats for each or if we have checked 100 chat logs
                if ((modChatChecked === 10 && userChatChecked === 10) || chatChecked === 100) {
                    break;
                }
            }

            await this.server.chat.addToMuteLog({
                userId: this.id,
                targetId: userId,
                muteReason,
                logs,
                durationInMin,
            });
        }

        await this.server.internalRequest.send('muteUserFromHistory', userId);

        return Promise.resolve();
    }

    async addToMuteLog({ // eslint-disable-line class-methods-use-this
        userId,
        targetId,
        muteReason,
        logs,
        durationInMin,
    }) {
        try {
            await knex('chat_mute_logs').insert({
                user_id: userId,
                target_id: targetId,
                reason: muteReason,
                logs: JSON.stringify(logs),
                duration: durationInMin,
            });
            this.log.debug('Added to mute log', userId, targetId, muteReason, durationInMin);
        } catch (err) {
            handleException(err, 'Chat.addToMuteLog()', 'Failed to set user chat_mute_this.log.', {
                user_id: userId,
                target_id: targetId,
                reason: muteReason,
                logs: JSON.stringify(logs),
                durationInMin,
            });
        }
    }

    async onMuteUser({
        user,
        targetId,
        durationInMin,
        mutedUntil,
        muteReason,
    }) {
        try {
            const mute = {
                uid: targetId,
                mutedUntil,
                muteReason,
            };
            this.server.socket.emit('mute', mute);
            await this.server.internalRequest.sendUser(targetId, 'applyMute', mute);
        } catch (error) {
            this.log.error('onMuteUser error', error);
            return;
        }

        if (mutedUntil === 0 || durationInMin === 0) {
            // Unmute
            await client.del(`csgoempire_chat_mute_${targetId}`);
            this.log.info(`User ${targetId} was unmuted from chat`);
            return;
        }

        const durationInSec = durationInMin * 60;

        try {
            this.log.debug('save chat mute to redis for target', targetId, 'expiring in', durationInSec);
            await client.setex(`csgoempire_chat_mute_${targetId}`, durationInSec, JSON.stringify({
                mutedUntil,
                muteReason,
            }));
            if (user) {
                await knex.raw('UPDATE users SET muted_until = ?, mute_reason = ? WHERE id = ?', [mutedUntil, muteReason, targetId]);
            }
        } catch (err) {
            handleException(err, 'ChatWorker.onMuteUser()', 'Failed to set user as muted.', {
                id: user.id,
                target: targetId,
                muted_until: mutedUntil,
                mute_reason: muteReason,
            });

            if (user) {
                user.socket.emitError('Failed to mute user. Please try again later.');
            }
            return;
        }

        if (!user) {
            return;
        }

        user.socket.emit('mute success');
    }

    onSetSlowmodeState(from, { slowmode }) {
        this.slowmode = slowmode;
    }
}

ChatWorker.latinize = latinize;
