export const removeUrlParams = url => url.split('?')[0].split('#')[0];

export const extractRootDomain = (url) => {
    const noProtocol = url.replace('http://', '').replace('https://', '');
    const noParams = removeUrlParams(noProtocol);
    const withoutSubDomain = noParams.split('/')[0].split('.').slice(-2).join('.').split(':')[0];
    return withoutSubDomain;
};

export const extractUrl = (message) => {
    // Note: Create regex inside function, or otherwise they don't work every time, as they remember the history
    const regex = new RegExp(
    // eslint-disable-next-line max-len
        /(?:(?:https?):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#/%=~_|$?!:,.]*\)|[A-Z0-9+&@#/%=~_|$])/,
        'igm',
    );
    const match = regex.exec(message)?.[0];
    return match;
};
