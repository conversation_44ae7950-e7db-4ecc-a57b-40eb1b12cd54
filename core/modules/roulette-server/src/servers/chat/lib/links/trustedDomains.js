import { internalApiRequest, logger } from '../../../../helpers';

import getInternalDomains from './internalDomains';

const log = logger('chat:links:trustedDomains');

// TODO: Move this static list into AP and create tool to add new domains
// https://moonrailteam.atlassian.net/browse/MP-5687
const fallbackList = [
    'steamcommunity.com',
    'steampowered.com',
    'store.steampowered.com',
    'hltv.org',
    'dust2.us',
    'faceit.com',
    'counter-strike.net',
    'spotify.com',
    'google.com',
    'youtube.com',
    'facebook.com',
    'instagram.com',
    'x.com',
    'twitter.com',
    'whatsapp.com',
    'wikipedia.org',
    'yahoo.com',
    'reddit.com',
    'yahoo.co.jp',
    'netflix.com',
    'qq.com',
    'live.com',
    'bing.com',
    'google.co.jp',
    'msn.com',
    'yahoo.co.jp',
    'linkedin.com',
    'instagram.com',
    'weibo.com',
    'vk.com',
    'yandex.ru',
    'google.de',
    'google.ru',
    'ebay.com',
    'reddit.com',
    'pinterest.com',
    'google.it',
    'microsoft.com',
    'github.com',
    'dailymotion.com',
    'dropbox.com',
    'stackoverflow.com',
    'apple.com',
    'google.es',
    'paypal.com',
    'wordpress.com',
    'tumblr.com',
    'blogspot.com',
    'imgur.com',
];

let domains = null;
let domainsFetchedAt = null;

const getTrustedDomains = async () => {
    try {
        const hourAgo = Date.now() - 1000 * 60 * 60;
        // Update links once an hour so we don't need server restart to update them
        const isOutdated = domainsFetchedAt && (domainsFetchedAt < hourAgo);

        if (domains && !isOutdated) {
            return domains;
        }

        const internalDomains = await getInternalDomains();

        const response = await internalApiRequest('/trusted-domains', null, null, { method: 'GET' });

        if (!response.success || !response.data) {
            return internalDomains;
        }

        ({ domains } = response.data);

        domainsFetchedAt = Date.now();

        const allDomains = [...domains, ...internalDomains];
        return allDomains;
    } catch (error) {
        log.error('getTrustedDomains', error);
        // Fallback to static list that has some common domains
        return ['csgoempire.com', 'duel.com', ...fallbackList];
    }
};

export default getTrustedDomains;
