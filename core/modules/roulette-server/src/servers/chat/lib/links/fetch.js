import { internalApiRequest, IS_PRODUCTION, logger } from '../../../../helpers';

const log = logger('chat:links:fetch');

const fetchWithLogger = async ({ url, key }) => {
    try {
        const response = await internalApiRequest(url, null, null, { method: 'GET' });

        if (!response.success || !response.data) {
            return null;
        }
        return response.data[key];
    } catch (error) {
        if (error.statusCode >= 500 || !IS_PRODUCTION) {
            log.error('fetchWithLogger', url, error);
        }
        // Catch errors so that we return null for items/battles that are not found.
        // This way frontend can show "X not found" message.
        return null;
    }
};

export const fetchCaseBattle = id => fetchWithLogger({ url: `/case-battle/${id}`, key: 'case_battle' });

export const fetchItem = id => fetchWithLogger({ url: `/trading/item/${id}`, key: 'item' });
