import { extractUrl, extractRootDomain } from './helpers';

describe('Chat Helpers', () => {
    describe('extractUrl', () => {
        it('should find Intercom URL', () => {
            const message = 'check out here: https://help.csgoempire.com/en/articles/5341467-how-does-the-p2p-player-to-player-system-work';
            const match = extractUrl(message);
            expect(match).toEqual('https://help.csgoempire.com/en/articles/5341467-how-does-the-p2p-player-to-player-system-work');
        });

        it('should find dev URL', () => {
            const message = 'play with me http://csgoempire.localhost:8081/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660 pls now';
            const match = extractUrl(message);
            expect(match).toEqual('http://csgoempire.localhost:8081/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660');
        });

        it('should find dev URL with URL parameters', () => {
            const message = 'play with me http://csgoempire.localhost:8081/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660?asd=true';
            const match = extractUrl(message);
            expect(match).toEqual('http://csgoempire.localhost:8081/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660?asd=true');
        });

        it('should find production URL', () => {
            const message = 'play with me https://csgoempire.com/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660';
            const match = extractUrl(message);
            expect(match).toEqual('https://csgoempire.com/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660');
        });

        it('should find dev namespace URL', () => {
            const message = 'play with me https://mp-2779.dev.csgoempire.com/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660';
            const match = extractUrl(message);
            expect(match).toEqual('https://mp-2779.dev.csgoempire.com/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660');
        });

        it('should find YouTube URL', () => {
            const message = 'watch this https://www.youtube.com/watch?v=dQw4w9WgXcQ';
            const match = extractUrl(message);
            expect(match).toEqual('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
        });
    });

    describe('extractRootDomain', () => {
        it('should work for Intercom URL', () => {
            const url = 'https://help.csgoempire.com/en/articles/5341467-how-does-the-p2p-player-to-player-system-work';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('csgoempire.com');
        });

        it('should work for dev URL', () => {
            const url = 'http://csgoempire.localhost:8081/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('csgoempire.localhost');
        });

        it('should work for dev URL with URL parameters', () => {
            const url = 'http://csgoempire.localhost:8081/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660?asd=true';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('csgoempire.localhost');
        });

        it('should work for production URL', () => {
            const url = 'https://csgoempire.com/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('csgoempire.com');
        });

        it('should work for dev namespace URL', () => {
            const url = 'https://mp-2779.dev.csgoempire.com/case-battles/game/9e04ba41b7e7a125c1e383080ac1e660';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('csgoempire.com');
        });

        it('should work for YouTube URL', () => {
            const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('youtube.com');
        });

        it('should work for URL with hash parameters', () => {
            const url = 'https://csgoempire.com#hash=123';
            const hostname = extractRootDomain(url);
            expect(hostname).toEqual('csgoempire.com');
        });
    });
});
