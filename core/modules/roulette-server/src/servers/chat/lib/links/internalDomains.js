import { internalApiRequest, logger } from '../../../../helpers';

const log = logger('chat:links:internalDomains');

let domains = null;

const getInternalDomains = async () => {
    try {
        if (domains) {
            return domains;
        }

        const response = await internalApiRequest('/domains', null, null, { method: 'GET' });

        if (!response.success || !response.data) {
            return null;
        }

        ({ domains } = response.data);

        return domains;
    } catch (error) {
        log.error('getInternalDomains', error);
        // Fallback to the production domains that won't be changed for sure
        return ['csgoempire.com', 'duel.com'];
    }
};

export default getInternalDomains;
