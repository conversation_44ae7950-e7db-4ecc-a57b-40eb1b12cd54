import getTrustedDomains from './trustedDomains';

import { extractRootDomain, extractUrl } from './helpers';

const findTrustedLinks = async (message) => {
    if (!message) {
        return [];
    }

    // extractUrl matches only one link so split message into parts to find all links
    const linkMatches = message.split(' ').map(part => extractUrl(part)).filter(maybeUrl => !!maybeUrl);

    if (!linkMatches || linkMatches.length === 0) {
        return [];
    }

    const trustedDomains = await getTrustedDomains();

    const trustedLinks = linkMatches.filter((link) => {
        const domain = extractRootDomain(link);
        return trustedDomains.includes(domain);
    });

    return trustedLinks;
};

export default findTrustedLinks;
