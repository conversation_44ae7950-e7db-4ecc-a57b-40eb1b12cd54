import { fetchCaseBattle, fetchItem } from './fetch';
import parseIntercomData from './intercom';
import getInternalDomains from './internalDomains';
import { extractRootDomain, removeUrlParams, extractUrl } from './helpers';

// Match this to 'CHAT_MESSAGE_EMBED' in FE:
// packages/csgoempire/src/types/chatMessageEmbed.ts
export const MESSAGE_EMBED_TYPE = {
    INTERCOM_ARTICLE: 'intercom-article',
    ITEM: 'item',
    CASE_BATTLE: 'case-battle',
};

const findEmbedData = async (message) => {
    const url = extractUrl(message);
    if (!url) {
        return null;
    }

    // Note: Create regex inside function, or otherwise they don't work every time, as they remember the history
    const intercomEmpireRegex = new RegExp(
        /(https:\/\/help\.csgoempire\.com\/en\/articles\/\d*-\S*)/,
        'gm',
    );
    const intercomHelpRegex = new RegExp(
        /(https:\/\/intercom\.help\/csgoempire\/en\/articles\/\d*-\S*)/,
        'gm',
    );

    const intercomMatch = intercomEmpireRegex.exec(url)?.[0] || intercomHelpRegex.exec(url)?.[0];
    if (intercomMatch) {
        return {
            type: MESSAGE_EMBED_TYPE.INTERCOM_ARTICLE,
            url,
            data: parseIntercomData(url),
        };
    }

    // Address domain can be not just 'csgoempire.com', but e.g. 'mp-3391.dev.csgoempire.com' or 'staging.csgoempire.com'
    // Parse just the domain without subdomain(s)
    const domain = extractRootDomain(url);

    const currentDomains = await getInternalDomains();
    const isOurDomain = currentDomains.includes(domain);

    if (!isOurDomain) {
        return null;
    }

    const cleanUrl = removeUrlParams(url);

    const urlMatchWithoutProtocol = cleanUrl
        .replace('http://', '')
        .replace('https://', '');
    const cleanLink = removeUrlParams(urlMatchWithoutProtocol);
    const split = cleanLink.split('/');
    const [, path1, path2, path3] = split;

    if (path1 === 'item') {
        const data = await fetchItem(path2);
        return {
            type: MESSAGE_EMBED_TYPE.ITEM,
            url,
            data: data || null,
        };
    }

    if (path1 === 'case-battles' && path2 === 'game') {
        const data = await fetchCaseBattle(path3);
        return {
            type: MESSAGE_EMBED_TYPE.CASE_BATTLE,
            url,
            data: data || null,
        };
    }

    return null;
};

export default findEmbedData;
