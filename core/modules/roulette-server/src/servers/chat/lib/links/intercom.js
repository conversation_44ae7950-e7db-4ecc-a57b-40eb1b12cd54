// Most common question words that start our Help Center articles
const questionWords = [
    'how',
    'what',
    'what\'s',
    'why',
    'where',
    'when',
    'who',
    'which',
    'whom',
    'whose',
    'are',
    'do',
    'can',
];

const wordsToAllCaps = ['p2p', 'csgo', 'cs:go', 'cs2', 'i'];

// Since all we have access to is the slug of the URL, try to make it look somewhat like the original title
const parseTitle = (slug) => {
    const cleaned = slug
        .replace(/-/g, ' ')
        .toLowerCase()
        .split(' ')
        .map(word => (wordsToAllCaps.includes(word) ? word.toUpperCase() : word))
        .join(' ')
        // Word `What's` becomes `what s` in the slug
        .replace('what s ', 'what\'s ');
    const capitalised = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);

    const firstWord = cleaned.split(' ')[0];
    if (questionWords.includes(firstWord)) {
        return `${capitalised}?`;
    }

    return capitalised;
};

const parseIntercomData = (url) => {
    const idString = url.split('/articles/')[1].split('-')[0];
    const id = parseInt(idString);
    const slug = url.split(`${idString}-`)[1];
    const title = parseTitle(slug);

    return {
        id,
        slug,
        title,
    };
};

export default parseIntercomData;
