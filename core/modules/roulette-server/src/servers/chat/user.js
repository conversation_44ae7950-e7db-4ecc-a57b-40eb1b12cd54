import levenshtein from 'js-levenshtein';
import uniqBy from 'lodash/uniqBy';
import client from '../../lib/redis';
import SocketUser from '../../lib/user';

export default class ChatUser extends SocketUser {
    lastMessageTimestamps = {};

    messageTimestamps = [];

    muted = null;

    updateMessageTimestamps() {
        const now = Date.now();
        this.lastMessageTimestamps[this.room] = now;
        this.messageTimestamps.push(now);

        const expire = 60 * 60 * 2;
        const multi = client.multi();
        multi.hset(`chat:user:room-timestamps:${this.id}`, this.room, now);
        multi.lpush(`chat:user:global-timestamps:${this.id}`, now);
        multi.expire(`chat:user:global-timestamps:${this.id}`, expire);
        multi.expire(`chat:user:room-timestamps:${this.id}`, expire);
        return multi;
    }

    get chat() {
        return this.server.chat;
    }

    attachListeners() {
        super.attachListeners();
        this.attachListener('autocomplete', this.onAutocomplete);
    }

    toJSON() {
        return {
            ...super.toJSON(),
            room: this.room,
        };
    }

    attachAuthorizedListeners() {
        super.attachAuthorizedListeners();

        this.attachAuthorizedListener('chat send', this.server.chat.onChatSend.bind(this.userProxy));
        this.attachAuthorizedListener('update username', this.server.chat.onUpdateUsername.bind(this.userProxy));

        this.attachAuthorizedListener('chat_room_removed', this.server.onRemoveExpiredChatRoom.bind(this.userProxy));
        if (!this.user.hasPrivilegedRole) {
            return;
        }

        this.log.debug('attach admin commands for user', this.id);
        this.attachAuthorizedListener('chat mute', this.server.chat.onChatMute.bind(this.userProxy));
    }

    /**
     * Set initial global chat room
     * Locale/country-based room changing is handled on the frontend
     */
    setInitialChatRoom() {
        const rooms = this.server.chat.roomManager.getAllRooms();
        const highRollersRoom = rooms.find(room => room.name === 'Highrollers');

        if (this.user.total_bet >= 50000 * 100 && highRollersRoom) {
            this.room = highRollersRoom.hash;
        }
        // Default is already Main room
    }

    async onAfterIdentify() {
        try {
            if (this.authenticated) {
                const [lastMessageTimestamps, messageTimestamps, muted] = await Promise.all([
                    client.hgetall(`chat:user:room-timestamps:${this.id}`),
                    client.lrange(`chat:user:global-timestamps:${this.id}`, -100, 100),
                    client.get(`csgoempire_chat_mute_${this.id}`),
                ]);

                this.lastMessageTimestamps = lastMessageTimestamps;
                this.messageTimestamps = messageTimestamps;
                this.muted = muted ? JSON.parse(muted) : null;

                this.log.debug('Restored user message timestamps', this.id, lastMessageTimestamps, messageTimestamps);
                this.log.debug('Restored muted status', this.id, muted);
            }
        } catch (error) {
            this.log.error('Failed to fetch user message timestamps from redis', error);
        }

        this.setInitialChatRoom();

        await super.onAfterIdentify();

        this.socket.join(this.room);

        this.attachListener('change room', this.server.chat.onChangeRoom.bind(this.userProxy));
        this.attachListener('leave room', this.server.chat.onLeaveRoom.bind(this.userProxy));

        const chatHistory = this.server.chat.roomManager.getRoomByHash(this.room)?.getHistory();
        if (!chatHistory || chatHistory.length === 0) {
            this.log.warn('reload chat history since', this.room, 'not found from chat history');
        }
    }

    onAutocomplete = async (rawQuery) => {
        if (typeof rawQuery !== 'string' || rawQuery.length < 2) return;

        const query = rawQuery.trim().toLowerCase();

        const users = await this.presence.list();
        this.log.debug('connected users', users);

        let results = users
            .filter(user => user && user.meta && user.meta.steam_name
                && (
                    user.meta.steam_name.toLowerCase().replace(/\s+/g, '').includes(query)
                    || user.id.toString() === query
                ))
            .map(user => ({
                ...user.meta,
                l_distance: user.meta.id.toString() === query
                    ? 0
                    : levenshtein(user.meta.steam_name.toLowerCase(), query),
            }));

        // sort results by likeness to query
        // eslint-disable-next-line no-confusing-arrow,no-nested-ternary
        results.sort((a, b) => a.l_distance > b.l_distance ? 1 : ((b.l_distance > a.l_distance) ? -1 : 0));
        results = results.splice(0, 5).map(user => ({
            id: user.id,
            steam_name: user.steam_name,
            avatar: user.avatar,
        }));
        this.socket.emit('autocomplete', uniqBy(results, 'id'));
    }

    onApplyMute(data) {
        this.log.debug('Apply mute for user', this.id, data);
        this.muted = data.mutedUntil > 0 ? data : null;
    }
}
