import SocketUser from '../../lib/user';

export default class GameIntegrationUser extends SocketUser {
    secondaryRooms = [];

    attachListeners() {
        super.attachListeners();
        this.attachListener('open_game', this.onOpenGame.bind(this));
        this.attachListener('close_game', this.onCloseGame.bind(this));
    }

    async onOpenGame(gameCode) {
        if (!this.secondaryRooms.includes(gameCode)) {
            this.secondaryRooms.push(gameCode);
            await this.refresh();
        }
    }

    async onCloseGame(gameCode) {
        if (this.secondaryRooms.includes(gameCode)) {
            this.secondaryRooms = this.secondaryRooms.filter(room => room !== gameCode);
            await this.refresh();
        }
    }
}
