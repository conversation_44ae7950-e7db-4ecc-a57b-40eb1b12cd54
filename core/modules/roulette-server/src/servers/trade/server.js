import Server from '../../lib/server';

export default class TradeServer extends Server {
    recordDisconnectsInRedis = true;

    shouldGracefullyDisconnectSockets = true;

    constructor(nsp) {
        super(nsp);

        const opts = {
            timeout: 1000,
            maxCount: 20,
            flatten: false,
            auto: true,
        };
        this.createThrottler('deleted_item', { ...opts, flatten: true });
        this.createThrottler('updated_item', opts);
        this.createThrottler('new_item', opts);
        this.createThrottler('auction_update', opts);
        this.createThrottler('trade_status', { ...opts, timeout: 3000 });
        this.createThrottler('deposit_failed', { ...opts, timeout: 3000 });
        // Each event contains a deposit id, original, and current online status.  Very small :)
        this.createThrottler('updated_seller_online_status', {
            ...opts, timeout: 2000, maxCount: 50, flatten: true,
        });
    }
}
