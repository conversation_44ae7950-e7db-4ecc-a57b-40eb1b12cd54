import { logger } from '../../helpers';
import Filters, { THREE_WAY_FILTER_STATE, filterValidation, SEARCH_TYPE } from './filters';

const userMock = {
    log: logger('user-mock'),
};

describe('Filters', () => {
    it('should test price min', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ price_min: 10 });
        expect(filters.every({ purchase_price: 10 })).toBe(true);
        expect(filters.every({ purchase_price: 11 })).toBe(true);
        expect(filters.every({ purchase_price: 5 })).toBe(false);
    });

    it('should test price max', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ price_max: 10 });
        expect(filters.every({ purchase_price: 10 })).toBe(true);
        expect(filters.every({ purchase_price: 11 })).toBe(false);
        expect(filters.every({ purchase_price: 5 })).toBe(true);
    });

    it('should test wear min', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ wear_min: 0.10 });
        expect(filters.every({ wear: 0.10 })).toBe(true);
        expect(filters.every({ wear: 0.05 })).toBe(false);
        expect(filters.every({ wear: 0.11 })).toBe(true);
        expect(filters.every({ wear: 0.5 })).toBe(true);
        expect(filters.every({ })).toBe(true);
    });

    it('should test wear max', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ wear_max: 0.10 });
        expect(filters.every({ wear: 0.10 })).toBe(true);
        expect(filters.every({ wear: 0.05 })).toBe(true);
        expect(filters.every({ wear: 0.11 })).toBe(false);
        expect(filters.every({ wear: 0.5 })).toBe(false);
        expect(filters.every({ })).toBe(true);
    });

    it('should test wear names', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ wear_names: ['Minimal Wear'] });
        expect(filters.every({ wear_name: 'Factory New' })).toBe(false);
        expect(filters.every({ wear_name: 'Minimal Wear' })).toBe(true);
        expect(filters.every({ wear_name: 'Field-Tested' })).toBe(false);
        expect(filters.every({ wear_name: 'Well-Worn' })).toBe(false);
        expect(filters.every({ wear_name: 'Battle-Scarred' })).toBe(false);

        filters.setFilters({ wear_names: [] });
        expect(filters.every({ wear_name: 'Battle-Scarred' })).toBe(true);

        filters.setFilters({ wear_names: null });
        expect(filters.every({ wear_name: 'Battle-Scarred' })).toBe(true);

        filters.setFilters({ });
        expect(filters.every({ wear_name: 'Battle-Scarred' })).toBe(true);
    });

    it('should test auction not ended', () => {
        const filters = new Filters({ ...userMock, id: 258 });

        const item = {
            auction_highest_bidder: 258,
            auction_ends_at: (Date.now() / 1000) + 60,
        };

        filters.setFilters({ auction: THREE_WAY_FILTER_STATE.ANY });
        expect(filters.every(item)).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: null })).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: 2 })).toBe(true);

        filters.setFilters({ auction: THREE_WAY_FILTER_STATE.NO });
        expect(filters.every(item)).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: null })).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: 2 })).toBe(false);

        filters.setFilters({ auction: THREE_WAY_FILTER_STATE.YES });
        expect(filters.every(item)).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: null })).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: 2 })).toBe(true);
    });

    it('should test auction ended', () => {
        const filters = new Filters({ ...userMock, id: 258 });

        const item = {
            auction_highest_bidder: 258,
            auction_ends_at: (Date.now() / 1000) - 60,
        };

        filters.setFilters({ auction: THREE_WAY_FILTER_STATE.ANY });
        expect(filters.every(item)).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: null })).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: 2 })).toBe(false);

        filters.setFilters({ auction: THREE_WAY_FILTER_STATE.NO });
        expect(filters.every(item)).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: null })).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: 2 })).toBe(false);

        filters.setFilters({ auction: THREE_WAY_FILTER_STATE.YES });
        expect(filters.every(item)).toBe(true);
        expect(filters.every({ ...item, auction_highest_bidder: null })).toBe(false);
        expect(filters.every({ ...item, auction_highest_bidder: 2 })).toBe(false);
    });

    it('should test price max above', () => {
        const filters = new Filters({ ...userMock, id: 258 });
        filters.setFilters({ price_max_above: 10 });
        expect(filters.every({ above_recommended_price: 10 })).toBe(true);
        expect(filters.every({ above_recommended_price: 11 })).toBe(false);
        expect(filters.every({ above_recommended_price: 5 })).toBe(true);

        expect(filters.every({ above_recommended_price: 10, auction_highest_bidder: 258 })).toBe(true);
        expect(filters.every({ above_recommended_price: 11, auction_highest_bidder: 258 })).toBe(true);
        expect(filters.every({ above_recommended_price: 5, auction_highest_bidder: 258 })).toBe(true);
    });

    it('should test delivery time long max', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ delivery_time_long_max: 10 });
        expect(filters.every({ depositor_stats: { delivery_time_minutes_long: 10 } })).toBe(true);
        expect(filters.every({ depositor_stats: { delivery_time_minutes_long: 11 } })).toBe(false);
        expect(filters.every({ depositor_stats: { delivery_time_minutes_long: 5 } })).toBe(true);
    });

    it('should test stattrak', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ stattrak: THREE_WAY_FILTER_STATE.YES });
        expect(filters.every({ market_name: 'Test Item StatTrak™' })).toBe(true);
        expect(filters.every({ market_name: 'Test Item' })).toBe(false);

        filters.setFilters({ stattrak: THREE_WAY_FILTER_STATE.NO });
        expect(filters.every({ market_name: 'Test Item StatTrak™' })).toBe(false);
        expect(filters.every({ market_name: 'Test Item' })).toBe(true);

        filters.setFilters({ stattrak: THREE_WAY_FILTER_STATE.ANY });
        expect(filters.every({ market_name: 'Test Item StatTrak™' })).toBe(true);
        expect(filters.every({ market_name: 'Test Item' })).toBe(true);
    });

    it('should test has stickers', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ has_stickers: THREE_WAY_FILTER_STATE.YES });
        expect(filters.every({ stickers: [1] })).toBe(true);
        expect(filters.every({ stickers: [] })).toBe(false);
        expect(filters.every({ })).toBe(false);

        filters.setFilters({ has_stickers: THREE_WAY_FILTER_STATE.NO });
        expect(filters.every({ stickers: [1] })).toBe(false);
        expect(filters.every({ stickers: [] })).toBe(true);
        expect(filters.every({ })).toBe(false);

        filters.setFilters({ has_stickers: THREE_WAY_FILTER_STATE.ANY });
        expect(filters.every({ stickers: [1] })).toBe(true);
        expect(filters.every({ stickers: [] })).toBe(true);
        expect(filters.every({ })).toBe(true);
    });

    it('should test commodity', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ commodity: THREE_WAY_FILTER_STATE.YES });
        expect(filters.every({ is_commodity: true })).toBe(true);
        expect(filters.every({ is_commodity: false })).toBe(false);

        filters.setFilters({ commodity: THREE_WAY_FILTER_STATE.NO });
        expect(filters.every({ is_commodity: true })).toBe(false);
        expect(filters.every({ is_commodity: false })).toBe(true);

        filters.setFilters({ commodity: THREE_WAY_FILTER_STATE.ANY });
        expect(filters.every({ is_commodity: true })).toBe(true);
        expect(filters.every({ is_commodity: false })).toBe(true);
    });

    test('if itemSearch filters work as expected', () => {
        const itemSearchMockSticker = {
            item_search: {
                category: 'Sticker',
                type: null,
                sub_type: null,
                rarity: 'Remarkable',
            },
        };

        const itemSearchMockContainer = {
            item_search: {
                category: 'Container',
                type: null,
                sub_type: null,
                rarity: 'BaseGrade',
            },
        };

        const filters = new Filters(userMock);
        filters.setFilters({ category: itemSearchMockContainer.item_search.category });
        expect(filters.every(itemSearchMockSticker)).toBe(false);
        expect(filters.every(itemSearchMockContainer)).toBe(true);

        filters.setFilters({ not_category: itemSearchMockContainer.item_search.category });
        expect(filters.every(itemSearchMockSticker)).toBe(true);
        expect(filters.every(itemSearchMockContainer)).toBe(false);

        filters.setFilters({ rarity: itemSearchMockSticker.item_search.rarity });
        expect(filters.every(itemSearchMockSticker)).toBe(true);
        expect(filters.every(itemSearchMockContainer)).toBe(false);
    });

    it('should set searchsplit', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ search: 'test item name' });
        expect(filters.searchSplit.length).toBe(3);
        expect(filters.searchSplit).toStrictEqual(['test', 'item', 'name']);

        filters.setFilters({ search: 't' });
        expect(filters.searchSplit).toBeNull();

        filters.setFilters({ search: null });
        expect(filters.searchSplit).toBeNull();
    });

    it('should test search', () => {
        const filters = new Filters(userMock);
        filters.setFilters({ search: 'test item name' });
        expect(filters.every({ market_name: 'Random test item' })).toBe(false);
        expect(filters.every({ market_name: 'Not match' })).toBe(false);
        expect(filters.every({ market_name: 'Random test item name xyz' })).toBe(true);

        filters.setFilters({ search: 'Spectrum Case' });
        expect(filters.every({ market_name: 'Spectrum Case 2' })).toBe(true);

        filters.setFilters({ search: 'Spectrum Case', search_type: SEARCH_TYPE.EXACT });
        expect(filters.every({ market_name: 'Spectrum Case 2' })).toBe(false);
        expect(filters.every({ market_name: 'Spectrum Case' })).toBe(true);
    });

    it('should validate filters', () => {
        expect(filterValidation({
            price_min: 10,
            price_max: 100,
            wear_min: 10,
            wear_max: 100,
            auction: THREE_WAY_FILTER_STATE.ANY,
            price_max_above: 10,
            delivery_time_long_max: 10,
            stattrak: THREE_WAY_FILTER_STATE.ANY,
            has_stickers: THREE_WAY_FILTER_STATE.ANY,
            commodity: THREE_WAY_FILTER_STATE.ANY,
            search: 'test',
        }).length).toBe(0);

        expect(filterValidation({ price_min: 10 }).length).toBe(0);
        expect(filterValidation({ price_min: '10', stattrak: 'test' }))
            .toStrictEqual(['Filter price_min must be an integer', 'Filter stattrak must be one of yes,no,any']);
        expect(filterValidation({ search: '1' })).toStrictEqual(['Search filter must be more than 2 characters']);

        expect(filterValidation({ has_stickers: THREE_WAY_FILTER_STATE.YES, commodity: THREE_WAY_FILTER_STATE.YES }))
            .toStrictEqual(['Commodity filter must be one of "any", "yes", "no" and cannot also have wear-based or sticker-based filters.']);
    });

    it('should filter packet', () => {
        const filters = new Filters(userMock);

        const mock = (extra = []) => ({ data: [null, [{ purchase_price: 10 }, ...extra]] });

        expect(filters.filterPacket(mock())).toBe(null);

        filters.setFilters({ price_min: 10 });
        expect(filters.filterPacket(mock())).toStrictEqual(mock());
        expect(filters.filterPacket(mock())).toStrictEqual(mock());
        expect(filters.filterPacket(mock([{ purchase_price: 5 }, { purchase_price: 15 }])))
            .toStrictEqual(mock([{ purchase_price: 15 }]));
    });
});
