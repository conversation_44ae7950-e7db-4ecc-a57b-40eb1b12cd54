import { cloneDeep } from 'lodash';

export const THREE_WAY_FILTER_STATE = {
    YES: 'yes',
    NO: 'no',
    ANY: 'any',
};

export const SEARCH_TYPE = {
    EXACT: 'exact',
    CONTAINS: 'contains',
};

const splitKeyword = word => word.toLowerCase()
    .split(' ')
    .map(n => n.replace(/[+\-@<>()~*$"|]/, '').trim())
    .filter(n => n.length);

export const filterValidation = (filters) => {
    if (filters === null) {
        return [];
    }

    const floats = ['wear_min', 'wear_max', 'price_max_above'];
    const integers = ['price_min', 'price_max', 'delivery_time_long_max'];
    const strings = ['category', 'not_category', 'type', 'not_type', 'sub_type', 'not_sub_type', 'rarity', 'not_rarity'];
    const threeWay = ['auction', 'stattrak', 'has_stickers', 'commodity'];
    const arrays = ['wear_names'];

    const invalid = {};

    floats.forEach((key) => {
        if (filters[key] && Number.isNaN(parseFloat(filters[key]))) {
            invalid[key] = `Filter ${key} must be a float`;
        }
    });

    integers.forEach((key) => {
        if (filters[key] && !Number.isInteger(filters[key])) {
            invalid[key] = `Filter ${key} must be an integer`;
        }
    });

    strings.forEach((key) => {
        if (filters[key] && typeof filters[key] !== 'string') {
            invalid[key] = `Filter ${key} must be a string`;
        }
    });

    threeWay.forEach((key) => {
        if (filters[key] && !Object.values(THREE_WAY_FILTER_STATE).includes(filters[key])) {
            invalid[key] = `Filter ${key} must be one of ${Object.values(THREE_WAY_FILTER_STATE)}`;
        }
    });

    arrays.forEach((key) => {
        if (filters[key] && !Array.isArray(filters[key])) {
            invalid[key] = `Filter ${key} must be an array`;
        }
    });

    if (filters.search && filters.search.length > 100) {
        invalid.search = 'Search filter must be less than 100 characters';
    }

    if (filters.search && filters.search.length < 2) {
        invalid.search = 'Search filter must be more than 2 characters';
    }

    const allowCommodity = !(filters.has_stickers === THREE_WAY_FILTER_STATE.YES
        || filters.wear_min !== undefined
        || filters.wear_max !== undefined
        || filters.wear_names);
    if (filters.commodity === THREE_WAY_FILTER_STATE.YES && !allowCommodity) {
        invalid.commodity = 'Commodity filter must be one of "any", "yes", "no" and cannot also have wear-based or sticker-based filters.';
    }

    return Object.values(invalid);
};

export default class Filters {
    filters = null;

    constructor(user) {
        this.user = user;
    }

    setFilters(filters) {
        this.filters = filters;

        this.user.log.debug('setFilters', this.filters);

        if (!this.filters) {
            return;
        }

        this.searchSplit = null;
        if (!this.filters.search || this.filters.search.length < 2) {
            return;
        }

        this.searchSplit = splitKeyword(this.filters.search);
    }

    filterPacket(decodedPacket) {
        const packet = { ...cloneDeep(decodedPacket) };

        const filteredData = this.filterItems(packet.data[1]);

        if (!filteredData.length) {
            return null;
        }

        packet.data[1] = filteredData;
        return packet;
    }

    filterItems(itemPacket) {
        if (this.filters === null) {
            this.user.log.debug('Filters not set', this.user.id);
            return [];
        }

        this.user.log.debug('Filtering data using', this.user.id, this.filters);

        const items = Array.isArray(itemPacket) ? itemPacket : [itemPacket];

        return items.filter(this.every);
    }

    every = (item) => {
        if (this.filters === null) {
            this.user.log.debug('Filters not set', this.user.id);
            return false;
        }

        this.user.log.debug('Filtering data using', this.user.id, this.filters);

        const filters = {
            price_min: this.priceMin,
            price_max: this.priceMax,
            wear_min: this.wearMin,
            wear_max: this.wearMax,
            wear_names: this.wearNames,
            auction: this.auction,
            price_max_above: this.priceMaxAbove,
            delivery_time_long_max: this.deliveryTimeLongMax,
            stattrak: this.stattrak,
            has_stickers: this.hasStickers,
            commodity: this.commodity,
            category: this.category,
            not_category: this.notCategory,
            type: this.type,
            not_type: this.notType,
            sub_type: this.subType,
            not_sub_type: this.notSubType,
            rarity: this.rarity,
            not_rarity: this.notRarity,
            search: this.search,
        };

        this.user.log.debug('Using filters', this.filters);

        return Object.keys(filters).every((key) => {
            if (this.filters[key] === undefined) {
                return true;
            }

            return filters[key].call(this, item);
        });
    }

    priceMin(item) {
        return item.purchase_price >= this.filters.price_min;
    }

    priceMax(item) {
        return item.purchase_price <= this.filters.price_max;
    }

    wearMin(item) {
        return item.wear === undefined || item.wear >= this.filters.wear_min;
    }

    wearMax(item) {
        return item.wear === undefined || item.wear <= this.filters.wear_max;
    }

    wearNames(item) {
        return !this.filters.wear_names?.length || this.filters.wear_names?.includes(item.wear_name);
    }

    auction(item) {
        const { auction } = this.filters;

        if (item.auction_highest_bidder === this.user.id) {
            // always include user's own auctions
            return true;
        }

        const withoutBidder = !item.auction_highest_bidder;
        if (auction === THREE_WAY_FILTER_STATE.NO && withoutBidder) {
            // if non-auction return all without highest bidder, regardless of the timer
            // as it might transform to non-auction after timer ends
            return true;
        }

        const hasActiveAuction = item.auction_ends_at && item.auction_ends_at > Date.now() / 1000;
        if (auction === THREE_WAY_FILTER_STATE.YES && hasActiveAuction) {
            return true;
        }

        if (auction === THREE_WAY_FILTER_STATE.ANY && (withoutBidder || hasActiveAuction)) {
            return true;
        }

        return false;
    }

    priceMaxAbove(item) {
        return item.above_recommended_price <= this.filters.price_max_above
            || (
                item.auction_highest_bidder
                && this.user
                && item.auction_highest_bidder === this.user.id
            );
    }

    deliveryTimeLongMax(item) {
        return this.filters.delivery_time_long_max >= item.depositor_stats.delivery_time_minutes_long;
    }

    stattrak(item) {
        if (this.filters.stattrak === THREE_WAY_FILTER_STATE.YES && item.market_name.includes('StatTrak™')) {
            return true;
        }

        if (this.filters.stattrak === THREE_WAY_FILTER_STATE.NO && !item.market_name.includes('StatTrak™')) {
            return true;
        }

        return this.filters.stattrak === THREE_WAY_FILTER_STATE.ANY;
    }

    hasStickers(item) {
        if (item.stickers === undefined) {
            // item is in privacy mode and stickers are not available include only ANY is set
            return this.filters.has_stickers === THREE_WAY_FILTER_STATE.ANY;
        }

        if (this.filters.has_stickers === THREE_WAY_FILTER_STATE.YES && item.stickers.length) {
            return true;
        }

        if (this.filters.has_stickers === THREE_WAY_FILTER_STATE.NO && !item.stickers.length) {
            return true;
        }

        return this.filters.has_stickers === THREE_WAY_FILTER_STATE.ANY;
    }

    commodity(item) {
        if (this.filters.commodity === THREE_WAY_FILTER_STATE.YES && item.is_commodity) {
            return true;
        }

        if (this.filters.commodity === THREE_WAY_FILTER_STATE.NO && !item.is_commodity) {
            return true;
        }

        return this.filters.commodity === THREE_WAY_FILTER_STATE.ANY;
    }

    category(item) {
        return this.itemSearchFilter(item, 'category', true);
    }

    notCategory(item) {
        return this.itemSearchFilter(item, 'category', false);
    }

    type(item) {
        return this.itemSearchFilter(item, 'type', true);
    }

    notType(item) {
        return this.itemSearchFilter(item, 'type', false);
    }

    subType(item) {
        return this.itemSearchFilter(item, 'sub_type', true);
    }

    notSubType(item) {
        return this.itemSearchFilter(item, 'sub_type', false);
    }

    rarity(item) {
        return this.itemSearchFilter(item, 'rarity', true);
    }

    notRarity(item) {
        return this.itemSearchFilter(item, 'rarity', false);
    }

    itemSearchFilter(item, attribute, inclusive) {
        const filterAttribute = inclusive ? attribute : `not_${attribute}`;

        if (!this.filters[filterAttribute]) {
            return true;
        }

        if (inclusive) {
            return this.filters[filterAttribute] === item.item_search?.[attribute];
        }
        return this.filters[filterAttribute] !== item.item_search?.[attribute];
    }

    search(item) {
        if (this.filters.search_type === SEARCH_TYPE.EXACT) {
            return item.market_name === this.filters.search;
        }

        // Just one item in array needs to match so that we consider it a search match
        const name = splitKeyword(item.market_name);

        if (!name.length) {
            return false;
        }

        // Item must have all arguments to it be considered a match
        // split match start with search split
        // this evaluates our ft search as close as possible
        return this.searchSplit.every(split => name.some(nsplit => nsplit.startsWith(split)));
    }
}
