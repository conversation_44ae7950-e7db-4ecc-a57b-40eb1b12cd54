import http from 'http';
import { Server as SocketIO } from 'socket.io';
import { io as client } from 'socket.io-client';
import Redis from 'ioredis-mock';
import TradeServer from './index';
import createAdapter from '../../lib/adapter';

const runServer = async () => {
    const server1 = http.createServer();
    const server2 = http.createServer();

    const opts = {
        allowEIO3: true, // allow old clients to connect
        serveClient: false,
        path: '/s',
        pingInterval: 45000,
        pingTimeout: 20000,
        requestsTimeout: 10000,
        perMessageDeflate: false,
    };

    const listeners = [server1, server2].map(server => new Promise((done) => {
        server.on('listening', () => {
            done();
        });
    }));

    const min = 60000;
    const max = 65000;
    const port = Math.floor(Math.random() * (max - min) + min);

    server1.listen({
        host: 'localhost',
        port,
        exclusive: false,
    });
    server2.listen({
        host: 'localhost',
        port: port + 1,
        exclusive: false,
    });

    await Promise.all(listeners);

    const io = new SocketIO(server1, { ...opts, adapter: createAdapter(new Redis(), new Redis()) });
    const io2 = new SocketIO(server2, { ...opts, adapter: createAdapter(new Redis(), new Redis()) });

    const fakeMiddleware = () => (socket, next) => next();
    const nsp1 = new TradeServer('trade');
    nsp1.rateLimiter = fakeMiddleware;
    nsp1.identifierCheck = fakeMiddleware;
    await nsp1.init(io);

    const nsp2 = new TradeServer('trade');
    nsp2.rateLimiter = fakeMiddleware;
    nsp2.identifierCheck = fakeMiddleware;
    await nsp2.init(io2);

    const address = server1.address();
    const sender = client(`ws://localhost:${address.port}/trade`, {
        transports: ['websocket'],
        reconnection: false,
        path: '/s',
    });

    await new Promise((done) => {
        sender.once('init', () => {
            done();
        });
    });

    const close = async () => {
        sender.disconnect();
        await Promise.all([nsp1, nsp2].map(nsp => new Promise((resolve) => {
            nsp.socket.adapter.pubClient.disconnect();
            nsp.socket.adapter.subClient.disconnect();
            nsp.server.on('closed', () => {
                resolve();
            });
            nsp.server.emit('shutdown');
        })));
        server1.close();
        server2.close();
        jest.restoreAllMocks();
    };

    return {
        sender, nsp1, nsp2, close,
    };
};

describe('trade server', () => {
    test('should receive new_item in batch', async () => {
        const { sender, nsp2, close } = await runServer();

        sender.emit('filters', { auction: 'any' });

        const promise = new Promise((done) => {
            sender.on('new_item', (data) => {
                expect(data).toEqual([{ test: 'some messages1' }, { test: 'some messages2' }]);
                done();
            });
        });

        setTimeout(() => {
            nsp2.socket.emit('new_item', { test: 'some messages1' });
            nsp2.socket.emit('new_item', { test: 'some messages2' });
        }, 50);

        await promise;
        await close();
    });
    test('should receive filtered item only', async () => {
        const {
            sender,
            nsp1,
            nsp2,
            close,
        } = await runServer();

        const auctionEndsAt = (Date.now() / 1000) + 60;
        sender.emit('filters', { auction: 'yes' });

        const nsps = [nsp1, nsp2];
        let counter = nsps.length;
        const promise = new Promise((done) => {
            sender.on('new_item', (data) => {
                const d = Array.isArray(data) ? data : [data];
                expect(d).toEqual([{ nsp: d[0].nsp, test: 'some messages1', auction_ends_at: auctionEndsAt }]);
                counter -= 1;

                if (!counter) {
                    setTimeout(() => done(), 1000);
                }
            });
        });

        setTimeout(() => {
            nsps.forEach((nsp, index) => {
                nsp.socket.emit('new_item', { nsp: index + 1, test: 'some messages1', auction_ends_at: auctionEndsAt });
                nsp.socket.emit('new_item', { nsp: index + 1, test: 'some messages2 - filtered', auction_ends_at: auctionEndsAt - 120 });
            });
        }, 50);

        await promise;
        await close();
    });
});
