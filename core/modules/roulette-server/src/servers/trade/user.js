// parser is installed via socket.io-redis
// eslint-disable-next-line import/no-extraneous-dependencies
import { PacketType, Decoder, Encoder } from 'socket.io-parser';
import { isEqual } from 'lodash';
import SocketUser from '../../lib/user';
import Filters, { filterValidation } from './filters';

const shouldFilterPacket = (packet) => {
    const filterEvents = ['new_item'];
    return (packet && packet.type === PacketType.EVENT && packet.data[0].includes(filterEvents))
        || (typeof packet[0] === 'string' && packet[0].includes(filterEvents));
};

export default class TradeUser extends SocketUser {
    allowApiTokenAuth = true;

    filters = null;

    attachAuthorizedListeners() {
        super.attachAuthorizedListeners();
        this.attachAuthorizedListener('timesync', this.timesync);
    }

    attachListeners() {
        this.filters = this.filters || new Filters(this);

        super.attachListeners();
        this.attachListener('filters', this.onFilters);

        const {
            writeToEngine,
            _packet: packetFn,
        } = this.socket.client;

        this.socket.client.writeToEngine = (packet, opts) => this.filterPacket(packet)
            .then(filteredPacket => this.filteredPacket(filteredPacket, opts, writeToEngine));

        // eslint-disable-next-line no-underscore-dangle
        this.socket.client._packet = (packet, opts) => this.filterPacket(packet)
            .then(filteredPacket => this.filteredPacket(filteredPacket, opts, packetFn));
    }

    filterPacket(packet) {
        if (!shouldFilterPacket(packet)) {
            return Promise.resolve({ packet });
        }

        if (packet.type === PacketType.EVENT) {
            // this packet is already decoded or no need to filter
            return Promise.resolve({ packet: this.filters.filterPacket(packet) });
        }

        return new Promise((resolve) => {
            const decoder = new Decoder();
            decoder.on('decoded', (decodedPacket) => {
                const filteredPacket = this.filters.filterPacket(decodedPacket);
                if (filteredPacket === null) {
                    resolve({ packet: null });
                    return;
                }

                if (isEqual(decodedPacket, filteredPacket)) {
                    // no changes
                    resolve({ packet });
                }

                const encoder = new Encoder();
                const encodedPacket = encoder.encode(filteredPacket);
                resolve({ packet: encodedPacket, filtered: true });
            });

            decoder.add(packet[0]);
        });
    }

    filteredPacket = ({ packet: filteredPacket, filtered }, opts, clientCallback) => {
        if (filteredPacket === null) {
            this.log.debug('Packet filtered', filteredPacket);
            return;
        }

        try {
            if (!this.socket.client.conn) {
                this.log.warn('Ignore apply packet due to client conn not set');
                return;
            }

            this.log.debug('Packet allowed', filteredPacket);

            const packetOptions = opts || {};
            if (filtered && packetOptions.wsPreEncodedFrame) {
                // there are changes so we can't use pre-encoded data anymore
                delete packetOptions.wsPreEncodedFrame;
                packetOptions.preEncoded = false;
            }

            clientCallback.apply(this.socket.client, [filteredPacket, packetOptions]);
        } catch (error) {
            this.log.warn('Failed apply packet', error?.toString());
        }
    }

    timesync = () => {
        this.socket.emit('timesync', Date.now());
    }

    onFilters = (data) => {
        const validationErrors = filterValidation(data);
        this.log.debug('onFilters', data, validationErrors);
        if (validationErrors.length) {
            this.socket.emitError(validationErrors.join('\n'));
        } else {
            this.filters.setFilters(data);
        }
    }
}
