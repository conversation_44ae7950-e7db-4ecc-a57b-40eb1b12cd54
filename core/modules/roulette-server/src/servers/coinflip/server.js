import Server from '../../lib/server';
import CoinflipMaster from './lib/CoinflipMaster';
import CoinflipWorker from './lib/CoinflipWorker';

export default class CoinflipServer extends Server {
    WorkerModuleClass = CoinflipWorker

    MasterModuleClass = CoinflipMaster

    get coinflip() {
        return this.slaveModule;
    }

    get coinflipMaster() {
        return this.masterModule;
    }
}
