import { handleException } from '../../helpers';
import SocketUser from '../../lib/user';
import {
    executeCoinflipRoundForUser,
} from '../../lib/provably-fair';

export default class CoinflipUser extends SocketUser {
    attachAuthorizedListeners() {
        super.attachAuthorizedListeners();
        this.attachAuthorizedListener('acceptChallenge', this.acceptChallenge);
        this.attachAuthorizedListener('callBot', this.callBot);
        this.attachAuthorizedListener('reaction', this.reaction);
    }

    acceptChallenge = async (data) => {
        await this.execute('challenge', data);
    }

    callBot = async (data) => {
        await this.execute('call-bot', data);
    }

    reaction = async (data) => {
        try {
            const COLONS_REGEX = new RegExp(
                '([^:]+)?(:[a-zA-Z0-9-_+]+:(:skin-tone-[2-6]:)?)',
                'g',
            );
            const HASH_REGEX = /^[a-f0-9]{32}$/i;

            if (typeof data.emoji !== 'string' || typeof data.hash !== 'string' || !Number.isInteger(data.side) || !COLONS_REGEX.test(data.emoji) || !HASH_REGEX.test(data.hash)) {
                this.log.warn('Invalid reaction data:', data);
                this.socket.emitError('Invalid reaction data');
                return;
            }

            this.server.socket.emit('reaction', data);
        } catch (error) {
            this.log.error('Error sending reaction:', error);
            this.socket.emitError(error);
        }
    }

    async execute(type, { roundId, security_token: securityToken }) {
        try {
            this.log.debug('execute coinflip', type, roundId);
            const round = await executeCoinflipRoundForUser(this.user, securityToken, type, roundId);

            if (this.server.module) {
                this.log.debug('Handle directly in the slave');
                await this.server.module.handleChallengedCoinflip(round);
            } else {
                this.log.debug('Handle in the master');
                await this.server.internalRequest.master('handleChallengedCoinflip', round);
            }
        } catch (error) {
            this.socket.emitError(error);
            await handleException(error, `Coinflip execute: ${type}`, `[Block #${roundId}] Error executing coinflips`);
        }
    }
}
