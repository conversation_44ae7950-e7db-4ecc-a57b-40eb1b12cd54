import { createRegistry, createRequest, applyParams } from '@substreams/core';
import { readPackage } from '@substreams/manifest';
import { BlockEmitter } from '@substreams/node';
import { createNodeTransport } from '@substreams/node/createNodeTransport';

const {
    SUBSTREAMS_API_KEY,
} = process.env;

// auth API token
// https://app.streamingfast.io/
// https://app.pinax.network/
if (!SUBSTREAMS_API_KEY) {
    throw new Error('SUBSTREAMS_API_KEY is require');
}

const token = SUBSTREAMS_API_KEY;
const baseUrl = 'https://eos.substreams.pinax.network:443';

// User parameters
const manifest = 'https://github.com/pinax-network/substreams/releases/download/blocks-v0.1.0/blocks-v0.1.0.spkg';
const outputModule = 'map_blocks';
const startBlockNum = -1;
const productionMode = true;

export default async function antelope(startCursor) {
    // Read Substream
    const substreamPackage = await readPackage(manifest);
    if (!substreamPackage.modules) {
        throw new Error('No modules found in substream package');
    }

    // Connect Transport
    const registry = createRegistry(substreamPackage);
    const transport = createNodeTransport(baseUrl, token, registry);
    const request = createRequest({
        substreamPackage,
        outputModule,
        startBlockNum,
        productionMode,
        startCursor,
    });

    return new BlockEmitter(transport, request, registry);
}
