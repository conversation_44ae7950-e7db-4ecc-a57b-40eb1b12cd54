import crypto from 'crypto';
import request from 'request-promise';
import map from 'async/map';
import { knex } from '../../../database';
import CoinflipBase from './CoinflipBase';
import client from '../../../lib/redis';
import {
    handleException,
    setImmediatePromise,
    IS_PRODUCTION,
    pad<PERSON>ero,
} from '../../../helpers';
import {
    getHashForValue,
    getCoinflipPendingRounds,
} from '../../../lib/provably-fair';

const CHECK_PENDING_ROUNDS_INTERVAL = 60; // seconds

const SUBSTREAM_STATUS_CHECK_INTERVAL = 30; // seconds

// how often we reset substream connection to avoid memory leaks
const SUBSTREAM_RESET_INTERVAL = Number(process.env.SUBSTREAM_RESET_INTERVAL_SECONDS || Infinity); // seconds

// if we reach this memory threshold, we reset the substream connection
const SUBSTREAM_RESET_MEMORY_THRESHOLD = Number(process.env.SUBSTREAM_RESET_MEMORY_THRESHOLD_MB || 400); // MB

const { SUBSTREAMS_API_KEY } = process.env;

const SIMULATE_BLOCKS = !SUBSTREAMS_API_KEY;

if (IS_PRODUCTION && !SUBSTREAMS_API_KEY) {
    throw new Error('SUBSTREAMS_API_KEY missing!');
}

const EOS_API_ENDPOINT = process.env.EOS_API_ENDPOINT || 'eos.pff.gg,mainnet.eosamsterdam.net,eos.api.eosnation.io';
const API_ENDPOINTS = EOS_API_ENDPOINT.split(',');

/**
 * Should not be instantiated directly, but rather be called via the static `create()` function
 */
export default class CoinflipMaster extends CoinflipBase {
    logSuffix = 'lib:CoinflipMaster';

    constructor(server) {
        super(server);

        this.log.debug('creating CoinflipMaster');

        this.anteloper = null;
        this.substreamWatchdogAlerted = null;
        this.highestEosBlock = null;
        this.timeLastBlockSaved = 0;
        this.lastSubstreamReset = Date.now();
    }

    static async create(server) {
        const coinflip = new CoinflipMaster(server);

        try {
            await coinflip.loadPendingRounds();
            await coinflip.substreamConnectionAttempt();
            coinflip.startWatchdogs();
        } catch (err) {
            await handleException(err, 'CRITICAL', 'Coinflip.create() failed.');
            throw err;
        }

        return coinflip;
    }

    onHandleChallengedCoinflip(from, round) {
        this.log.debug('Got handle round event from', from, 'with round', round);
        try {
            this.handleChallengedCoinflip(round);
        } catch (error) {
            this.log.error('onHandleRound.handleChallengedCoinflip', error);
        }
    }

    startWatchdogs() {
        // Check against the database for integrity every minute
        const checkPendingRounds = async () => {
            clearTimeout(this.checkInterval);
            this.log.info('checkPendingRounds...');
            try {
                await this.loadPendingRounds();
            } catch (error) {
                await handleException(error, 'Coinflip.pendingRoundsChecker', 'Failed to load pendingRounds');
            }
            this.checkInterval = setTimeout(checkPendingRounds, 1000 * CHECK_PENDING_ROUNDS_INTERVAL);
        };
        checkPendingRounds();

        const substreamWatchdog = () => {
            clearInterval(this.substreamWatchdogInterval);
            this.substreamWatchdogInterval = setTimeout(async () => {
                await this.checkSubstreamStatus();
                substreamWatchdog();
            }, SUBSTREAM_STATUS_CHECK_INTERVAL * 1000);
        };

        substreamWatchdog();
    }

    async checkSubstreamStatus() {
        this.log.info(
            'Checking substream status.',
            'this.timeLastBlockSaved=', this.timeLastBlockSaved,
            'anteloper.cancelFn=', !!this.anteloper?.cancelFn,
        );

        if (Date.now() - this.lastSubstreamReset > SUBSTREAM_RESET_INTERVAL * 1000) {
            this.log.warn(
                'Substream reset interval reached. Resetting substream connection.',
                'this.lastSubstreamReset=', this.lastSubstreamReset,
                'SUBSTREAM_RESET_INTERVAL=', SUBSTREAM_RESET_INTERVAL,
            );
            this.substreamConnectionAttempt();
            return;
        }

        const used = process.memoryUsage().heapUsed / 1024 / 1024;
        const usedMb = Math.round(used * 100) / 100;
        if (usedMb > SUBSTREAM_RESET_MEMORY_THRESHOLD) {
            this.log.warn(
                'Substream mempry threshold reached. Resetting substream connection.',
                'usedMb=', usedMb,
                'SUBSTREAM_RESET_MEMORY_THRESHOLD=', SUBSTREAM_RESET_MEMORY_THRESHOLD,
            );
            this.substreamConnectionAttempt();
            return;
        }

        try {
            if (!this.substreamWatchdogAlerted && (Date.now() - this.timeLastBlockSaved > 30000 || !this.anteloper?.cancelFn)) {
                const msg = `Haven't saved a eos highest block in ${Date.now() - this.timeLastBlockSaved} milliseconds.`;
                this.substreamWatchdogAlerted = true;
                this.substreamConnectionAttempt();
                await handleException(null, 'CRITICAL', msg);
            }
        } catch (error) {
            await handleException(error, 'CRITICAL', error.message);
        }
    }

    async stop() {
        await super.stop();
        this.pendingRounds = {};
        if (this.anteloper?.cancelFn) {
            this.anteloper.stop();
        }
        clearTimeout(this.nextBlockFetcher);
        clearTimeout(this.substreamConnectTimer);
        clearTimeout(this.checkInterval);
        clearTimeout(this.substreamWatchdogInterval);
        clearInterval(this.substreamSimulator);
    }

    async handleEOSData(msg, cursor = undefined) {
        try {
            const ts = new Date(msg.time || (Number(msg.timestamp.seconds) + Number(msg.timestamp.nanos) / 1e9) * 1000);
            const formattedDate = `${ts.getFullYear()}-${padZero(ts.getMonth() + 1)}-${padZero(ts.getDate())}T${padZero(ts.getHours())}:${padZero(ts.getMinutes())}:${padZero(ts.getSeconds())}.${padZero(ts.getMilliseconds(), 3)}`;

            const blockData = {
                height: Number(msg.number),
                hash: msg.id,
                time: formattedDate,
            };

            let blockIsNew = false;
            if (Date.now() - new Date(blockData.time) <= 30000) {
                await this.server.internalRequest.slave('newEosBlock', blockData);
                await this.handlePendingRounds(blockData);
                blockIsNew = true;
            }

            const blockDataJson = JSON.stringify(blockData);

            // Put it into redis
            const multi = client.multi();
            multi.setex(`roulette-server:coinflip:eos-blocks:${blockData.height}`, 30 * 60, blockDataJson);

            if (cursor && Date.now() - this.timeLastBlockSaved >= 10000) {
                this.log.info(`Saved eos for block ${blockData.height} at time ${blockData.time}`);
                this.timeLastBlockSaved = Date.now();
                this.substreamWatchdogAlerted = false; // reset the watchdog when we successfully save a cursor
                multi.setex('roulette-server:coinflip:eos-substream-cursor', 600, cursor);
            }

            if (blockIsNew && (!this.highestEosBlock || blockData.height > this.highestEosBlock.height)) {
                // This key expires after 2 seconds. If the key doesn't exist when someone tries to challenge a coinflip, we will not allow the challenge
                // to go through, since we don't have up-to-date eos information.
                multi.setex('roulette-server:coinflip:eos-highest-block', 2, blockDataJson);
                this.highestEosBlock = blockData;
            }

            await multi.exec();
        } catch (error) {
            await handleException(error, 'Coinflip.handleEOSData', `Failed to handle EOS data ${error.message}`);
        }

        await setImmediatePromise();
    }

    async prepareSubstreamConnect() {
        await setImmediatePromise();

        if (SIMULATE_BLOCKS) {
            this.log.warn('SUBSTREAMS_API_KEY not set. Simulate randoms seed without PF');
            this.substreamSimulator = setInterval(async () => {
                try {
                    if (!this.substreamSimulatorData) {
                        this.substreamSimulatorData = {
                            number: 129318603,
                            id: null,
                            timestamp: { seconds: Date.now() / 1000, nanos: 0 },
                        };
                    }

                    this.substreamSimulatorData.number += 1;
                    this.substreamSimulatorData.time = Date.now();
                    this.substreamSimulatorData.id = crypto.createHash('sha256')
                        .update([this.substreamSimulatorData.num, this.substreamSimulatorData.timestamp, Math.random()].join('+'))
                        .digest('hex');
                    const data = {
                        ...this.substreamSimulatorData,
                    };

                    await this.handleEOSData(data);
                } catch (error) {
                    this.log.error('Failed to save fake eos data', error);
                }
            }, 500);
            return;
        }

        const startCursor = this.cursor || (await client.get('roulette-server:coinflip:eos-substream-cursor')) || undefined;
        if (!this.substreamModule) {
            this.log.info('EOS Substream SETUP. Start cursor:', startCursor);

            /**
             * Substream packages are pure ESM and can't be imported in CommonJS.
             * Ugly workaround: Use dynamic import() to load the package and import it via src instead of babeled version
             *
             * ⚠️Warning: This package is native ESM.
             * If your project uses CommonJS, you'll have to convert to ESM or use the dynamic import() function.
             * Please don't open issues for questions regarding CommonJS / ESM.
             */
            const antelopeMJS = '/app/src/servers/coinflip/lib/antelope-blocks.mjs';
            this.substreamModule = await import(antelopeMJS);
        } else {
            this.log.info('EOS Substream SETUP RECONNECT. Start cursor:', startCursor);
        }

        if (this.anteloper?.cancelFn) {
            this.anteloper.stop();
        }

        this.anteloper = await this.substreamModule.default(startCursor);

        await setImmediatePromise();
    }

    startSubstreamListener() {
        if (SIMULATE_BLOCKS) {
            this.listenerReady();
            return;
        }

        const onError = async (error) => {
            const err = error || new Error('Stream closed');
            this.log.error('Substream Error', err);
            await handleException(err, 'CRITICAL', err.message);
            this.substreamConnectionAttempt();
        };

        // Session Trace ID
        this.anteloper.on('session', (session) => {
            clearTimeout(this.substreamConnectTimer);
            this.log.info('Antelope Session ready', session);
            this.listenerReady();
        });

        this.anteloper.on('cursor', (cursor, block) => {
            this.log.info('Block', cursor, block);
            this.cursor = cursor;
            this.handleEOSData(block, cursor);
        });

        // End of Stream
        this.anteloper.on('close', (error) => {
            onError(error);
        });

        // Fatal Error
        this.anteloper.on('fatalError', (error) => {
            onError(error);
        });

        this.anteloper.start();
    }

    async substreamConnectionAttempt() {
        if (this.server.isShuttingDown) {
            this.log.debug('Server is exiting, do not try to connect substream');
            return;
        }

        await setImmediatePromise();
        if (this.substreamConnectionAttemptRunning) {
            return;
        }

        this.substreamConnectionAttemptRunning = true;

        this.log.debug('Attempt to connect substream...');
        try {
            await this.prepareSubstreamConnect();
            const promise = new Promise((resolve, reject) => {
                this.substreamConnectTimer = setTimeout(() => reject(new Error('Substream connection timeout!')), 30000);
                this.listenerReady = resolve;
            });
            this.startSubstreamListener(); // do not wait, avoid memory leak
            await setImmediatePromise();
            await promise;
            clearTimeout(this.nextBlockFetcher);
            await setImmediatePromise();
            this.lastSubstreamReset = Date.now();
            this.substreamConnectionAttemptRunning = false;
        } catch (err) {
            this.fetchNextBlock(); // start fallback poller

            this.log.info(`Substream connect error: ${err.message}`);
            this.substreamConnectionAttemptRunning = false;
        }
    }

    async fetchNextBlock() {
        clearTimeout(this.nextBlockFetcher);

        let startBlock = this.highestEosBlock ? this.highestEosBlock.height + 1 : undefined;
        if (!this.highestEosBlock || !this.highestEosBlockSynced || this.highestEosBlockSynced < Date.now() - 5000) {
            try {
                this.log.info('Sync highest eos block');
                const block = await this.getBlockData();
                this.highestEosBlockSynced = Date.now();
                this.highestEosBlock = block;
            } catch (error) {
                this.log.error('Failed to fetch initial block', error);
            }
        }

        if (!this.highestEosBlock) {
            this.nextBlockFetcher = setTimeout(() => this.fetchNextBlock(), 501);
            return;
        }

        if (!startBlock) {
            startBlock = this.highestEosBlock.height;
        }

        const endBlock = this.highestEosBlock.height + 1;

        this.log.info('fetchNextBlock fallback...', startBlock, 'to', endBlock);
        for (startBlock; startBlock <= endBlock; startBlock++) {
            try {
                // eslint-disable-next-line no-await-in-loop
                const block = await this.getBlockData(startBlock);

                if (!block) {
                    break;
                }

                // eslint-disable-next-line no-await-in-loop
                await this.handleEOSData({
                    number: Number(block.height),
                    id: block.hash,
                    time: block.time,
                });
            } catch (error) {
                this.log.error('Failed to fetch next block', error);
            }
            // eslint-disable-next-line no-await-in-loop
            await setImmediatePromise();
        }

        await setImmediatePromise();

        const timeout = Math.max(0, this.highestEosBlock ? Math.min(Date.now() - new Date(this.highestEosBlock.time).getTime(), 500) : 501);
        this.nextBlockFetcher = setTimeout(() => this.fetchNextBlock(), timeout);
    }

    async loadPendingRounds() {
        const rows = await getCoinflipPendingRounds(CHECK_PENDING_ROUNDS_INTERVAL);

        this.log.info('found', rows.length, 'unresolved rounds from database', rows);
        await map(rows, async (row) => {
            this.pendingRounds[row.seed_index] = this.pendingRounds[row.seed_index] || [];
            if (this.pendingRounds[row.seed_index].some(round => round.id === row.id)) {
                this.log.warn('round', row.id, 'is already in memory');
                return; // this round is already in memory
            }

            // Now retrieve user data
            const userIdsToRetrieve = [row.creator_user_id];
            if (row.challenger_user_id) {
                userIdsToRetrieve.push(row.challenger_user_id);
            }
            const [users] = await knex.raw('SELECT u.id, u.steam_name, u.avatar, l.level FROM users u ' +
                'LEFT JOIN user_levels_data l ON l.user_id = u.id ' +
                `WHERE u.id IN (${userIdsToRetrieve.join(',')})`);

            const roundData = {
                id: row.id,
                creator_user_id: row.creator_user_id,
                creator_profile: null,
                challenger_user_id: row.challenger_user_id,
                challenger_profile: null,
                creator_chosen_side: row.creator_chosen_side,
                bet_amount: row.bet_amount,
                challenger_bet_amount: row.challenger_bet_amount,
                win_amount: row.win_amount,
                private_seed: row.private_seed,
                private_seed_hash: getHashForValue(row.private_seed),
                seed_source: row.seed_source,
                seed_index: row.seed_index,
                seed: row.seed,
                result: row.result,
                creator_dismissed: (row.flags & 1) === 1,
                created_at: Math.floor((new Date(row.created_at)).getTime() / 1000),
                challenged_at: row.challenged_at ? Math.floor((new Date(row.challenged_at)).getTime() / 1000) : null,
                start_at: row.start_at ? Math.floor((new Date(row.start_at)).getTime() / 1000) : null,
            };

            users.forEach((userRow) => {
                roundData[userRow.id === row.creator_user_id ? 'creator_profile' : 'challenger_profile'] = {
                    id: userRow.id,
                    steam_name: userRow.steam_name,
                    avatar: userRow.avatar,
                    level: userRow.level,
                };
            });

            this.log.debug('added pending round, id=', row.id, 'index=', row.seed_index);
            this.pendingRounds[row.seed_index].push(roundData);
            await setImmediatePromise();
        });

        const roundKeys = Object.keys(this.pendingRounds);
        this.log.info('total pending blocks', roundKeys.length);

        // See if any of our pending rounds are ready to be executed
        await map(roundKeys, async (blockHeight) => {
            try {
                this.log.info('resolving pending block', blockHeight);
                await setImmediatePromise();
                const blockData = await this.getBlockData(blockHeight);
                if (!blockData) {
                    this.log.warn('block data not found for height', blockHeight, blockData);
                    return;
                }

                if (Date.now() - new Date(blockData.time) < 5000) {
                    this.log.warn('block happened in last 5s, height:', blockHeight);
                    // If this block happened within the past 5 seconds, don't do anything with it
                    return;
                }

                this.log.info('resolve pending blockHeight', blockHeight);
                await this.handlePendingRounds(blockData);
            } catch (err) {
                await handleException(err, 'Coinflip execute', `[Block #${blockHeight}] Error retrying execution of coinflips for block ${blockHeight}`);
            }
        });
    }

    getApiEndpoint() {
        if (!this.apiEndpointResetted || this.apiEndpointResetted < Date.now() - 1000 * 60 * 5) {
            this.currentApiEndpointIndex = 0;
            this.apiEndpointResetted = Date.now();
        }

        return API_ENDPOINTS[this.currentApiEndpointIndex];
    }

    async getBlockData(blockHeight) {
        const apiEndpoint = this.getApiEndpoint();
        if (!apiEndpoint) {
            this.log.error('Out of working endpoints!');
            return null;
        }

        if (SIMULATE_BLOCKS) {
            this.log.warn('Blocks simulation enabled. Get random block data for height', blockHeight);
            const time = Date.now();
            return {
                height: blockHeight,
                hash: crypto.createHash('sha256')
                    .update([blockHeight, time, Math.random()].join('+'))
                    .digest('hex'),
                time,
            };
        }

        let blockData;
        try {
            blockData = blockHeight ? await client.get(`roulette-server:coinflip:eos-blocks:${blockHeight}`) : null;
            if (blockData) {
                return JSON.parse(blockData);
            }
        } catch (error) {
            this.log.error('Failed to get block data from redis', error);
        }

        if (blockHeight) {
            this.log.warn('block data not found from cache for height', blockHeight);
        }

        try {
            this.log.debug('Fetch eos block data from', apiEndpoint);
            const data = await request({
                method: blockHeight ? 'POST' : 'GET',
                uri: blockHeight ? `https://${apiEndpoint}/v1/chain/get_block` : `https://${apiEndpoint}/v1/chain/get_info`,
                body: blockHeight ? { block_num_or_id: blockHeight } : undefined,
                json: true,
                timeout: 1000,
            });

            blockData = {
                height: blockHeight || data.head_block_num,
                hash: blockHeight ? data.id : data.head_block_id,
                time: blockHeight ? data.timestamp : data.head_block_time,
            };

            this.log.info('Resolved missing block', blockHeight, 'data', blockData, 'from', apiEndpoint);

            return blockData;
        } catch (error) {
            if (error.error?.error?.code === 3100002) {
                // Unknown block
                this.log.warn('Unknown block', blockHeight, 'from', apiEndpoint);
                return null;
            }

            this.log.error('Failed to fetch missing block data from eos api', apiEndpoint, error);
            this.currentApiEndpointIndex++; // start using next endpoint
            return this.getBlockData(blockHeight);
        }
    }
}
