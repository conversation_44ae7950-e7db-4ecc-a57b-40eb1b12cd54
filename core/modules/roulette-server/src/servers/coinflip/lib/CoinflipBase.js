import map from 'async/map';
import cloneDeep from 'lodash/cloneDeep';
import ServerModule from '../../../lib/server-module';
import { handleException, internalApiRequest, setImmediatePromise } from '../../../helpers';
import { getCoinflipRoundResult } from '../../../lib/provably-fair';

export default class CoinflipBase extends ServerModule {
    logSuffix = 'lib:CoinflipBase';

    constructor(server) {
        super(server);
        this.pendingRounds = {};
        this.createThrottler('results', this.flushResults, { timeout: 200, maxCount: 20 });
        this.createThrottler('challenges', this.flushChallenges, { timeout: 100, maxCount: 20 });
        this.createThrottler('reaction', { timeout: 100, maxCount: 20, auto: true });
    }

    async handleChallengedCoinflip(roundData) {
        this.log.debug('handle coinflip challenge', roundData.id, roundData);

        if (!roundData.private_seed) {
            throw new Error('Private seed missing from round data');
        }

        const emitData = cloneDeep(roundData);
        emitData.private_seed = null;
        this.throttler('challenges').push(emitData);

        const clonedRoundData = cloneDeep(roundData);
        this.pendingRounds[clonedRoundData.seed_index] = this.pendingRounds[clonedRoundData.seed_index] || [];
        this.pendingRounds[clonedRoundData.seed_index].push(clonedRoundData);
    }

    flushChallenges = (data) => {
        this.server.socket.emit('challenge', data);
    }

    async handlePendingRounds(blockData) {
        // See if there are any rounds for this block height
        if (!this.pendingRounds[blockData.height]) {
            return;
        }

        try {
            // These rounds need to be completed
            await map(this.pendingRounds[blockData.height], async (round) => {
                await this.executeCoinflipRound(round, blockData);
            });
            delete this.pendingRounds[blockData.height];
        } catch (err) {
            await handleException(err, 'Coinflip execute', `[Block #${blockData.height}] Error executing coinflips for block ${blockData.height}`);
        }
    }

    async executeCoinflipRound(round, blockData) {
        await setImmediatePromise();

        if (round.seed_index.toString() !== blockData.height.toString()) {
            throw new Error(`Tried to execute coinflip ${round.id} with block height ${blockData.height} (expected ${round.seed_index})`);
        }

        const winningSide = getCoinflipRoundResult(round, blockData.hash);

        try {
            const result = await internalApiRequest(`/coinflip/execute/${round.id}`, {
                winningSide,
                hash: blockData.hash,
                // If the seed index changed because of something like readying-up early, this lets us know we can
                // ignore this event on Laravel.
                seed_index: round.seed_index,
            });

            this.throttler('results').push(result);
        } catch (err) {
            await handleException(err, 'Coinflip win', `[Round #${round.id}] Error recording coinflip win of ${round.bet_amount} bet amount for user with winning side ${winningSide}`, {
                amount: round.bet_amount,
            });
        }
    }

    flushResults = (results) => {
        this.server.socket.emit('result', results);
    }
}
