import crypto from 'crypto';
import moment from 'moment';
import isArray from 'lodash/isArray';
import { Model, knex } from '.';
import client from '../lib/redis';
import {
    logger,
    setImmediatePromise,
    SOCKET_DATA_ENCRYPT_ALGORITHM,
    SOCKET_DATA_ENCRYPT_SECRET,
    toBoolean,
} from '../helpers';

const log = logger('database:user');

export default class User extends Model {
    static getTableName() {
        return 'users';
    }

    static hidden = ['socket', 'encuidCached'];

    static publicAttributes = ['id', 'steam_name', 'name', 'avatar', 'verified', 'lvl', 'badges'];

    static virtualAttributes = [
        'uid',
        'helper_mod',
        'mod',
        'super_mod',
        'admin',
        'qa',
        'deposited',
        'lvl',
        'badge_text',
        'badge_text_localized',
        'badge_color',
        'hide_rank',
        'name',
        'balance',
        'balances',
        'isGuest',
        'encuid',
    ];

    static jsonSchema = {
        type: 'object',
        properties: {
            id: { type: 'integer' },
            steam_name: {
                type: ['string'],
            },
            verified: {
                type: 'boolean',
            },
            hide_verified_icon: {
                type: 'boolean',
            },
            avatar: {
                type: 'string',
            },
            bet_threshold: {
                type: 'integer',
            },
            total_bet: {
                type: 'integer',
            },
            total_deposit: {
                type: 'integer',
            },
            withdraw_limit: {
                type: 'integer',
            },
            ref_id: {
                type: 'integer',
            },
            referral_code: {
                type: ['string', null],
            },
            muted_until: {
                type: ['integer', null],
            },
            mute_reason: {
                type: ['string', null],
            },
            utm_campaign: {
                type: ['string', null],
            },
            whitelisted: {
                type: 'boolean',
            },
            registration_ip: {
                type: ['string', null],
            },
            registration_timestamp: {
                type: 'datetime',
            },
            total_profit: {
                type: 'integer',
            },
            roles: {
                type: 'array',
                default: [],
            },
            chat_tag: {
                type: 'object|null',
                default: null,
            },
            badges: {
                type: ['array', null],
                default: [],
            },
        },
    };

    $parseJson(rawJson, opt) {
        const json = super.$parseJson(rawJson, opt);
        json.whitelisted = toBoolean(json.whitelisted);
        json.roles = isArray(rawJson.roles) ? rawJson.roles.map(el => el.name) : [];
        json.lvl = rawJson.level;
        return json;
    }

    get isGuest() {
        return !this.id;
    }

    get uid() {
        return this.id;
    }

    get deposited() {
        return this.total_deposit && this.total_deposit > 0;
    }

    /**
     * @returns {boolean}
     */
    get hasPrivilegedRole() {
        return (
            this.mod || this.junior_mod
        );
    }

    // eslint-disable-next-line camelcase
    get helper_mod() {
        return this.roles.includes('helper-mod');
    }

    /**
     * @returns {boolean}
     */
    // eslint-disable-next-line camelcase
    get junior_mod() {
        return (
            this.helper_mod
            || this.roles.includes('hidden-chat-mod')
        );
    }

    get mod() {
        return (
            this.roles.includes('mod')
            || this.super_mod
            || this.manager_mod
            || this.admin
        );
    }

    get super_mod() { // eslint-disable-line camelcase
        return this.roles.includes('super-mod');
    }

    get manager_mod() { // eslint-disable-line camelcase
        return this.roles.includes('manager');
    }

    get qa() {
        return this.roles.includes('qa');
    }

    get admin() {
        return this.roles.includes('admin');
    }

    get badge_text() { // eslint-disable-line camelcase
        return this.chat_tag ? this.chat_tag.text : null;
    }

    get badge_text_localized() { // eslint-disable-line camelcase
        return this.chat_tag ? this.chat_tag.localized_text : null;
    }

    get badge_color() { // eslint-disable-line camelcase
        return this.chat_tag ? this.chat_tag.color : null;
    }

    get hide_rank() { // eslint-disable-line camelcase
        return (this.chat_tag && this.chat_tag.hide_rank);
    }

    get name() {
        return this.steam_name;
    }

    get encuid() {
        if (this.isGuest) {
            return null;
        }
        if (this.encuidCached) {
            return this.encuidCached;
        }
        try {
            // Create an encrypted user ID for the chat tipping system
            const key = Buffer.from(SOCKET_DATA_ENCRYPT_SECRET, 'utf8');
            const iv = crypto.randomBytes(16); // Generate random IV, Laravel uses 16-byte IV

            const cipher = crypto.createCipheriv(SOCKET_DATA_ENCRYPT_ALGORITHM, key, iv);
            let encrypted = cipher.update(this.id.toString(), 'utf8', 'base64');
            encrypted += cipher.final('base64');

            // Generate Laravel-style payload with the IV, encrypted value, and MAC
            const ivBase64 = iv.toString('base64');
            const payload = JSON.stringify({
                iv: ivBase64,
                value: encrypted,
                mac: crypto.createHmac('sha256', key).update(ivBase64 + encrypted).digest('hex'),
            });
            this.encuidCached = Buffer.from(payload).toString('base64');
            return this.encuidCached;
        } catch (error) {
            log.error('Failed to generate encuid', error);
            return null;
        }
    }

    static guest() {
        const user = new User();
        user.roles = [];
        return user;
    }

    static async findOne(where) {
        await setImmediatePromise();

        const user = await User.findOneWithoutRoles(where);

        if (!user) {
            return user;
        }

        const [[roles], [chatTag]] = await Promise.all([
            knex.raw(`SELECT roles.name FROM roles
                INNER JOIN role_user ON roles.id = role_user.role_id
                WHERE role_user.user_id = ?`, [user.id]),
            knex.raw('SELECT * FROM users_chat_tags WHERE user_id = ?', [user.id]),
        ]);

        user.roles = roles.map(el => el.name);

        await user.loadLevels();

        if (chatTag) {
            user.chat_tag = {
                text: chatTag.content,
                localized_text: chatTag.localized_content || null,
                color: chatTag.color || null,
                hide_rank: (chatTag.flags & 1) === 1, // eslint-disable-line no-bitwise
            };
        }

        return user;
    }

    async loadLevels() {
        await setImmediatePromise();
        const lvl = await client.get(`user:${this.id}:level`);

        this.lvl = lvl ? parseInt(lvl, 10) : 0;
    }

    static async findOneWithoutRoles(where) {
        await setImmediatePromise();
        const manual = ['verified', 'hide_verified_icon', 'roles', 'chat_tag'];
        const select = [];
        Object.keys(User.jsonSchema.properties).forEach((key) => {
            if (manual.includes(key)) {
                return;
            }

            select.push(`users.${key}`);
        });

        const user = await User.query()
            .select(knex.raw(select.join(',')))
            .findOne(where);

        if (!user) {
            return null;
        }

        user.roles = [];

        return user;
    }

    toPublicJSON() {
        const user = {};
        User.publicAttributes.forEach((key) => {
            user[key] = this[key];
        });

        return user;
    }

    toJSON() {
        const user = {};
        Object.keys(User.jsonSchema.properties).forEach((key) => {
            if (User.hidden.includes(key)) return;
            user[key] = this[key];
        });
        User.virtualAttributes.forEach((key) => {
            if (User.hidden.includes(key)) return;
            user[key] = this[key];
        });

        return user;
    }

    async reload(propertyName) {
        let columnNames = propertyName;
        if (!Array.isArray(columnNames)) {
            columnNames = [columnNames];
        }
        const promises = columnNames.map(async (columnName) => {
            const response = await this.$query().select(columnName).findOne({ id: this.id });
            this[columnName] = response[columnName];
            return this[columnName];
        });
        return Promise.all(promises);
    }

    async getUpdatedUsername() {
        const { steam_name: username } = this.$query().select('steam_name').findOne({ id: this.id });
        this.steam_name = username;
        return this.steam_name;
    }

    /**
     * WARNING: If this code is changed, the corresponding Laravel code must also be changed.
     * @returns {Promise<boolean>}
     */
    async isHighRiskAccount() {
        return false;

        if (moment(this.registration_timestamp).isBefore('2019-08-16')) { // eslint-disable-line no-unreachable
            // BROZZZ in name => flag user
            if (this.steam_name.toLowerCase().startsWith('brozzz')) {
                return true;
            }
            // If from flagged IP address EXCEPT
            // - total profit < -0.8 coins
            // OR
            // - total bet > 50 coins
            const exemptFromFlaggedIp = this.total_profit < -80 || this.total_bet > 5000;
            if (!exemptFromFlaggedIp && await client.sismember('legacy-abuser-ips', this.registration_ip)) {
                return true;
            }
        } else {
            // If from flagged IP address EXCEPT
            // - total profit < -0.8 coins
            // OR
            // - total bet > 50 coins
            const exemptFromFlaggedIp = this.total_profit < -80 || this.total_bet > 5000;
            if (!exemptFromFlaggedIp && await client.sismember('abuser-ips', this.registration_ip)) {
                return true;
            }

            /*
            let gamesData;
            try {
                gamesData = JSON.parse(await client.get(`user:${this.id}:steamdata:gamesowned`));
            } catch (ignoredErr) {
                throw new Error('not ready');
            }
            if (!gamesData) {
                throw new Error('not ready');
            }

            if (gamesData.eresult === 15) {
                throw new Error('private profile');
            }

            // shouldn't happen but checking just in case
            if (gamesData.eresult !== 1) {
                throw new Error('not ready');
            }

            if (gamesData.response.game_count > 0) {
                const { games } = gamesData.response;

                // > 1 hour of CSGO, no achievements
                const csgoData = games.find(game => game.appid === 730);
                if (csgoData && csgoData.playtime_forever > 60 && csgoData.achievements.num_achieved === 0) {
                    return true;
                }

                // > 5 hours of any other game with achievements available & no achievements
                for (let i = 0; i < games.length; i++) {
                    const game = games[i];
                    if (game.achievements.num_available <= 0) continue;
                    if (game.playtime_forever > 5 * 60 && game.achievements.num_achieved === 0) {
                        return true;
                    }
                }
            }
            */
        }
        return false;
    }
}
