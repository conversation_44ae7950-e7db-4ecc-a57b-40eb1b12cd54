import Knex from 'knex';
import { logger } from '../helpers';

const log = logger('db-connection-manager');

const prefix = process.env.APP_NAME || 'csgoempire';

const connection = {
    host: process.env.DB_HOST || `${prefix}-database'`,
    user: process.env.DB_USER || prefix,
    password: process.env.DB_PASSWORD || prefix,
    database: process.env.DB_DATABASE || prefix,
    port: process.env.DB_PORT || '3306',

    supportBigNumbers: true, // set to true when converted back to bigint
    bigNumberStrings: false, // keep as false
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT, 10) || 3500,
    pool: {
        min: 10,
        max: 3500,
        acquireTimeout: 60 * 1000,
        createTimeoutMillis: 30000,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 30000,
        reapIntervalMillis: 1000,
        createRetryIntervalMillis: 100,
        propagateCreateError: false, // <- default is true, set to false
    },
};

const knexMaster = Knex({
    client: 'mysql2',
    connection,
});

let knexRead;
if (process.env.DB_HOST_REPLICA) {
    knexRead = Knex({
        client: 'mysql2',
        connection: {
            ...connection,
            host: process.env.DB_HOST_REPLICA,
            user: process.env.DB_HOST_READONLY_USER || connection.user,
        },
    });
}

// and then we will use another knex object as a wrapper
// this one doesn't need a connection or pool settings
const knexWrapper = Knex({
    client: 'mysql2',
});

// We override the runner method of our wrapper client
knexWrapper.client.runner = function runner(builder) {
    /** here we will redirect the query on the correct knex object,
    * We use this method since this is one of the first executed
    * after your query has been built and it's still before the aquireConnection process
    * */

    if (!knexRead) {
        return knexMaster.client.runner(builder);
    }

    const { _queryContext: qc } = builder;

    /** bypass with knex.select('*').queryContext({ useMaster: true })....
    * this is to force your query to be executed on read or write endpoint
    * not sure about using queryContext for this but it seems ok to me
    * */
    let useMaster;
    if (qc?.useMaster === true) {
        useMaster = true;
    } else if (qc?.useMaster === false) {
        useMaster = false;
    } else {
        // .toSQL() return an object or an array of object (not sure about that but this is what I found in /lib/runner.js)
        const sql = builder.toSQL();
        useMaster = (Array.isArray(sql) ? sql : [sql])
            .some((object) => {
                if (object.method === 'raw') {
                    return object.sql.match(/insert|delete|update|replace/i) || !(object.sql.includes('select') || object.sql.includes('SELECT'));
                }

                return ['insert', 'del', 'update'].includes(object.method);
            });
    }

    let client;
    if (useMaster) {
        ({ client } = knexMaster);
        log.debug('query on MASTER');
    } else {
        ({ client } = knexRead);
        log.debug('query on REPLICA');
    }

    return client.runner(builder);
};

knexWrapper.client.destroy = async () => {
    await knexMaster.destroy();
    if (knexRead) {
        await knexRead.destroy();
    }
};

knexWrapper.client.initialize = async () => {
    knexMaster.initialize();
    if (knexRead) {
        knexRead.initialize();
    }
};

knexWrapper.client.transaction = (...args) => knexMaster.transaction(...args);

export default knexWrapper;
