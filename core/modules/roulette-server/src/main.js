import http from 'http';
import SocketIO from 'socket.io';
import moment from 'moment';
import createAdapter from './lib/adapter';
import { knex } from './database';
import * as servers from './servers';
import {
    logger,
    toBoolean,
    getAuthTokenFromJWt,
    IS_PRODUCTION,
    SOCKET_REDIS_CHANNEL_PREFIX_KEY,
    getIp,
} from './helpers';
import { socketSub, socketPub } from './lib/redis';
import { rateLimiterMemory, rateLimiterMemoryPing } from './lib/rate-limiter';
import Namespace from './lib/namespace';
import { ForbiddenError } from './errors/client';

const log = logger('server:main');

const port = process.env.PORT || 7000;

let readyToServe = false;
let onShuttingDown = false;

const server = http.createServer((req, res) => {
    req.socket.setNoDelay(true);
    if (req.url === '/ping' || req.url === '/healthz') {
        // log.debug('health check ping');
        const httpCode = readyToServe ? 200 : 503;
        res.writeHead(httpCode, { 'Content-Type': 'text/plain' });
        res.write((new Date()).toString());
        res.end();
    } else if (req.url === '/readyz') {
        const httpCode = readyToServe && !onShuttingDown ? 200 : 503;
        res.writeHead(httpCode, { 'Content-Type': 'text/plain' });
        res.write((new Date()).toString());
        res.end();
    }
});

setInterval(() => {
    server.getConnections((error, count) => {
        if (error) {
            log.error('Failed to get connections count', error);
        } else {
            log.info('Server connections count:', count);
        }
    });
}, 30000);

const startedAt = moment();

const { SERVICE_ENABLED } = process.env;

if (SERVICE_ENABLED) {
    log.info('SERVICE_ENABLED=', SERVICE_ENABLED);
}

log.debug('Inspect port', process.env.INSPECT_PORT);

const isServiceEnabled = name => (SERVICE_ENABLED && SERVICE_ENABLED.toLowerCase() === name.toLowerCase()) || (!SERVICE_ENABLED && toBoolean(process.env[`${name.toUpperCase()}_ENABLED`]));

export default async function startServer() {
    const io = SocketIO(server, {
        allowEIO3: true, // allow old clients to connect
        adapter: createAdapter(socketPub, socketSub, { key: SOCKET_REDIS_CHANNEL_PREFIX_KEY }),
        serveClient: false,
        path: '/s',
        pingInterval: 45000,
        pingTimeout: 20000,
        requestsTimeout: 10000,
        perMessageDeflate: false,
    });

    io.engine.use(async (req, res, next) => {
        const { _query: query, headers } = req;

        if (toBoolean(process.env.JWT_SOCKET_TOKEN_DISABLED)) {
            next();
            return;
        }

        const { token, uid } = query;

        log.debug('VALIDATE HANDSHAKE', token, uid, headers);

        if (!IS_PRODUCTION && headers['x-artillery-token'] === process.env.LOAD_TEST_SECURITY_TOKEN) {
            log.debug('Handshake accepted by x-artillery-token header.');
            next();
            return;
        }

        if (!token || !uid || !uid.match(/^(guest|[0-9]+)$/)) {
            log.debug('Forbidden: handshake_token_missing_or_uid_invalid');
            next(new ForbiddenError());
            return;
        }

        try {
            getAuthTokenFromJWt(token, uid, req);
            next();
        } catch (error) {
            log.debug('Forbidden: failed_to_validate_handshake_jwt_token', error);
            next(new ForbiddenError());
        }
    });

    if (!toBoolean(process.env.RATE_LIMITER_IN_MEMORY_DISABLED)) {
        io.engine.on('connection', (socket) => {
            // eslint-disable-next-line no-underscore-dangle
            const socketRateLimiter = (type, rateLimiter, checkFn = null) => socket.transport.socket._socket.on(type, (message) => {
                if (checkFn && !checkFn(message)) {
                    // skip message
                    return;
                }

                log.debug('socket event', type, message.length);
                // eslint-disable-next-line no-underscore-dangle
                const { uid } = socket.request?._query;

                const consumer = uid !== 'guest' && uid ? uid : getIp(socket);
                rateLimiter.consume(consumer).catch(() => {
                    try {
                        log.warn('rate limit exceeded on main socket', type, consumer);
                        // eslint-disable-next-line no-underscore-dangle
                        socket.transport.socket._socket.destroy(); // close is too slow in spamming - destroy it
                    } catch (error) {
                        log.error('Failed to destroy socket', error);
                    }
                });
            });

            if (!toBoolean(process.env.RATE_LIMITER_PING_IN_MEMORY_DISABLED)) {
                socketRateLimiter('data', rateLimiterMemoryPing, chunk => chunk?.[0] === 0x89);
            }

            socketRateLimiter('data', rateLimiterMemory);
        });
    }

    const namespaces = (await Promise.all(Object.entries(servers).map(async ([name, ServerNamespace]) => {
        const namelcase = name.toLowerCase();
        const isEnabled = (SERVICE_ENABLED && SERVICE_ENABLED.toLowerCase() === namelcase) || (!SERVICE_ENABLED && toBoolean(process.env[`${name.toUpperCase()}_ENABLED`]));

        if (!isEnabled) {
            log.debug('namespace', name, 'not enabled');
            return null;
        }

        log.info('Starting namespace', name);
        const nsp = new ServerNamespace(namelcase);
        await nsp.init(io);
        return nsp;
    }))).filter(nsp => nsp && nsp instanceof Namespace);

    if (!namespaces.length) {
        throw new Error('Zero namespaces started');
    }

    let dbCheckErrorCount = 0;
    let dbCheckInterval;

    if (!isServiceEnabled('trade')) { // exclude trade-server
        dbCheckInterval = setInterval(async () => {
            try {
                const [resultRead] = await knex.raw('SELECT 1').queryContext({ useMaster: false }).timeout(5000);
                const [resultWrite] = await knex.raw('SELECT 1').queryContext({ useMaster: true }).timeout(5000);
                dbCheckErrorCount = 0;
                log.debug('Database is healthy', resultRead, resultWrite);
            } catch (error) {
                log.error('Database healthy check failed', error);
                if (error.code === 'ETIMEDOUT') {
                    try {
                        log.warn('Database healthy check failed by ETIMEDOUT error. Reset pool.');
                        await knex.destroy();
                        knex.initialize();
                    } catch (poolError) {
                        log.error('Failed to reset db pool');
                    }
                }

                dbCheckErrorCount += 1;
            }

            if (dbCheckErrorCount >= 5) {
                log.error('Too many db check errors, force kill.');
                process.exitCode = 101;
                process.kill(process.pid, 'SIGTERM');
            }
        }, 30000);
    }

    const shutdownListener = signal => process.on(signal, () => {
        if (onShuttingDown) {
            return;
        }

        onShuttingDown = true;
        log.warn('Exit with signal', signal, process.pid);

        let waiting = namespaces.length;
        let exitCode = Number(process.exitCode || 0);
        namespaces.forEach((namespace) => {
            namespace.server.once('closed', (namespaceExitCode) => {
                log.info(namespace.name, 'closed with exitCode', namespaceExitCode);
                exitCode = Math.max(exitCode, namespaceExitCode);
                waiting -= 1;

                if (waiting === 0) {
                    clearTimeout(dbCheckInterval);
                    log.info('All namespaces closed. Exit process', process.pid, 'with signal', signal, 'and exit code', exitCode);
                    process.exit(exitCode);
                }
            });
            namespace.server.emit('shutdown', signal, exitCode);
        });
    });
    shutdownListener('SIGTERM');
    shutdownListener('SIGINT');
    shutdownListener('SIGUSR2');

    log.info('Starting socket server on port', port);
    server.listen(port, () => {
        log.info('socket server is ready');
        log.info('Started in', moment().diff(startedAt) / 1000, 'seconds');
        readyToServe = true;
    });
}
