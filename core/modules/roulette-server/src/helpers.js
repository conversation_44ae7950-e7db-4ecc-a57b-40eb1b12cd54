import newrelic from 'newrelic';
import crypto from 'crypto';
import chalk from 'chalk';
import camelCase from 'lodash/camelCase';
import upperFirst from 'lodash/upperFirst';
import debug from 'debug';
import request from 'request-promise';
import Agent from 'agentkeepalive';
import cluster from 'cluster';
import jwt from 'jsonwebtoken';
import { ClientError } from './errors/client';
import { InternalApiException, BetException } from './errors/server';

const JWT_SECRET = process.env.SOCKET_JWT_SECRET || 'default-socket-token-secret';

export const SOCKET_DATA_ENCRYPT_SECRET = process.env.SOCKET_DATA_ENCRYPT_SECRET || 'default-chat-messages-secret-key';
export const SOCKET_DATA_ENCRYPT_ALGORITHM = 'aes-256-cbc';

export const IS_PRODUCTION = process.env.NODE_ENV === 'production';

export const toBoolean = (val) => {
    const falsy = /^(?:f(?:alse)?|no?|0+)$/i;
    return !falsy.test(val) && !!val;
};

export const SOCKET_REDIS_CHANNEL_PREFIX_KEY = process.env.SOCKET_REDIS_KEY || 'empire.socket-server';

const DISABLE_KEEPALIVE_AGENT = toBoolean(process.env.DISABLE_KEEPALIVE_AGENT);

const DISABLE_PROVABLY_FAIR = toBoolean(process.env.DISABLE_PROVABLY_FAIR);

if (DISABLE_PROVABLY_FAIR && IS_PRODUCTION) {
    throw new Error('PROVABLY_FAIR cannot be disabled in production mode');
}

const loggerInterfaces = {};

export const logger = (key) => {
    if (loggerInterfaces[key]) {
        return loggerInterfaces[key];
    }

    const worker = cluster.isMaster ? 'workerMaster' : `worker${cluster.worker.id}`;
    const d = level => debug(`socket-server:${level.toUpperCase()}:${worker}:${key}`);
    const levels = ['error', 'debug', 'info', 'warn'];
    const loggers = {};
    levels.forEach((level) => {
        let levelPrefix = level;
        if (level === 'error') levelPrefix = chalk.red(level);
        if (level === 'warn') levelPrefix = chalk.yellow(level);
        const newRelicErrorLevels = (process.env.NEW_RELIC_ERROR_LEVELS || 'error').split(',').map(l => l.trim());
        const reportToNewrelic = !process.env.JEST_WORKER_ID
            && newRelicErrorLevels.includes(level) && toBoolean(process.env.NEW_RELIC_ENABLED) === true;
        const loggerInstance = d(levelPrefix);
        loggers[level] = reportToNewrelic
            ? (...args) => {
                const error = args.find(arg => arg instanceof Error);
                if (error) {
                    newrelic.noticeError(error);
                } else if (args.length > 0 && typeof args[0] === 'string') {
                    newrelic.noticeError(new Error(args[0]));
                } else if (args.length > 0 && args[0]?.message) {
                    newrelic.noticeError(new Error(args[0].message));
                } else if (args.length > 0) {
                    newrelic.noticeError(new Error(JSON.stringify(args[0])));
                }

                loggerInstance(...args);
            }
            : loggerInstance;
    });

    loggerInterfaces[key] = loggers;

    return loggers;
};

export const sleep = x => new Promise(resolve => setTimeout(resolve, x));

export const sha256 = str => crypto.createHash('sha256').update(str, 'utf8').digest();

export const getHookActionName = eventName => camelCase(`on${upperFirst(eventName)}`);

export const slack = async (message, options = {}) => {
    const log = logger('helpers:slack:[NOTIFICATION]');
    const token = options.token || process.env.SLACK_OAUTH_TOKEN;
    const appName = process.env.APP_NAME || 'Unknown App';

    if (!token) {
        log.error('No Slack token provided');
        return;
    }

    try {
        let channel = options.channel || process.env.NOTIFICATION_CHANNEL || '#roulette-notify';

        if (channel && !channel.startsWith('#')) {
            channel = `#${channel}`;
        }

        // Prepend APP_NAME to the message
        const fullMessage = `[${appName}] ${message}`;

        log.info('slack message to channel', channel, ' -- ', fullMessage);

        const response = await request.post('https://slack.com/api/chat.postMessage', {
            headers: {
                Authorization: `Bearer ${token}`,
            },
            form: {
                channel,
                text: fullMessage,
                username: 'Roulette Server',
                icon_emoji: ':robot_face:',
                ...options,
            },
            json: true,
        });

        if (!response.ok) {
            log.error(`Slack API error: ${response.error}`);
            throw new Error(`Slack API error: ${response.error}`);
        }

        log.info('Message sent successfully:', response);
    } catch (error) {
        log.error('Error sending message to Slack:', error);
    }
};

export const handleException = async (e, type, msg, data) => {
    const log = logger('EXCEPTION');

    const err = e instanceof BetException && e.apiError ? e.apiError : e;

    const errObj = {
        type,
        msg,
        data: {
            ...err?.data,
            ...data,
            security_token: data?.security_token ? '****' : undefined, // eslint-disable-line camelcase
        },
        err: err?.stack || err,
        debug_message: err?.debugMessage, // eslint-disable-line camelcase
        timestamp: Math.round(Date.now() / 1000),
    };

    if (err instanceof InternalApiException) {
        errObj.url = err.url;
        errObj.exception = err.exception;
        errObj.userId = err.userId;
    }

    const pretty = JSON.stringify(errObj, null, 2);

    if (err instanceof ClientError) {
        log.debug('Ignore ClientError notify', pretty);
        return;
    }

    log.error(pretty);

    if (!err || !err.toString().includes('This socket has been ended by the other party')) {
        let content = '';
        if (errObj.type === 'CRITICAL') {
            content += '<!channel> **CRITICAL ERROR OCCURRED!** ';
        } else {
            content += `Error occurred at \`${errObj.type}\`: `;
        }
        if (errObj.msg) {
            content += `*${errObj.msg}*`;
        }
        if (errObj.err && !errObj.debug_message) {
            content += `\n\n${errObj.err}`;
        }
        if (err instanceof InternalApiException) {
            content += `\n\nInternal api request to \`${errObj.url}\`, with user id \`${errObj.userId}\` got exception \`${errObj.exception}\``;
        }
        if (errObj.debug_message) {
            content += `\n\n${errObj.debug_message}`;
        }
        if (errObj.data) {
            content += `\n\`\`\`${JSON.stringify(errObj.data, null, 2)}\`\`\``;
        }
        try {
            await slack(content);
        } catch (slackErr) {
            log.error('Failed to send slack notification', slackErr);
        }
    }
};

let keepaliveAgent = null;
let keepaliveAgentChecker = null;
function getKeepaliveAgent(reset = false) {
    if (DISABLE_KEEPALIVE_AGENT) {
        return null;
    }

    if (keepaliveAgent && !reset) {
        return keepaliveAgent;
    }

    keepaliveAgent = new Agent({
        keepAlive: true,
        maxSockets: Infinity,
        maxFreeSockets: 512,
        timeout: 60000, // active socket keepalive for 60 seconds
        freeSocketTimeout: 30000, // free socket keepalive for 30 seconds
        socketActiveTTL: null,
    });

    const log = logger('helpers:keepaliveAgent');

    clearInterval(keepaliveAgentChecker);
    keepaliveAgentChecker = setInterval(() => {
        if (!keepaliveAgent.statusChanged) {
            return;
        }

        log.info('agent status changed:', keepaliveAgent.getCurrentStatus());
    }, 2000);

    return keepaliveAgent;
}

// https://snyk.io/blog/nodejs-how-even-quick-async-functions-can-block-the-event-loop-starve-io/
export function setImmediatePromise() {
    return new Promise((resolve) => {
        setImmediate(() => resolve());
    });
}

export async function internalApiRequest(url, data, user = null, options = { }) {
    const opts = {
        retries: 5,
        retryCount: 0,
        method: 'POST',
        agent: getKeepaliveAgent(),
        timeout: 15000,
        ...options,
    };

    if (!opts.startedAt) {
        opts.startedAt = Date.now();
    }

    let userInfo = { 'x-empire-socket-user-id': 'general' };
    if (user) {
        userInfo = {
            'x-empire-socket-user-id': user.id,
            'cf-ipcountry': user.country,
            'x-forwarded-for': user.ip,
            'x-empire-domain': user.hostname,
            'x-empire-session-id': user.session?.id,
        };
    }

    const baseUrl = process.env.INTERNAL_API_URL || `http://${process.env.APP_NAME || 'csgoempire'}.backend/api/v2/socket`;

    const log = logger('helpers:api');

    log.debug('Send internal api request', baseUrl, url);

    try {
        await setImmediatePromise();
        const result = await request({
            ...opts,
            baseUrl,
            url,
            headers: {
                ...opts.headers,
                ...userInfo,
                'x-empire-socket-api-token': process.env.INTERNAL_API_TOKEN,
                'x-empire-server-timestamp': opts.startedAt,
                'Content-Type': 'application/json',
            },
            json: true,
            body: data,
        });

        log.info('Internal api request handled in', Date.now() - opts.startedAt, 'milliseconds with', opts.retryCount, 'retries');

        return result;
    } catch (error) {
        if (error.statusCode && error.statusCode < 500) {
            let message = null;
            if (error?.error?.message) {
                message = error?.error?.message;
            } else if (error.statusCode === 422 && typeof error.error === 'object') {
                const parts = Object.values(error.error);
                message = parts?.[0]?.[0];
            }

            throw new ClientError(message, error.statusCode, { data: error.error });
        }

        if (error?.error?.code === 'ESOCKETTIMEDOUT') {
            log.warn('ESOCKETTIMEDOUT error detected, reset keepalive agent');
            getKeepaliveAgent(true);
        }

        if (opts.retryCount < opts.retries) {
            return internalApiRequest(url, data, user, { ...opts, retryCount: opts.retryCount + 1 });
        }

        if (error.statusCode && error.statusCode >= 500) {
            throw new InternalApiException(error);
        }

        throw error;
    }
}

export function getIp(socket) {
    let ip = socket.handshake?.address || socket.remoteAddress;
    const { headers } = socket?.request || {};
    if (headers && 'cf-connecting-ip' in headers && headers['cf-connecting-ip'] !== '') {
        ip = headers['cf-connecting-ip'];
    } else if (headers && 'x-forwarded-for' in headers && headers['x-forwarded-for'] !== '') {
        ip = headers['x-forwarded-for'];
    }

    if (ip && ip.includes(',')) {
        ip = ip.split(',')[0].trim();
    }

    return ip;
}

export function getSubjectForAuthToken(uid, req) {
    if (uid !== 'guest') {
        return parseInt(uid, 10);
    }

    const { headers } = req?.request || req || {};
    const ua = headers?.['user-agent'];
    return ua ? crypto.createHash('md5').update(ua).digest('hex') : null;
}

export function getAuthTokenFromJWt(authorizationToken, uid, req) {
    const subject = getSubjectForAuthToken(uid, req);

    if (!subject) {
        throw new Error('Cannot find subject for jwt');
    }
    // if token is invalid, this will throw an error
    const { jti } = jwt.verify(authorizationToken, JWT_SECRET, {
        algorithms: ['HS256'],
        audience: process.env.APP_NAME,
        subject,
    });

    return { token: jti, subject };
}

const currencyMap = {
    csgoempire: { env: 'csgoempire', symbol: '', name: 'coins' },
    duel: { env: 'duel', symbol: '$', name: '' },
};

export function getAppCurrency() {
    return currencyMap[(process.env.APP_NAME || '').toLowerCase()] || currencyMap.csgoempire;
}

export const padZero = (num, size = 2) => String(num).padStart(size, '0');
