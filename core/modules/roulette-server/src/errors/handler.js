import snakeCase from 'lodash/snakeCase';
import {
    ClientError,
    NotFoundError,
    DataNotFoundError,
} from './client';
import {
    DBError,
    UniqueViolationError,
    NotNullViolationError,
    ForeignKeyViolationError,
    CheckViolationError,
    DataError,
    ValidationError,
} from './database';
import {
    ServerError,
} from './server';
import { logger, IS_PRODUCTION } from '../helpers';

const log = logger('error-handler');

export function normalizeError(error) {
    return error;
}

export default class ErrorResponse {
  statusCode;

  defaultError = {
      type: 'UnknownError',
      message: 'Internal server error',
      code: 'internal_server_error',
      data: {},
  };

  JSON = this.defaultError;

  constructor(error, context = undefined) {
      this.error = normalizeError(error);
      this.context = context;
  }

  get type() {
      return this.JSON.type;
  }

  set type(type) {
      this.JSON.type = type;
  }

  get code() {
      return this.JSON.code;
  }

  set code(code) {
      this.JSON.code = code;
  }

  get message() {
      return this.JSON.message;
  }

  set message(message) {
      this.JSON.message = message;
  }

  get data() {
      return this.JSON.data;
  }

  set data(data) {
      this.JSON.data = data || undefined;
  }

  buildValidationError() {
      this.statusCode = 400;
      this.type = 'ValidationError';
      this.data = this.error.data;
      this.code = 'validation_error';
  }

  buildNotFoundError() {
      this.statusCode = 404;
      this.type = 'NotFound';
      this.message = this.error.message;
  }

  buildClientError() {
      this.type = 'ClientError';
      this.data = Object.values(this.error.data).length > 0 ? this.error.data : undefined;
  }

  buildServerError() {
      this.type = 'ServerError';
      this.data = Object.values(this.error.data).length > 0 ? this.error.data : undefined;
  }

  buildJsonWebTokenError() {
      this.type = 'JsonWebTokenError';
      if (this.code === 'jwt_expired') {
          this.data = { expiredAt: this.error.data.expiredAt };
      }
      if (this.code === 'jwt_not_active') {
          this.data = { date: this.error.data.date };
      }
  }

  buildDatabaseError() {
      this.type = 'UnknownDatabaseError';
      if (this.error instanceof UniqueViolationError) {
          this.type = 'UniqueViolationError';
          this.data = {
              columns: this.error.columns,
              table: this.error.table,
              constraint: this.error.constraint,
          };
      } else if (this.error instanceof NotNullViolationError) {
          this.type = 'NotNullViolationError';
          this.data = {
              column: this.error.column,
              table: this.error.table,
          };
      } else if (this.error instanceof ForeignKeyViolationError) {
          this.type = 'ForeignKeyViolationError';
          this.data = {
              table: this.error.table,
              constraint: this.error.constraint,
          };
      } else if (this.error instanceof CheckViolationError) {
          this.type = 'CheckViolationError';
          this.data = {
              table: this.error.table,
              constraint: this.error.constraint,
          };
      } else if (this.error instanceof DataError) {
          this.type = 'InvalidDataError';
      }

      const hiddenMessages = ['UnknownDatabaseError', 'UniqueViolationError', 'NotNullViolationError'];
      if (IS_PRODUCTION && hiddenMessages.includes(this.type)) {
          this.message = this.type;
          this.code = snakeCase(this.message);
      }
  }

  build() {
      try {
          log.debug('Build error response from', this.error);

          const { message, code } = this.error;

          this.statusCode = this.error.statusCode || 500;
          this.message = message;
          this.code = code || snakeCase(message);
          if (this.error instanceof ValidationError) {
              this.buildValidationError();
          } else if (this.error instanceof NotFoundError
              || this.error instanceof DataNotFoundError
          ) {
              this.buildNotFoundError();
          } else if (this.error instanceof ClientError) {
              this.buildClientError();
          } else if (this.error instanceof DBError) {
              this.buildDatabaseError();
          } else if (this.error instanceof ServerError) {
              this.buildServerError();
          }
      } catch (error) {
          log.error('errorHandlingError', error);
          this.statusCode = 500;
          this.JSON = this.defaultError;
      }
  }
}
