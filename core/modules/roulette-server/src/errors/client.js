/* eslint-disable max-classes-per-file */
import BaseError from './base';
import { DataNotFoundError } from './database';

export { DataNotFoundError };

export class ClientError extends BaseError {
    type = 'client'

    constructor(message, statusCode = 400, options = {}) {
        super(message || 'There was unknown error.', statusCode >= 500 ? 400 : statusCode, options);
    }
}

export class UnauthorizedError extends ClientError {
    type = 'auth'

    constructor(message) {
        super(message || 'Unauthorized', 401);
    }
}

export class NotFoundError extends ClientError {
    type = 'not_found'

    constructor(message) {
        super(message || 'NotFound', 404);
    }
}

export class ForbiddenError extends ClientError {
    type = 'access'

    constructor() {
        super('Forbidden', 403);
    }
}

export class BetError extends ClientError {
    type = 'bet'

    data = {}

    constructor(message, data = {}) {
        super(message || 'There was an error placing your bet. Please try again later.');
        this.data = data;
    }
}

export default ClientError;
