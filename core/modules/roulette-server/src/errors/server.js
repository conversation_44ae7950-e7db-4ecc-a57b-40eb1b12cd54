/* eslint-disable max-classes-per-file */
import BaseError from './base';

export class ServerError extends BaseError {
    constructor(message, statusCode = 500, options = {}) {
        super(message, statusCode < 500 ? 500 : statusCode, options);
    }
}

export class NotImplementedError extends Server<PERSON>rror {
    constructor() {
        super('Not implemented', 501);
    }
}

export class InternalApiException extends ServerError {
    debugMessage = null

    constructor(err) {
        const { error, options } = err;

        super(error.message);
        this.debugMessage = error.debug_message;
        this.trace = error.trace;
        this.exception = error.exception;
        this.data = options.body;
        this.userId = options.headers['x-empire-socket-user-id'];
        this.url = options.baseUrl + options.url;
    }
}

export class BetException extends ServerError {
    trace = null;

    data = null;

    title = null;

    apiError = null;

    constructor(title, error, trace, data) {
        super(error instanceof InternalApiException ? error.message : (error || 'Unknown exception while placing bet.'));
        this.title = title;
        this.trace = trace;
        this.data = data;

        if (error instanceof InternalApiException) {
            this.apiError = error;
        }
    }
}

export default ServerError;
