export default class BaseError extends Error {
    constructor(message, statusCode = 400, options = {}) {
        super(message);

        this.name = this.constructor.name;
        this.statusCode = statusCode;
        if (typeof Error.captureStackTrace === 'function') {
            Error.captureStackTrace(this, this.constructor);
        } else {
            this.stack = (new Error(message)).stack;
        }

        const opts = {
            popup: true,
            data: {},
            message_localized: {},
            ...options,
        };

        this.popup = opts.popup;
        this.data = opts.data;
        this.message_localized = opts.message_localized;
        this.isClientError = this.statusCode < 500;
    }

    setLocalizedMessage(key, params = null) {
        this.message_localized = { key, params };
        return this;
    }
}
