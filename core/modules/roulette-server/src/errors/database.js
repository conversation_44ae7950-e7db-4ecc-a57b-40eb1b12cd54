/* eslint-disable max-classes-per-file */

import {
    ValidationError,
    NotFoundError as DataNotFoundError,
} from 'objection';
import {
    DBError,
    UniqueViolationError,
    NotNullViolationError,
    ForeignKeyViolationError,
    CheckViolationError,
    DataError,
} from 'objection-db-errors';

export {
    DBError,
    UniqueViolationError,
    NotNullViolationError,
    ForeignKeyViolationError,
    CheckViolationError,
    DataError,
    ValidationError,
    DataNotFoundError,
};

export default function wrapDBError(error) {
    if (!(error instanceof DBError)) {
        return error;
    }

    const { nativeError } = error;

    if (!nativeError) {
        return error;
    }

    return error;
}
