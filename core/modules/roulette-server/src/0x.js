/* eslint-disable no-console */
import path from 'path';
import zeroEks from '0x';
import fs from 'fs-extra';

const name = process.env.NAME_0X;
console.log('Running with 0x', name);
console.log('cwd', process.cwd());

async function capture() {
    const opts = {
        argv: [path.join(__dirname, 'socket.js')],
        workingDir: process.cwd(),
        outputDir: path.join(process.cwd(), '0x', `${name}.0x`),
        title: name,
    };

    console.log('0x opts', opts);

    try {
        const file = await zeroEks(opts);
        console.log(`flamegraph in ${file}`);
        await fs.move(opts.outputDir, path.join(process.cwd(), 'logs', `${name}.0x`));
    } catch (e) {
        console.error('0x failure', e);
    }
}

capture();
