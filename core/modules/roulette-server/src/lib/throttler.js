import throttle from 'lodash/throttle';
import flattenDeep from 'lodash/flattenDeep';
import { logger } from '../helpers';

const ROOM_GLOBAL = 'global';

export default class Throttler {
    auto = false;

    queues = {};

    onPushCallback = () => null;

    constructor(server, callback, opts = {}) {
        this.log = logger(`throttles:${opts.name || 'unknown'}`);
        this.name = opts.name;
        this.server = server;
        this.timeout = opts.timeout || 200;
        this.maxCount = opts.maxCount || 20;
        this.auto = opts.auto || false;
        this.callback = typeof callback === 'function' ? callback : (items, room) => this.defaultCallback(items, room, opts.flatten);
        this.throttle = throttle(() => this.flush(), this.timeout, { trailing: true, leading: false });
    }

    onPush(callback) {
        this.onPushCallback = callback;
    }

    push(originalItem, roomsSet = new Set()) {
        const rooms = Array.isArray(roomsSet) ? new Set(roomsSet) : roomsSet;
        const queueRooms = rooms instanceof Set && rooms.size > 0 ? rooms : new Set([ROOM_GLOBAL]);

        const item = this.onPushCallback(originalItem) || originalItem;

        queueRooms.forEach((room) => {
            if (!this.queues[room]) {
                this.queues[room] = {
                    queue: [],
                    flushedAt: Date.now(),
                };
            }

            this.queues[room].queue.push(item);
            if (this.queues[room].queue.length >= this.maxCount) {
                this.flush(room);
            } else {
                this.throttle();
            }
        });
    }

    clear(room = ROOM_GLOBAL) {
        this.queues[room] = {
            queue: [],
            flushedAt: Date.now(),
        };
    }

    async stop() {
        this.cancel();
        await this.flush();
    }

    cancel() {
        this.throttle.cancel();
    }

    defaultCallback = (items, room, flatten = false) => {
        this.log.debug(`flush throttler ${this.name} to ${room}`, items);

        const { local } = this.server.socket;
        const emitter = room && room !== ROOM_GLOBAL ? local.to(room) : local;
        emitter.emit(this.name, flatten ? flattenDeep([items]) : items);
    }

    async flush(roomToFlush = null) {
        const rooms = roomToFlush ? [roomToFlush] : Object.keys(this.queues);
        await Promise.allSettled(rooms.map(async (room) => {
            try {
                const queue = Array.isArray(this.queues?.[room]?.queue) ? [...this.queues[room].queue] : [];
                if (queue.length <= 0) {
                    return;
                }

                this.queues[room].queue = [];

                await this.callback(queue, room, this);

                this.queues[room].flushedAt = Date.now();
            } catch (error) {
                this.log.error('Failed to flush queue', room, error);
            }
        }));
    }
}
