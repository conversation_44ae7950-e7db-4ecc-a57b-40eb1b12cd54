import sortBy from 'lodash/sortBy';
import countBy from 'lodash/countBy';
import sumBy from 'lodash/sumBy';
import groupBy from 'lodash/groupBy';
import mapValues from 'lodash/mapValues';
import BigNumber from 'bignumber.js';
import { socketPub as client } from './redis';
import Server from './server';
import { sleep } from '../helpers';

const DEFAULT_FLUSH_INTERVAL = 200; // how often we flush bets
const DEFAULT_MAX_EMIT_BET_ARRAY = 10;

const events = {
    addBet: 'addBet',
    flushBets: 'flushBets',
};

export default class ServerGame extends Server {
    betKeys = {}

    betCount = 0;

    bets = null;

    flushInterval = DEFAULT_FLUSH_INTERVAL;

    constructor(nsp) {
        super(nsp);
        this.bets = this.createBetsObject();
        this.createThrottler('bets', this.flushBets, { timeout: DEFAULT_FLUSH_INTERVAL, maxCount: 20 });
    }

    get keys() {
        return [this.name];
    }

    get game() {
        return this.masterModule;
    }

    get round() {
        return this.module?.round;
    }

    createBetsObject() {
        const bets = {};
        this.keys.forEach((key) => {
            bets[key] = {};
            bets[`${key}Count`] = 0;
            bets[`${key}Amount`] = 0;
            bets[`${key}Max`] = [];
        });

        return bets;
    }

    async run() {
        if (this.isSlave) {
            this.log.info('Total bets handled by this worker on last round=', this.betCount);
        }

        // clear and reset memory on every round
        this.betKeys = {};
        this.betCount = 0;
        this.throttler('bets').clear();
        this.bets = this.createBetsObject();

        if (this.isMaster) {
            try {
                this.log.debug('Reset pending bets');
                await client.set(`${this.name}:pending-bets`, 0);
            } catch (error) {
                this.log.error('Failed to reset pending bets');
            }
        }

        await super.run();
    }

    addBet(newBet) {
        const bet = newBet;
        this.betCount += 1;
        bet.isCountable = !this.bets[bet.key][bet.uid];
        this.onAddBet(null, bet);
        this.throttler('bets').push(bet);
    }

    onAddBet(from, bet) {
        if (from === this.uid) {
            // ignore own since it is already added directly
            return;
        }
        const {
            key,
            uid,
            amount,
            amount_currency: amountCurrency,
            amount_coins: amountCoins,
        } = bet;
        this.log.debug(`Added bet of ${amount} on ${key} from user #${uid} into list.`);

        if (uid in this.bets[key]) {
            this.bets[key][uid].amount += amount;
            const betAmountCurrency = new BigNumber(this.bets[key][uid].amount_currency);
            const amountCurrencyNr = new BigNumber(amountCurrency);
            this.bets[key][uid].amount_currency = betAmountCurrency.plus(amountCurrencyNr).toFixed(18);
            const betAmountCoins = new BigNumber(this.bets[key][uid].amount_coins);
            const amountCoinsNr = new BigNumber(amountCoins);
            this.bets[key][uid].amount_coins = betAmountCoins.plus(amountCoinsNr).toFixed(6);
            const indx = this.bets[`${key}Max`].findIndex(b => b.uid === uid);
            if (indx > -1) {
                this.bets[`${key}Max`][indx].amount += amount;
                const betMaxAmountCurrency = new BigNumber(this.bets[`${key}Max`][indx].amount_currency);
                const amountMaxCurrency = new BigNumber(amountCurrency);
                this.bets[`${key}Max`][indx].amount_currency = betMaxAmountCurrency.plus(amountMaxCurrency).toFixed(18);
                const betMaxAmountCoins = new BigNumber(this.bets[`${key}Max`][indx].amount_coins);
                const amountMaxCoins = new BigNumber(amountCoins);
                this.bets[`${key}Max`][indx].amount_coins = betMaxAmountCoins.plus(amountMaxCoins).toFixed(6);
            } else {
                this.bets[`${key}Max`].push({ ...this.bets[key][uid] });
            }
        } else {
            this.bets[key][uid] = { ...bet };
            this.bets[`${key}Count`] += 1;
            this.bets[`${key}Max`].push({ ...bet });
        }

        this.bets[`${key}Max`] = sortBy(this.bets[`${key}Max`], 'amount');
        const len = this.bets[`${key}Max`].length;
        if (len > DEFAULT_MAX_EMIT_BET_ARRAY) {
            this.bets[`${key}Max`].splice(0, len - DEFAULT_MAX_EMIT_BET_ARRAY);
        }

        this.bets[`${key}Amount`] += amount;

        if (!this.masterModule) {
            return;
        }

        this.masterModule.addBet(bet);
    }

    flushBets = async (bets) => {
        try {
            // Filter emitted bets to only those in the max list.
            const emitBets = bets.filter(bet => this.bets[`${bet.key}Max`].find(({ uid }) => uid === bet.uid));
            const emitData = {
                round: this.round,
                bets: emitBets,
                amount: mapValues(groupBy(bets, 'key'), group => sumBy(group, 'amount')),
                count: countBy(bets.filter(({ isCountable }) => isCountable), 'key'),
            };

            this.socket.emit('bet', emitData);

            await this.internalRequest.send(events.flushBets, bets);
        } catch (error) {
            this.log.error('Failed to flush bets', error);
        }
    }

    onFlushBets(from, bets) {
        bets.forEach(bet => this.onAddBet(from, bet));
    }

    async getPendingBets() {
        return parseInt(await client.get(`${this.name}:pending-bets`), 10) || 0;
    }

    async waitPendingBets(timeout = 3000, startWait = null) {
        const pendingBets = await this.getPendingBets();
        if (pendingBets <= 0) {
            return;
        }

        if (!startWait) {
            this.log.info(`Waiting for ${pendingBets} bets to finish processing...`);
        } else if (Date.now() - startWait > timeout) {
            this.log.info(`More than ${timeout / 1000}s elapsed since we started waiting for pending bets to finish. Resolving now despite still have ${pendingBets} bets`);
            return;
        }

        const startedAt = startWait || Date.now();
        await sleep(100);
        await this.waitPendingBets(timeout, startedAt);

        if (!startWait) {
            this.log.info('Done waiting for pending bets in', Date.now() - startedAt, 'milliseconds');
        }
    }

    getCurrentState(user) {
        const currentState = {
            ...super.getCurrentState(user),
            bets: {},
            amount: {},
            count: {},
            state: this.module?.state,
            ...this.module?.stateData,
        };

        this.keys.forEach((key) => {
            const bets = this.bets[`${key}Max`];
            if (user && !bets.find(b => b.uid === user.uid) && user.uid in this.bets[key]) {
                bets.push(this.bets[key][user.uid]);
            }

            currentState.bets[key] = bets;
            currentState.count[key] = this.bets[`${key}Count`];
            currentState.amount[key] = this.bets[`${key}Amount`];
        });

        return currentState;
    }
}
