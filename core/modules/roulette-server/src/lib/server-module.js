import ServerBase from './server-base';
import {
    logger,
} from '../helpers';

export default class ServerModule extends ServerBase {
    logSuffix = 'unknown module';

    server = null;

    constructor(server) {
        super();
        this.server = server;
    }

    get log() {
        return logger(`${this.server.loggerPrefix}:${this.logSuffix}`);
    }

    async stop() {
        await this.stopThrottlers();
    }

    static async create(server) {
        return new this(server);
    }
}
