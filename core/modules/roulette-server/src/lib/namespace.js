import omit from 'lodash/omit';
import User from './user';
import BetUser from './bet-user';
import Server, { SCALING_HORIZONTAL_WITH_MASTER } from './server';
import GameServer from './server-game';
import {
    logger, sleep, getIp, toBoolean,
} from '../helpers';
import { rateLimiter } from './rate-limiter';
import { ClientError } from '../errors/client';

export default class Namespace {
    scaling = SCALING_HORIZONTAL_WITH_MASTER;

    UserClass = User;

    resettedCount = 0;

    resetting = false;

    constructor(name) {
        this.log = logger(`namespace:${name}`);
        this.name = name;
    }

    async init(io) {
        this.socket = io.of(`/${this.name}`);

        await sleep(500);

        await this.initServer();

        this.log.info('Server is ready.');

        this.socket.on('connection', async (socket) => {
            this.log.debug('new socket connected', socket.id, 'on', this.name);

            socket.emitError = this.setSocketEmitError(socket); // eslint-disable-line no-param-reassign

            socket.queue = []; // eslint-disable-line no-param-reassign

            socket.use(this.rateLimiter(socket));
            socket.use(this.identifierCheck(socket));

            if (!this.server || !this.server.isReadyToAcceptConnections || !this.server.module) {
                let reason = 'server-not-ready';
                if (!this.server) {
                    reason = 'server-not-found';
                } else if (!this.server.module) {
                    reason = 'server-module-not-ready';
                }
                socket.emit('force_reconnect', { reason, event: 'connection' });
                return;
            }

            try {
                const notificationNamespace = process.env.NOTIFICATION_NAMESPACE || 'notifications';
                const getNotificationNsp = () => {
                    if (this.name === notificationNamespace) {
                        return socket;
                    }

                    const nsp = `/${notificationNamespace}`;
                    return socket.client.nsps ? socket.client.nsps.get(nsp) : null;
                };

                const pushPendingNotification = (data) => {
                    if (!socket.pendingNotifications) {
                        socket.pendingNotifications = []; // eslint-disable-line no-param-reassign
                    }

                    socket.pendingNotifications.push(data);
                };

                socket.notification = { // eslint-disable-line no-param-reassign
                    emit(e, data) {
                        const nsp = getNotificationNsp();

                        if (!nsp) {
                            pushPendingNotification({ e, data, broadcast: false });
                            return;
                        }

                        nsp.emit(e, data);
                    },
                    broadcast(e, data) {
                        const nsp = getNotificationNsp();

                        if (!nsp) {
                            pushPendingNotification({ e, data, broadcast: true });
                            return;
                        }

                        nsp.broadcast.emit(e, data);
                    },
                };

                const authNsp = Object.values(socket.client.nsps).find(nsp => nsp.user && nsp.user.authenticated);
                const authorizedUser = authNsp ? authNsp.user : null;

                const user = this.createUser(socket);
                this.server.addUser(user);
                socket.user = user; // eslint-disable-line no-param-reassign

                if (authorizedUser) {
                    user.identifying = true;
                }

                await user.authorize(authorizedUser ? authorizedUser.user.$clone() : null);
                user.identifying = false;
            } catch (error) {
                this.log.error('Failed to handle new connection for the socket', socket.id, error);
                this.server.removeUser(socket.id);
                socket.disconnect(true);
            }
        });
    }

    createServer() {
        const server = new this.ServerClass(this);

        if (!(server instanceof Server)) {
            throw new Error('this.server needs to be an instance of Server');
        }

        return server;
    }

    createUser(socket) {
        const user = new this.UserClass(socket, this.server);

        if (!(user instanceof User)) {
            throw new Error('User needs to be instance of User');
        }

        if (!(user instanceof BetUser) && this.server instanceof GameServer) {
            throw new Error('User needs to be an instance of BetUser for GameServer');
        }

        return user;
    }

    async initServer() {
        this.server = this.createServer();

        this.server.on('done', () => {
            this.log.info('Server round done...');
        });
        this.server.on('error', (error) => {
            this.log.error('Server emitted error', error);
        });
        await this.server.start();

        this.log.info('Start process done. Wait to be ready.');
        await this.server.ready();
    }

    setSocketEmitError(socket) {
        return (err, popup = true, disconnect = undefined) => {
            const error = typeof err === 'string' ? new ClientError(err, 400, { popup }) : err;

            const restrictedData = ['security_token'];

            if (err?.data?.backtrace && !err?.data?.backtrace?.bypass) {
                restrictedData.push('backtrace');
            }

            let ack;
            if (disconnect !== undefined) {
                const ackTimeout = setTimeout(() => socket?.disconnect(!!disconnect), 1000);
                ack = () => {
                    clearTimeout(ackTimeout);
                    socket.disconnect(!!disconnect);
                };
            }

            socket.notification.emit('err', {
                error: error instanceof ClientError ? error.message : 'Something went wrong. Please try again later.',
                popup: error instanceof ClientError ? error.popup : popup,
                data: error && error.data ? omit(error.data, restrictedData) : undefined,
                type: `${this.server.name}/${error && error.type ? error.type : 'unknown'}`,
                nsp: this.server.name.replace('/', ''),
                message_localized: error?.message_localized?.key ? error.message_localized : undefined,
            }, ack);
        };
    }

    rateLimiter(socket) {
        return async (packet, next) => {
            if (toBoolean(process.env.RATE_LIMITER_DISABLED)) {
                next();
                return;
            }

            const consumer = socket.user?.id || getIp(socket);
            try {
                await rateLimiter.consume(consumer);
                next();
            } catch (erro) {
                this.log.info('rate limit exceeded', consumer, erro);
                socket.emitError(new ClientError('Rate limit exceeded. Please try again later.', 429));
                socket.disconnect(true);
            }
        };
    }

    identifierCheck(socket) { // eslint-disable-line class-methods-use-this
        return async (packet, next) => {
            if (socket.user?.isAdded || packet[0] === 'identify' || toBoolean(process.env.IDENTIFIER_CHECK_DISABLED)) {
                next();
                return;
            }

            socket.queue.push([packet, next]);

            if (socket.initTimeout) {
                return;
            }

            socket.initTimeout = setTimeout(async () => { // eslint-disable-line no-param-reassign
                if (!socket || socket.user?.isAdded) {
                    return;
                }

                socket.queue = []; // eslint-disable-line no-param-reassign
                socket.emitError(new ClientError('Socket connection is not initialized or identified!', 400), true);
            }, 5000);
        };
    }
}
