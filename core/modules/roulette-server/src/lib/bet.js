import { Random, MersenneTwister19937 } from 'random-js';
import { omit } from 'lodash';
import { BetError, ClientError } from '../errors/client';
import { BetException } from '../errors/server';
import {
    logger, handleException, internalApiRequest, setImmediatePromise, toBoolean,
} from '../helpers';
import { socketPub as client } from './redis';

const random = new Random(MersenneTwister19937.autoSeed());

export default class Bet {
    server = null;

    socket = null;

    amount = null;

    data = {};

    betResponse = {};

    success = false;

    constructor(user, server, socket) {
        this.user = user;
        this.server = server;
        this.socket = socket;
        this.id = random.string(32).toString();

        this.log = logger(`${server.loggerPrefix}:main-lib:bet`);
    }

    get key() {
        return this.server.name;
    }

    get roundKey() { // eslint-disable-line class-methods-use-this
        throw new Error('Bet round key not implemented');
    }

    get minBet() { // eslint-disable-line class-methods-use-this
        return process.env.MIN_BET;
    }

    get maxBet() { // eslint-disable-line class-methods-use-this
        return process.env.MAX_BET || Infinity;
    }

    get isNativeCurrenciesEnabled() { // eslint-disable-line class-methods-use-this
        return toBoolean(process.env.USE_NATIVE_CURRENCIES || false);
    }

    setBetData(data) {
        const { amount, round, currency } = data;
        if (this.isNativeCurrenciesEnabled) {
            if (!currency) {
                throw new BetError('Currency is missing!');
            }
            if (!amount || typeof amount !== 'string' || amount <= 0) {
                throw new BetError('Please enter a valid amount.');
            }
        } else if (!amount || typeof amount !== 'number' || amount <= 0 || !Number.isInteger(amount)) {
            throw new BetError('Please enter a valid amount.');
        }

        if (round !== this.server.round) {
            throw new BetError('It\'s too late to place a bet on this round (0).');
        }
        this.data = data;
        this.amount = amount;
    }

    preValidate() {
        const betKey2 = `${this.user.id}:${Math.floor(Date.now() / 100)}`; // also prevent bets by the same user made on the exact same tick, which shouldn't happen normally

        if (betKey2 in this.server.betKeys) throw new BetError('Duplicate bet (0)');

        this.server.betKeys[betKey2] = true;

        if (this.amount < this.minBet) {
            const currencyAmountMinBet = (this.minBet / 100).toFixed(2);
            const minBetLocalized = {
                key: 'modals.bet.min_bet_limit',
                params: { currencyAmountMinBet },
            };
            throw new BetError(`You must bet at least ${currencyAmountMinBet} coins. This is a temporary measure to mitigate lag issues due to higher traffic.`)
                .setLocalizedMessage(minBetLocalized.key, minBetLocalized.params);
        }

        if (this.amount > this.maxBet) {
            const currencyAmountMaxBet = (this.maxBet / 100).toFixed(2);
            const maxBetLocalized = {
                key: 'modals.bet.max_bet_limit',
                params: { currencyAmountMaxBet },
            };
            throw new BetError(`The maximum bet is ${currencyAmountMaxBet} coins.`)
                .setLocalizedMessage(maxBetLocalized.key, maxBetLocalized.params);
        }
    }

    async recordBet() {
        try {
            await this.increasePendingBet();
            this.betResponse = await this.deductBet();
            await this.decreasePendingBet();
            this.success = true;
        } catch (error) {
            this.log.debug('error on recordBet', error);

            try {
                await this.decreasePendingBet();
            } catch (error2) {
                await handleException(error2, 'lib/bet.recordBet() - decreasePendingBet', 'Failed to decrease pending bets.');
            }

            if (error instanceof ClientError) {
                this.log.debug('BetError', error);
                throw error;
            }

            this.log.error('critical error', error);

            if (error instanceof BetException) {
                await handleException(error, error.title, error.message, error.data);
            }

            throw new BetError();
        }
    }

    async onPostCommit() { // eslint-disable-line class-methods-use-this
        // placeholder
    }

    async postCommit() {
        if (!this.success) {
            this.log.debug('ignore post commit for failed bet');
            return;
        }

        try {
            await this.user.loadLevels();
            await setImmediatePromise();
            const mybet = this.toJSON();
            this.user.socket.emit('own_bet', mybet);
            this.server.addBet(mybet);
            await setImmediatePromise();
            await this.onPostCommit();
        } catch (error) {
            this.log.error('bet post commit error', error); // no need to report user
        }
    }

    async deductBet() {
        try {
            const result = await internalApiRequest(this.apiRoute, this.data, this.user);
            this.log.debug('Bet placed', result);
            return result;
        } catch (error) {
            if (error instanceof ClientError) {
                throw new BetError(error.message, error.data);
            }

            throw new BetException('Bet.placeBet() Api Error', error, error.trace, this.data);
        }
    }

    async increasePendingBet() {
        await client.incr(`${this.server.name}:pending-bets`);
    }

    async decreasePendingBet() {
        await client.decr(`${this.server.name}:pending-bets`);
    }

    toJSON() {
        return omit({
            ...this.data, // Keys-filtered user-provided data. Should be considered untrusted, which is why it's listed first, so authoritative data can override it.
            ...this.user.toPublicJSON(), // User model
            ...this.betResponse, // Bet model
            id: this.id,
            key: this.key,
            uid: this.user.id,
        }, ['balance_before', 'balance', 'security_token']);
    }
}
