import { RedisAdapter as Adapter } from '@socket.io/redis-adapter';
// adapter is installed via socket.io-redis
// eslint-disable-next-line import/no-extraneous-dependencies
import { Adapter as BaseAdapter } from 'socket.io-adapter';
// parser is installed via socket.io
// eslint-disable-next-line import/no-extraneous-dependencies
import { PacketType } from 'socket.io-parser';

/**
 * Upgrade guide:
 * - Check onmessage logic from the original adapter and override onmessage
 * - Wrap broadcast function with it at then end of onmessage
 * - Make sure correct params are set in our custom createAdapter function
 */
class RedisAdapter extends Adapter {
    /**
     * Called with a subscription message
     * Overrides original onmessage to handle throttler
     * @link https://github.com/socketio/socket.io-redis-adapter/blob/8.3.0/lib/index.ts#L202
     */
    onmessage(pattern, channelBuffer, msg) {
        const channel = channelBuffer.toString();

        const channelMatches = channel.startsWith(this.channel);
        if (!channelMatches) {
            return;
        }

        const room = channel.slice(this.channel.length, -1);
        if (room !== '' && !this.hasRoom(room)) {
            return;
        }

        const args = this.parser.decode(msg);

        const [uid, packet, opts] = args;
        if (this.uid === uid) {
            return;
        }

        if (packet && packet.nsp === undefined) {
            packet.nsp = '/';
        }

        if (!packet || packet.nsp !== this.nsp.name) {
            return;
        }

        opts.rooms = new Set(opts.rooms);
        opts.except = new Set(opts.except);

        /**
         * This is only change compared to original adapter
         * Otherwise we could wrap just broadcast method but unfortunately
         * super.onmessage is calling super.broadcast and not this.broadcast
         */
        if (!this.autoThrottle(packet, opts.rooms)) {
            BaseAdapter.prototype.broadcast.call(this, packet, opts);
        }
    }

    /**
     * Autothrottler is our custom function to throttle messages and send them in batches
     *
     *
     * @param {Object} packet - packet to emit
     * @param {Set} rooms
     * @returns
     */
    autoThrottle = (packet, rooms) => {
        if (!this.server || !packet || packet.type !== PacketType.EVENT || !packet.data || !packet.data[0]) {
            return false;
        }

        const throttler = this.server.throttler(packet.data[0]);

        if (!throttler?.auto) {
            return false;
        }

        throttler.push(packet.data[1], rooms);

        return true;
    }
}

export default function createAdapter(pubClient, subClient, opts) {
    return function createAdapterForNamespace(nsp) {
        return new RedisAdapter(nsp, pubClient, subClient, opts);
    };
}
