import { EventEmitter } from 'events';
import Throttler from './throttler';

export default class ServerBase extends EventEmitter {
    throttlers = {}

    createThrottler(name, cb, opts) {
        if (this.throttlers[name]) {
            throw new Error(`Throttler ${name} already exists`);
        }

        const callback = typeof cb === 'object' ? null : cb;
        const options = typeof cb === 'object' ? cb : opts;

        this.throttlers[name] = new Throttler(this, callback, { ...options, name });
        return this.throttlers[name];
    }

    throttler(name) {
        return this.throttlers[name];
    }

    async stopThrottlers() {
        await Promise.all(Object.values(this.throttlers).map(throttler => throttler.stop()));
    }

    async destroyThrottler(name) {
        const throttler = this.throttlers[name];
        if (throttler) {
            await throttler.stop();
            delete this.throttlers[name];
        }
    }
}
