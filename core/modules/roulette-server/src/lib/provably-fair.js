import crypto from 'crypto';
import { Random, MersenneTwister19937 } from 'random-js';
import { knex } from '../database';
import { internalApiRequest } from '../helpers';

const random = new Random(MersenneTwister19937.autoSeed());

export function getCoinflipRoundResult(round, blockHash) {
    // Compute our winning value
    const hash = crypto.createHash('sha256');
    hash.update(`${round.private_seed}-${blockHash}-${round.id}`);
    const roundHash = hash.digest('hex');

    return (parseInt(roundHash.substr(0, 8), 16) % 2) + 1;
}

export async function getCoinflipPendingRounds(timeout) {
    const [rows] = await knex.raw(`
        SELECT
            round.*,
            chat.hash as chat_room_hash
        FROM coinflip_rounds round
        LEFT JOIN chat_rooms chat
            ON chat.id = round.chat_room_id
        WHERE
            round.result IS NULL AND
            round.seed_index IS NOT NULL AND
            round.challenged_at IS NOT NULL AND
            round.challenged_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL ${Math.round(timeout / 2)} SECOND)
    `);

    return rows;
}

export async function executeCoinflipRoundForUser(user, securityToken, type, roundId) {
    const { round } = await internalApiRequest(`coinflip/${type}/${roundId}`, {
        security_token: securityToken,
    }, user);

    return round;
}

export function getHashForValue(val, key) {
    const c = key ? crypto.createHmac('sha256', key) : crypto.createHash('sha256');
    return c.update(val).digest('hex');
}

export function getRouletteRollResult(hash) {
    // Convert the full hexadecimal hash to a BigInt
    const decimalHash = BigInt(`0x${hash}`);
    return parseInt((decimalHash % 15n).toString(), 10);
}

export function generateRoulettePublicSeed() {
    let publicSeed = '';
    for (let i = 0; i < 5; i++) {
        let rand = random.integer(1, 39).toString();
        if (rand.length === 1) {
            rand = `0${rand}`;
        }
        publicSeed += rand;
    }
    return publicSeed;
}

async function generateRandomBytes() {
    const buffer = await new Promise((resolve, reject) => {
        crypto.randomBytes(16, (err, buf) => {
            if (err) return reject(err);
            return resolve(buf);
        });
    });

    return buffer.toString('hex');
}

export async function generateRoulettePrivateSeed() {
    return getHashForValue(await generateRandomBytes());
}
