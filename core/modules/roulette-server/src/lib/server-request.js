import crypto from 'crypto';
import omitBy from 'lodash/omitBy';
import { socketSub, socketPub } from './redis';
import {
    logger,
    getHookActionName,
    setImmediatePromise,
} from '../helpers';

const EVENT_HELLO_MASTER = 'HelloMaster';
const EVENT_RESPONSE = 'response';

export default class ServerRequest {
    messageIdList = {};

    waitingForResponse = {};

    constructor(server) {
        this.server = server;
        this.internalChannel = `internal-channel:${this.server.name}`;
        this.internalChannelMaster = `${this.internalChannel}:master`;
        this.internalChannelSlave = `${this.internalChannel}:slave`;
        this.internalChannelPrivate = `${this.internalChannel}:${this.uid}`;
    }

    get log() {
        return logger(`${this.server.loggerPrefix}:request`);
    }

    get uid() {
        return this.server.uid;
    }

    async init() {
        socketSub.on('message', this.onInternalMessage.bind(this));
        this.channels = [
            this.internalChannel,
            this.server.isSlave ? this.internalChannelSlave : this.internalChannelMaster,
            this.internalChannelPrivate,
        ];
        this.log.debug('subscribing to channels', ...this.channels);
        await socketSub.subscribe(...this.channels);
    }

    async send(
        eventName,
        data = {},
        toSlave = true,
        toMaster = true,
        waitResponse = true,
        maxRetries = 0,
        expectedData = null,
        expectedReplies = null,
    ) {
        const ts = Date.now();
        const id = crypto.randomBytes(16).toString('hex');

        const execute = () => new Promise((resolve, reject) => {
            let channel = this.internalChannel;
            if (!toSlave && toMaster) {
                channel = this.internalChannelMaster;
            } else if (toSlave && !toMaster) {
                channel = this.internalChannelSlave;
            }

            let shouldWaitResponse = waitResponse;

            if (eventName === EVENT_RESPONSE) {
                channel = `${this.internalChannel}:${data.uid}`;
                shouldWaitResponse = false;
            }

            this.log.debug('send', channel, eventName, data, id, ts, shouldWaitResponse);
            socketPub.publish(channel, JSON.stringify({
                eventName,
                uid: this.uid,
                data,
                id,
                ts,
                waitResponse: shouldWaitResponse,
            }));

            if (!shouldWaitResponse) {
                resolve();
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error(`timeout reached while waiting for response for event ${eventName}`));
                delete this.waitingForResponse[id];
            }, this.server.socket.adapter.requestsTimeout);

            const done = (replies) => {
                clearTimeout(timeout);
                resolve(replies);
            };

            this.waitingForResponse[id] = {
                expectedReplies,
                expectedData,
                resolve: done,
                replies: [],
            };
        });

        const run = async (retries = 0) => {
            try {
                await setImmediatePromise();
                return await execute();
            } catch (error) {
                if (retries < maxRetries) {
                    this.log.debug('retry request', retries, '<', maxRetries);
                    return run(retries + 1);
                }

                if (this.waitingForResponse[id]) {
                    delete this.waitingForResponse[id];
                }

                throw error;
            }
        };

        return run();
    }

    onInternalMessage = async (channel, message) => {
        if (!this.channels.includes(channel)) {
            return;
        }

        this.log.debug('Received internal message', channel, message);
        const {
            eventName,
            data,
            id,
            ts,
            waitResponse,
            uid,
        } = JSON.parse(message);

        const maxAge = (Date.now() - (this.server.socket.adapter.requestsTimeout * 5));
        if (ts && ts < maxAge) {
            return;
        }

        if (id && this.messageIdList[id]) {
            this.log.debug('duplicate id', data.id, data);
            return;
        }

        if (id) {
            this.messageIdList[id] = ts || Date.now();
            this.messageIdList = omitBy(this.messageIdList, t => t < maxAge);
        }

        const action = getHookActionName(eventName);
        const fn = this[action]?.bind(this)
            || this.server[action]?.bind(this.server)
            || this.server.module?.[action]?.bind(this.server.module);
        if (!fn) {
            this.log.debug('action not found', action);
            return;
        }

        const response = await fn(uid, data);

        if (waitResponse) {
            this.log.debug('SENDING response for action', action, response);
            this.send(EVENT_RESPONSE, {
                id,
                response,
                uid,
                isMaster: this.server?.isMaster,
                isStandbyMaster: this.server?.isStandbyMaster,
                isSlave: this.server?.isSlave,
                startedAt: this.server?.startedAt ? this.server.startedAt.getTime() : 0,
                initializedAt: this.server?.initializedAt,
            });
        }
    }

    onResponse(from, data) {
        this.log.debug('Received response', from, data);
        this.log.debug('expectedData', this.waitingForResponse[data.id]?.expectedData?.toString());
        const { id, response } = data;
        if (!this.waitingForResponse[id]) {
            return;
        }

        const { expectedReplies, resolve, expectedData } = this.waitingForResponse[id];

        const reply = {
            ...data,
            uid: from,
        };

        if (expectedData && !expectedData(reply)) {
            this.log.debug('Ignore unexpected response', response, 'for', id);
            return;
        }

        this.waitingForResponse[id].replies.push(reply);

        if (expectedReplies && expectedReplies > 1) {
            this.waitingForResponse[id].expectedReplies -= 1;
            this.log.debug('still waiting for', this.waitingForResponse[id].expectedReplies, 'replies, was', expectedReplies);
            return;
        }

        resolve(this.waitingForResponse[id].replies);
        delete this.waitingForResponse[id];
    }

    async getSlave(eventName, data = {}) {
        const replies = await this.send(eventName, data, true, false, true);
        const master = replies.find(reply => reply && !reply.isMaster && !reply.isStandbyMaster);
        return master ? master.response : null;
    }

    async getMaster(eventName, data = {}) {
        const replies = await this.send(eventName, data, false, true, true, 1, (reply => reply.isMaster));
        this.log.debug('GetMaster: replies', replies);
        const master = replies[0];
        this.log.info('GetMaster: master', master);
        if (!master || !master.uid) {
            return null;
        }
        if (eventName === EVENT_HELLO_MASTER) {
            return master.uid;
        }

        return master.response;
    }

    slave(eventName, data = {}, maxRetries = 0) {
        return this.send(eventName, data, true, false, false, maxRetries);
    }

    master(eventName, data = {}, maxRetries = 0) {
        return this.send(eventName, data, false, true, false, maxRetries);
    }

    sendUser(uid, eventName, data) {
        return this.slave('userMessage', {
            uid,
            eventName,
            data,
        });
    }
}
