import countBy from 'lodash/countBy';
import flatMap from 'lodash/flatMap';
import mapValuesLimit from 'async/mapValuesLimit';
import { socketPub } from './redis';
import ServerModule from './server-module';
import { setImmediatePromise, SOCKET_REDIS_CHANNEL_PREFIX_KEY } from '../helpers';

export default class Presence extends ServerModule {
    logSuffix = 'Presence';

    constructor(server) {
        super(server);
        this.client = socketPub;
        this.timeout = server.socket.server.engine.opts.pingTimeout + server.socket.server.engine.opts.pingInterval;

        this.key = `${SOCKET_REDIS_CHANNEL_PREFIX_KEY}:${this.server.name}-presence`;

        this.log.debug('presence key', this.key);
        this.log.debug('presence timeout', this.timeout);

        this.users = {}; // local users

        this.onlineReal = null;
        this.online = null;
        this.onlineCountUpodatedTs = 0;
    }

    async getAndUpdateOnlineCount() {
        if (!this.server.enableOnlineCounts) {
            this.log.debug('Online count updates are disabled');
            return this.online;
        }

        try {
            const interval = Date.now() - 1500;
            if (this.online && this.onlineCountUpodatedTs > interval) {
                this.log.debug('Use cached online count', this.onlineCountUpodatedTs, interval);
                return this.online;
            }

            this.onlineCountUpodatedTs = Date.now();
            const list = await this.list();

            const count = list.length;
            const mainRoomCounts = countBy(list, 'room');
            const flattenedArray = flatMap(list, item => item?.secondaryRooms || []);
            const secondaryRoomsCount = countBy(flattenedArray);
            const roomCounts = { ...mainRoomCounts, ...secondaryRoomsCount };

            this.log.info('ONLINE COUNTS', count, roomCounts, secondaryRoomsCount);

            this.onlineReal = {
                count,
                roomCounts,
            };

            this.online = this.onlineReal;
            return this.online;
        } catch (error) {
            this.log.error('Failed to update online count', error);
        }

        return this.online;
    }

    /**
     * Remove expired connections from Redis for a given key (user).
     *
     * @param key
     * @param multi
     * @returns {Promise<void>}
     */
    async cleanExpiredUserConnections(key, multi) {
        const now = Date.now();
        const expired = await this.client.hgetall(key);

        await setImmediatePromise();
        await mapValuesLimit(expired, 1, async (when, connectionId) => {
            await setImmediatePromise();

            if (now - when > this.timeout) {
                multi.hdel(key, connectionId);
                // If the connection is expired, we don't need to mark this user as newly offline.
                // They should truly be offline, or they have a new connection, and it doesn't matter anyway.
            }
        });
    }

    /**
     * Mark user as newly offline in Redis so Laravel can avoid toggling users as offline incorrectly
     * if the user temporarily disconnects and reconnects, even if they intentionally closed the connection
     * (ex: refresh page).
     *
     * @param user
     * @param multi
     * @returns {void}
     */
    markUserAsNewlyOffline(user, multi) {
        if (!this.server.recordDisconnectsInRedis || !user.id) {
            return;
        }
        this.log.debug('Marking user as newly offline', user.id);
        const key = `${this.key}:users-offline`;

        // Score by timestamp so we can remove old entries easily.
        multi.zadd(key, Date.now(), user.id);
        // No need to delete when the user comes back online.  Just let it expire.
    }

    /**
     * Called when a user explicitly logs out, and also when an authed user disconnects
     *
     * @param user
     * @returns {Promise<void>}
     */
    async logout(user) {
        try {
            if (!user.id) {
                return;
            }

            const key = `${this.key}:user:${user.id}`;
            const multi = this.client.multi();
            multi.hdel(key, user.socketId);
            this.markUserAsNewlyOffline(user, multi);
            await this.cleanExpiredUserConnections(key, multi);
            await multi.exec();
        } catch (error) {
            this.log.error('Failed to logout presence in redis', error);
        }
    }

    async upsert(user) {
        try {
            const multi = this.client.multi();
            const current = this.users[user.socketId];
            if (current && current.id !== user.id) {
                const oldKey = `${this.key}:user:${current.id || 0}`;
                multi.hdel(oldKey, current.socketId);
                await this.cleanExpiredUserConnections(oldKey, multi);
            }

            this.users[user.socketId] = user;

            const now = Date.now();
            multi.hset(
                this.key,
                user.socketId,
                JSON.stringify({
                    meta: user.public,
                    room: user.room,
                    secondaryRooms: user.secondaryRooms,
                    ip: user.ip,
                    when: now,
                }),
            );
            const key = `${this.key}:user:${user.id || 0}`;
            multi.hset(key, user.socketId, now);
            multi.expire(key, this.timeout * 2);

            await this.cleanExpiredUserConnections(key, multi);

            await multi.exec();
        } catch (error) {
            this.log.error('Failed to store presence in redis', error);
        }
    }

    /**
     * Remove user from presence list.  This is called either when a user disconnects or when a user's connection
     * has timed out.
     *
     * @param connectionId
     * @param multiRedis
     * @returns {Promise<void>}
     */
    async remove(connectionId, multiRedis = null) {
        try {
            const multi = multiRedis || (await this.client.multi());
            const user = this.users[connectionId];
            if (user) {
                delete this.users[connectionId];
            }
            multi.hdel(this.key, connectionId);
            if (user) {
                multi.hdel(`${this.key}:user:${user.id || 0}`, connectionId);
            }

            if (!multiRedis) {
                // If we created the multiRedis object, then we need to execute it.
                // Otherwise, assume the caller will execute it.
                await multi.exec();
            }
        } catch (error) {
            this.log.error('Failed to remove presence in redis', error);
        }
    }

    /**
     * Get a list of all active connections, and prunes expired connections.
     * On the trading server, we handle expired connections in Laravel instead.
     * See UpdateOnlineTraders.php if you're looking for that.
     *
     * This method should only be used on servers with a Master.
     *
     * @returns {Promise<*[]>}
     */
    async list() {
        try {
            const multi = this.client.multi();
            const now = Date.now();
            const active = [];

            await setImmediatePromise();
            const list = await this.client.hgetall(this.key);

            if (!list) {
                return [];
            }

            await setImmediatePromise();
            await mapValuesLimit(list, 1, async (connection, id) => {
                await setImmediatePromise();

                const details = JSON.parse(connection);
                details.id = id;

                if (now - details.when > this.timeout) {
                    await this.remove(id, multi);
                } else {
                    active.push(details);
                }
            });

            await setImmediatePromise();
            await multi.exec();

            return active;
        } catch (error) {
            this.log.error('Failed to get presence in redis', error);
        }

        return [];
    }
}
