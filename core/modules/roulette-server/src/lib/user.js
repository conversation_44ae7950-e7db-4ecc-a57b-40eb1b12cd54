import crypto from 'crypto';
import unionBy from 'lodash/unionBy';
import User from '../database/User';
import { ForbiddenError } from '../errors/client';
import {
    logger,
    getIp,
    getSubjectForAuthToken,
    getAuthTokenFromJWt,
    toBoolean,
    IS_PRODUCTION,
} from '../helpers';
import { socketPub } from './redis';

export default class SocketUser {
    server;

    isAdded = false

    user = null;

    authenticated = false;

    identifying = false;

    ip = null;

    httpCfIpcountry = null;

    hostname = null;

    authorizedListeners = [];

    normalListeners = [];

    balanceUpdateTimeout = null;

    /**
     * The global room the user is in. Integers as strings indicate global rooms.
     *
     *  @type {string|null}
     */
    room = '0';

    /**
     * The secondary (temporary) rooms the user is in.
     *
     * @type {string[]}
     */
    secondaryRooms = [];

    connectionId = null;

    uuid = null;

    allowApiTokenAuth = false;

    initialized = false;

    constructor(socket, server) {
        this.socket = socket;
        this.server = server;
        this.presence = server.presence;
        this.connectionId = socket.id;

        this.log = logger(`${server.loggerPrefix}:main-lib:user`);

        this.ip = getIp(socket);

        if ('cf-ipcountry' in socket.request.headers) {
            this.httpCfIpcountry = socket.request.headers['cf-ipcountry'];
        }

        this.hostname = socket.request.headers.host;

        // this.user = User.guest();
        this.user = new User();
        this.user.roles = [];
        this.log.debug(`Socket connected with IP: ${this.ip}`);
    }

    get socketId() {
        return this.socket?.id || this.connectionId;
    }

    get userId() {
        return this.authenticated ? this.user.id : null;
    }

    get id() {
        return this.userId;
    }

    get uid() {
        return this.userId;
    }

    get profile() {
        return this.user;
    }

    get public() {
        return this.isAuthenticated ? this.user.toPublicJSON() : { id: 'guest' };
    }

    get isAuthenticated() {
        return !!(this.authenticated && this.userId);
    }

    checkServerStatus(listener = 'unknown') {
        if (this.server && this.server.isReadyToAcceptConnections && !this.server.isShuttingDown) {
            return true;
        }

        let reason = 'unknown';
        if (!this.server) {
            reason = 'server-not-found';
        } else if (this.server.isShuttingDown) {
            reason = 'server-shutting-down';
        } else if (!this.server.isReadyToAcceptConnections) {
            reason = 'server-not-ready';
        }

        this.socket.emit('force_reconnect', { reason, event: listener });
        return false;
    }

    listenerWrapper(listener, fn, allowGuest = true) {
        return async (...args) => {
            if (!this.checkServerStatus(listener)) {
                return;
            }

            if (!allowGuest && !this.checkIsAuthenticated()) {
                return;
            }

            try {
                await fn(...args);
            } catch (error) {
                if (this.checkServerStatus(listener)) {
                    this.log.error('Listener', listener, 'throw error', error);
                    this.socket.emitError();
                }
            }
        };
    }

    attachAuthorizedListener(listener, cb) {
        const fn = this.listenerWrapper(listener, cb, false);
        this.authorizedListeners.push([listener, fn]);
        this.socket.on(listener, fn);
    }

    attachListener(listener, cb) {
        const fn = this.listenerWrapper(listener, cb, true);
        this.normalListeners.push([listener, fn]);
        this.socket.on(listener, fn);
    }

    removeAuthorizedListeners() {
        this.log.debug('removeAuthorizedListeners');
        this.authorizedListeners.forEach(([listener, fn]) => this.socket.removeListener(listener, fn));
    }

    attachAuthorizedListeners() { // eslint-disable-line class-methods-use-this
        // placeholder
    }

    attachListeners() {
        this.socket.conn.on('heartbeat', this.refresh);
        this.attachListener('disconnect', this.onDisconnect);
        this.attachListener('logout', this.onLogout);
        this.attachListener('identify', this.onIdentify);
    }

    removeListeners() {
        this.socket.conn.removeListener('heartbeat', this.refresh);
        this.socket.removeAllListeners();
    }

    onLogout = async () => {
        try {
            await this.presence.logout(this);
            await this.clear();
            this.user = User.guest();
            this.authenticated = false;
            this.removeAuthorizedListeners();

            this.socket.emit('authorized', { authorized: false });
        } catch (error) {
            this.log.error('Failed to handle logout', error);
        }
    }

    onIdentify = async (data) => {
        if (!data) {
            this.log.debug('Forbidden: missing_data');
            this.socket.emitError(new ForbiddenError());
            return;
        }

        if (this.identifying) {
            return;
        }

        this.identifying = true;

        try {
            const {
                uid,
                authorizationToken,
                model,
                signature,
                uuid,
            } = data;

            if (
                !authorizationToken
                || !uid

                || typeof authorizationToken !== 'string'
                || (uid !== 'guest' && typeof uid !== 'number')
                || (uid !== 'guest' && model.id !== uid)
            ) {
                this.log.debug('Forbidden: invalid_identify_data');
                this.socket.emitError(new ForbiddenError());
                return;
            }

            if (!IS_PRODUCTION && uid === 'guest' && authorizationToken === process.env.LOAD_TEST_SECURITY_TOKEN) {
                // auth artillery guests directly
                await this.authorize();
                return;
            }

            const jwtDisabled = toBoolean(process.env.JWT_SOCKET_TOKEN_DISABLED);
            this.log.debug('require jwt token=', !jwtDisabled);

            let token;
            let subject;
            try {
                ({ token, subject } = getAuthTokenFromJWt(authorizationToken, uid, this.socket));
            } catch (error) {
                if (jwtDisabled) {
                    // if jwt token disabled, we can try to use it as plain token
                    token = authorizationToken;
                    subject = getSubjectForAuthToken(uid, this.socket);
                } else {
                    this.log.debug('Forbidden: invalid_jwt_token', 'jwtDisabled=', jwtDisabled, error);
                    this.socket.emitError(new ForbiddenError());
                    return;
                }
            }

            if (!token
                || typeof token !== 'string'
                || !token.match(/^[A-Za-z0-9+/]{44}$/)
            ) {
                this.log.debug('Forbidden: invalid_token');
                this.socket.emitError(new ForbiddenError());
                return;
            }

            if (uid === 'guest') {
                const redisKey = `signature-token:${subject}:${token}`;
                const signatureToken = await socketPub.get(redisKey);

                if (!signatureToken) {
                    this.log.debug('Forbidden: invalid_guest_signature_token');
                    this.socket.emitError(new ForbiddenError());
                    return;
                }

                await this.authorize();
                return;
            }

            if (
                !model
                || !signature

                || typeof model !== 'object'
                || typeof signature !== 'string'

                || !signature.match(/^[A-Za-z0-9]{128}$/)
            ) {
                this.log.debug('Forbidden: invalid_signature_data');
                this.socket.emitError(new ForbiddenError());
                return;
            }

            const redisKey = `signature-token:${uid}:${token}`;
            const modelstr = JSON.stringify(model);
            const signatureToken = await socketPub.get(redisKey);

            if (!signatureToken) {
                this.log.debug('Forbidden: signature_token_not_found');
                this.socket.emitError(new ForbiddenError());
                return;
            }

            const ourSignature = crypto.createHmac('sha512', signatureToken).update(modelstr, 'utf8').digest('hex');

            if (ourSignature !== signature) {
                this.log.debug('Forbidden: signature_mismatch', 'ourSignature=', ourSignature, 'signature=', signature, 'token=', token, 'signatureToken=', signatureToken);
                this.socket.emitError(new ForbiddenError('signature_mismatch'));
                return;
            }

            const user = User.fromJson(model);

            if (!user) {
                this.log.debug('Forbidden: user_not_found');
                this.socket.emitError(new ForbiddenError());
                return;
            }

            if (user.api_token && !this.allowApiTokenAuth) {
                this.log.debug('Forbidden: api_token_not_allowed');
                this.socket.emitError(new ForbiddenError());
                return;
            }

            this.uuid = uuid;
            await this.authorize(user);
        } catch (error) {
            if (this.checkServerStatus('onIdentify')) {
                this.log.error('Failed to identify', error, data?.uid);
                this.socket.emitError('Identify failed. Please refresh.');
            }
        } finally {
            this.identifying = false;
        }
    }

    async authorize(user = null) {
        this.removeListeners();
        this.attachListeners();
        this.removeAuthorizedListeners();
        if (!user) {
            this.log.debug('User identified as guest from ip', this.ip);
            this.user = User.guest();
            this.authenticated = false;
        } else {
            this.log.debug('user authorized', user.id, user.steam_name, 'from ip', this.ip);

            this.user = user;
            this.user.socket = this.socket;
            this.authenticated = true;
        }

        this.userProxy = new Proxy(this, {
            get(userInstance, field) {
                if (field in userInstance) return userInstance[field]; // normal case
                return userInstance.user[field];
            },
        });

        if (this.authenticated) {
            this.attachAuthorizedListeners();
        }

        if (this.identifying) {
            this.isAdded = true;
            await this.refresh();
            await this.onAfterIdentify();
        }

        this.initialized = true;
        this.socket.emit('init', this.toJSON());

        if (!this.isAdded) {
            return;
        }

        clearTimeout(this.socket.initTimeout);
        if (this.socket.queue.length) {
            const { queue } = this.socket;
            this.socket.queue = [];
            queue.forEach(([, next]) => {
                next();
            });
        }
    }

    async onAfterIdentify() {
        if (this.server.enableOnlineCounts) {
            this.socket.emit('online', await this.server.presence.getAndUpdateOnlineCount());
        }
    }

    refresh = async (isAdded = this.isAdded) => {
        if (!isAdded) {
            return;
        }

        try {
            this.log.debug('refresh socket presence', this.socketId, this.userId);

            await this.presence.upsert(this);
        } catch (error) {
            this.log.error('Failed to handle presence', error);
        }
    }

    async clear() { // eslint-disable-line class-methods-use-this
        clearTimeout(this.balanceUpdateTimeout);
    }

    onDisconnect = async () => {
        const { socketId } = this;
        this.log.debug('socket disconnected', socketId);

        if (!this.isAdded) {
            return;
        }

        if (this.profile.isGuest) {
            this.log.debug(`Guest disconnected with IP: ${this.ip}`);
        } else {
            this.log.debug(`User #${this.id} "${this.profile.steam_name}" disconnected with IP ${this.ip}`);
            await this.onLogout();
        }

        try {
            this.removeListeners();
            await this.clear();
            await this.presence.remove(socketId);
            this.isAdded = false;
            this.initialized = false;
        } catch (error) {
            this.log.error('Failed to handle user left', error);
        }

        delete this.presence; // cleanup memory
        delete this.socket; // cleanup memory

        // make simple fake proxy to avoid some edge case errors;
        const fakeProxy = () => new Proxy({}, { get() { return () => null; }, set() { } });

        this.socket = fakeProxy();
        this.presence = fakeProxy();
        this.normalListeners = [];
        this.authorizedListeners = [];
        this.server.removeUser(socketId);
    }

    onBalanceUpdate({ balance, balances, delay }) {
        const execute = () => {
            if (!this.user) {
                return;
            }

            this.user.balance = balance;

            this.user.balances = unionBy(balances || [], this.user.balances || [], 'balance_type');
            this.socket.emit('balance', { balance, balances: this.user.balances });
        };

        if (!delay || delay <= 0) {
            execute();
            return;
        }
        clearTimeout(this.balanceUpdateTimeout);
        this.balanceUpdateTimeout = setTimeout(execute, delay * 1000);
    }

    checkIsAuthenticated() {
        if (!this.isAuthenticated) {
            this.socket.emitError('You don\'t seem to be logged in. Please refresh the page and try again.');
            return false;
        }

        this.user.ip = this.ip;
        if (this.httpCfIpcountry) {
            this.user.country = this.httpCfIpcountry;
        }

        this.user.hostname = this.hostname;

        return true;
    }

    async onRemoteLogout(uuid = null) {
        this.log.debug('onRemoteLogout', this.id);
        this.socket.emit('exterminate', uuid);
        if (!uuid || uuid === this.uuid) {
            await this.onLogout();
        }
    }

    toJSON() {
        return {
            authenticated: this.isAuthenticated,
            serverTime: (new Date()).toISOString(),
            server: this.server.loggerPrefix,
            serverHost: process.env.HOSTNAME || 'unknown',
            identifying: this.identifying,
            ...this.user.toJSON(),
            ...this.server.getCurrentState(this),
        };
    }
}
