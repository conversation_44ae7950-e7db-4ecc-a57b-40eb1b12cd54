import Redis from 'ioredis';
import Redlock from 'redlock';
import RedisMock from 'ioredis-mock';
import { logger } from '../helpers';

const log = logger('lib:redis');

export const redisConfig = {
    host: process.env.REDIS_HOST || 'csgoempire-redis',
    password: process.env.REDIS_PASSWORD || undefined,
    port: process.env.REDIS_PORT || '6379',
    showFriendlyErrorStack: true,
};

export const createClient = (customConfig = {}, name) => {
    log.info('Create socket client', name);

    const redis = new (process.env.JEST_WORKER_ID ? RedisMock : Redis)({ ...redisConfig, ...customConfig });
    redis.on('connect', () => {
        log.info(name, 'connected');
    });
    redis.on('ready', () => {
        log.info(name, 'ready');
    });
    redis.on('close', () => {
        log.warn(name, 'close');
    });
    redis.on('reconnecting', (ms) => {
        log.warn(name, 'reconnecting in', ms, 'ms');
    });
    redis.on('end', () => {
        log.info(name, 'end');
    });
    redis.on('error', (err) => {
        log.error(name, err);
    });
    // Used to make sure we only subscribe to global events once
    // (i.e. non-user-ID-specific events)
    redis.global_subscriptions = [];
    redis.globallySubscribe = function globallySubscribe(channel) {
        if (redis.global_subscriptions.includes(channel)) {
            return;
        }
        redis.subscribe(channel);
        redis.global_subscriptions.push(channel);
    };
    return redis;
};

const client = createClient({}, 'default');
export const subscriber = createClient({}, 'default_sub');

export default client;

const socketConfig = {
    host: process.env.REDIS_HOST_SOCKET || redisConfig.host,
    password: process.env.REDIS_PASSWORD_SOCKET || redisConfig.password,
    port: process.env.REDIS_PORT_SOCKET || redisConfig.port,
};

const socketConfigReadOnly = {
    host: process.env.REDIS_HOST_SOCKET_READONLY || process.env.REDIS_HOST_SOCKET || redisConfig.host,
    password: process.env.REDIS_PASSWORD_SOCKET_READONLY || process.env.REDIS_PASSWORD_SOCKET || redisConfig.password,
    port: process.env.REDIS_PASSWORD_SOCKET_READONLY || process.env.REDIS_PORT_SOCKET || redisConfig.port,
};

export const socketSub = createClient(socketConfigReadOnly, 'socket_sub');
export const socketPub = createClient(socketConfig, 'socket_pub');

export const getRedlock = (options = {}) => {
    const defaults = {
        // the expected clock drift; for more details
        // see http://redis.io/topics/distlock
        driftFactor: 0.05, // time in ms

        // the max number of times Redlock will attempt
        // to lock a resource before erroring
        retryCount: 10,

        // the time in ms between attempts
        retryDelay: 100, // time in ms

        // the max time in ms randomly added to retries
        // to improve performance under high contention
        // see https://www.awsarchitectureblog.com/2015/03/backoff.html
        retryJitter: 250, // time in ms
    };

    const opts = options || {};
    if (opts && opts.timeout) {
        opts.retryCount = opts.timeout / (opts.retryDelay || defaults.retryDelay);
    }

    const redlockClient = new Redlock([socketPub], {
        ...defaults,
        ...opts,
    });
    redlockClient.on('clientError', (err) => {
        log.error(`A redlock ${options.name || 'unknown'} redis error has occurred:`, err);
    });

    return redlockClient;
};

export const redlock = getRedlock({ name: 'default' });
