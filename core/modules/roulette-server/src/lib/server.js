import maxBy from 'lodash/maxBy';
import cloneDeep from 'lodash/cloneDeep';
import isNaN from 'lodash/isNaN';
import mean from 'lodash/mean';
import isEqual from 'lodash/isEqual';
import {
    logger,
    sleep,
    getHookActionName,
    toBoolean,
} from '../helpers';
import { redlock } from './redis';
import Presence from './presence';
import ServerBase from './server-base';
import ServerModule from './server-module';
import ServerRequest from './server-request';

const EVENT_HELLO_MASTER = 'HelloMaster';
const EVENT_HELLO_FROM_MASTER = 'HelloFromMaster';
export const EVENT_START_ELECTIONS = 'StartElections';
export const EVENT_END_ELECTIONS = 'EndElections';
const EVENT_PROMOTED_MASTER = 'PromotedMaster';

export const standbyMasterEvents = [EVENT_START_ELECTIONS, EVENT_END_ELECTIONS, EVENT_PROMOTED_MASTER, EVENT_HELLO_FROM_MASTER];

const MAX_EXIT_TIMEOUT = parseInt(process.env.MAX_EXIT_TIMEOUT, 10) || 120000;
const MAX_EXIT_ELECTIONS_TIMEOUT = parseInt(process.env.MAX_EXIT_ELECTIONS_TIMEOUT, 10) || 15000;

export const SCALING_HORIZONTAL = 'HORIZONTAL';
export const SCALING_SINGLE = 'DISABLED';
export const SCALING_HORIZONTAL_WITH_MASTER = 'HORIZONTAL_WITH_MASTER';
export const MASTER_PODS_COUNT = parseInt(process.env.MASTER_PODS_COUNT, 10) || 2;

export const MASTER_ENABLED = process.env.JEST_WORKER_ID ? true : toBoolean(process.env.MASTER_ENABLED);
export const SLAVE_ENABLED = process.env.JEST_WORKER_ID ? false : toBoolean(process.env.SLAVE_ENABLED);

const CAN_BE_MASTER = MASTER_ENABLED || (!MASTER_ENABLED && !SLAVE_ENABLED);
export const CAN_BE_SLAVE = SLAVE_ENABLED || (!MASTER_ENABLED && !SLAVE_ENABLED);

if (CAN_BE_SLAVE && CAN_BE_MASTER) {
    throw new Error('Misconfiguration detected. MASTER_ENABLED and SLAVE_ENABLED, both cannot be true!');
}

export default class MasterSlaveServer extends ServerBase {
  WorkerModuleClass = ServerModule

  MasterModuleClass = ServerModule

  presence = null;

  isMaster = !!process.env.JEST_WORKER_ID;

  masterHealtcheckTimer = null;

  users = {} // users in this server

  currentMaster = null;

  startedAt = null;

  onlineCountUpdateEmitterIntervalTimeout = null;

  enableOnlineCounts = false;

  recordDisconnectsInRedis = false;

  shouldGracefullyDisconnectSockets = false;

  isReadyToAcceptConnections = false;

  readyToAcceptConnectionsPromise = null;

  isClosed = false;

  isShuttingDown = false;

  isInitialized = false;

  errorRoundCounter = 0;

  roundIdleTimeout = null;

  roundMaxIdleTimeout = 120000;

  memoryHistory = [];

  constructor(nsp) {
      super();

      this.socket = nsp.socket;
      this.scaling = nsp.scaling;

      this.log.info('uid', this.uid);
      this.log.info('scaling', this.scaling);
      this.log.info('CAN_BE_SLAVE', CAN_BE_SLAVE);
      this.log.info('CAN_BE_MASTER', CAN_BE_MASTER);
      this.log.info('MASTER_PODS_COUNT', MASTER_PODS_COUNT);
      this.log.info('this.socket.server.engine.pingTimeout', this.socket.server.engine.opts.pingTimeout);
      this.log.info('this.socket.server.engine.pingInterval', this.socket.server.engine.opts.pingInterval);

      this.socket.adapter.server = this;

      this.once('shutdown', this.onShutdown);

      this.presence = new Presence(this);

      this.initializedAt = Date.now();
  }

  onUserMessage(from, data) {
      const userConnections = this.getConnectedUserConnections(data.uid);
      userConnections.forEach((user) => {
          const action = getHookActionName(data.eventName);
          if (user[action]) {
              user[action](data.data);
          }
      });
  }

  get name() {
      return this.socket.name;
  }

  get loggerPrefix() {
      return `${this.name.replace('/', '')}:${this.type}-server:${this.uid}`;
  }

  get log() {
      return logger(this.loggerPrefix);
  }

  get type() {
      if (this.isMaster) {
          return 'master';
      }

      return this.isStandbyMaster ? 'standby-master' : 'slave';
  }

  get isSlave() {
      return !this.isMaster && !this.isStandbyMaster;
  }

  get isStandbyMaster() {
      return !this.isMaster && !CAN_BE_SLAVE;
  }

  get startSlaveWorker() { // eslint-disable-line class-methods-use-this
      return CAN_BE_SLAVE;
  }

  get uid() {
      return this.socket.adapter.uid;
  }

  get slaveModule() {
      return this.isSlave && this.module ? this.module : null;
  }

  get masterModule() {
      return this.isMaster && this.module ? this.module : null;
  }

  onShutdown = async (signal, processExitCode) => {
      this.isShuttingDown = true;
      this.exit = () => null;

      this.log.warn('Shutdown event received with signal', signal, 'and exit code', processExitCode);

      let exitCode = Number(processExitCode || 0);
      try {
          clearTimeout(this.roundIdleTimeout);
          if (!this.isMaster) {
              clearTimeout(this.masterHealtcheckTimer);
          }

          clearTimeout(this.onlineCountUpdateEmitterIntervalTimeout);

          if (this.startedAt) {
              this.log.info('cleanup server...');
              await this.cleanup();
          }

          await this.gracefullyDisconnectSockets();
          await this.afterCleanup();

          this.log.debug('cleanup done...');

          if (this.isMaster && this.scaling !== SCALING_HORIZONTAL) {
              this.log.info('I\'m master, start elections before shutting done...');
              const startedAt = Date.now();
              const electNewMaster = async (plannedFailover) => {
                  const success = await this.startElections(true, [this.uid], plannedFailover);
                  if (!success) {
                      if (Date.now() - startedAt < MAX_EXIT_ELECTIONS_TIMEOUT) {
                          await sleep(500);
                          await electNewMaster(false);
                      } else {
                          throw new Error(`We failed to elect new master in ${MAX_EXIT_ELECTIONS_TIMEOUT / 1000} seconds`);
                      }
                  }
              };
              await electNewMaster(true);
              this.log.debug('Elections done. I\'m ready to exiting...');
          }
      } catch (error) {
          this.log.error('Failed to handle shutdown, stepping down', error);
          // stepping down to prevent master lock and make room for new master to be selected as fast as possible
          await this.stepDown();
          exitCode = 99;
      }

      this.isClosed = true;

      clearTimeout(this.masterHealtcheckTimer);
      this.emit('closed', exitCode);
  }

  cleanup() {
      return new Promise((resolve, reject) => {
          let cleanupTimeout;
          this.exit = (error) => {
              this.exit = () => null;
              clearTimeout(cleanupTimeout);

              if (error) {
                  reject(error);
                  return;
              }

              resolve();
          };

          cleanupTimeout = setTimeout(() => {
              this.log.error('Exit timeout.');
              this.exit(new Error('Cleanup timeout'));
          }, MAX_EXIT_TIMEOUT); // max wait timeout
      });
  }

  async afterCleanup() {
      await this.stopThrottlers();
  }

  async start() {
      this.internalRequest = new ServerRequest(this);
      await this.internalRequest.init();

      if (this.scaling !== SCALING_HORIZONTAL) {
          await this.checkMaster();
      }

      if (this.isSlave && this.scaling === SCALING_SINGLE) {
          this.log.info('I am slave, wait to promoting master...');
          await sleep(2000);
          await this.start();
      }

      await this.presence.getAndUpdateOnlineCount();
      this.startedAt = new Date();
  }

  readyToAcceptConnections() {
      if (!this.isReadyToAcceptConnections) {
          this.log.info('set isReadyToAcceptConnections=true');
          this.isReadyToAcceptConnections = true;
      }
      if (this.readyToAcceptConnectionsPromise) {
          this.readyToAcceptConnectionsPromise();
      }
  }

  ready() {
      this.log.debug('starting...');
      this.isInitialized = false;
      return new Promise((resolve, reject) => {
          const timeoutms = 30000;
          const timeout = setTimeout(() => {
              this.readyToAcceptConnectionsPromise = null;
              const msg = `Server not resolved connection state in ${60000}ms`;
              this.log.error(msg);
              reject(msg);
          }, timeoutms);

          this.readyToAcceptConnectionsPromise = () => {
              clearTimeout(timeout);
              this.log.debug('Ready to accept connections');
              this.readyToAcceptConnectionsPromise = () => null;
              resolve();
          };
          this.run();
      });
  }

  async createWorker(cb) {
      if (this.isStandbyMaster) {
          return null;
      }

      return cb();
  }

  async stop() {
      if (this.module) {
          await this.module.stop();
      }

      this.module = null;
  }

  async run() {
      const fn = async (err = null) => {
          const event = err ? 'error' : 'done';
          clearTimeout(this.roundIdleTimeout);
          this.emit(event, err);

          let error = null;
          if (this.isShuttingDown || event === 'error') {
              if (event === 'error') {
                  this.errorRoundCounter += 1;
                  this.log.info('Error event from round.', err);
              }

              try {
                  this.isReadyToAcceptConnections = false;
                  await this.stop();
              } catch (stopError) {
                  this.log.error('Failed to handle stop', error);
                  error = stopError;
              }
          } else {
              this.errorRoundCounter = 0;
          }

          if (this.isShuttingDown) {
              this.exit(error);
              return;
          }

          if (this.isStandbyMaster) {
              await sleep(5000);
          }

          const maxRoundErrorCount = 5;
          if (this.errorRoundCounter >= maxRoundErrorCount) {
              this.log.error('Too many failed rounds in row. Shutdown server', this.errorRoundCounter, '>=', maxRoundErrorCount);
              process.exitCode = 102;
              process.kill(process.pid, 'SIGTERM');
              return;
          }

          const used = process.memoryUsage().heapUsed / 1024 / 1024;
          const usedMb = Math.round(used * 100) / 100;

          const meanMemory = mean(this.memoryHistory);
          const meanMemoryMb = Math.round(meanMemory * 100) / 100;
          const meanCount = this.memoryHistory.length;

          this.memoryHistory.push(used);
          if (!isNaN(meanMemory) && meanCount >= 10) {
              const runningUp = used > meanMemory;
              this.log.debug('Mean memory usage', meanMemoryMb, 'MB running up=', runningUp);
              if (runningUp) {
                  this.log.debug('Memory usage is running up, used', usedMb, 'MB', '>', meanMemoryMb, 'MB from last', meanCount, 'rounds', meanMemoryMb, 'MB');
              }
              this.memoryHistory.pop();
          }
          this.log.debug('Approximately memory usage=', usedMb, 'MB', 'meanMemory=', meanMemoryMb, 'MB', 'rounds for mean', meanCount);

          this.log.info('Run this.run()');
          this.run();
      };

      this.log.debug('Run this.next()');

      clearTimeout(this.roundIdleTimeout);
      this.roundIdleTimeout = setTimeout(() => {
          this.log.error('Round timeout', this.roundMaxIdleTimeout, 'ms reached. Force kill server.');
          process.exitCode = 103;
          process.kill(process.pid, 'SIGTERM');
      }, this.roundMaxIdleTimeout);

      try {
          const moduleClass = this.isMaster ? 'MasterModuleClass' : 'WorkerModuleClass';

          if (!this.module && !this.isStandbyMaster) {
              this.module = await this[moduleClass].create(this);
              this.log.info('Server module created', moduleClass, this.module.constructor.name);

              if (!(this.module instanceof ServerModule)) {
                  throw new Error(`${moduleClass} class must be an instance of ServerModule`);
              }
          }

          await (!this.next || this.isStandbyMaster ? this.simpleNext() : this.next());
          fn();
      } catch (error) {
          fn(error);
      }
  }

  // default next, overide in sub classes
  async simpleNext() {
      this.log.debug('running this.next() isMaster=', this.isMaster);
      this.readyToAcceptConnections();
      await sleep(5000);
      this.log.debug('running this.next() done');
  }

  onHelloMaster(from) {
      if (this.isMaster) {
          this.log.info('Hello request from slave', from);
      }

      return true;
  }

  onHelloFromMaster(from) {
      if (!this.isMaster) {
          this.log.info('Hello from master', from);
          this.resetMasterCheckTimeout();
      }

      return true;
  }

  onStartElections(from) {
      this.log.info('Elections started by', from);
      const isValidCandidate = CAN_BE_MASTER && !this.isShuttingDown;
      this.log.info('Am I a validCandidate: ', isValidCandidate);
      return { accept: isValidCandidate };
  }

  async onEndElections(from, data) {
      this.log.info('New master elected', data.newMaster);
      if (data.newMaster === this.uid && !this.isMaster && !this.isShuttingDown && CAN_BE_MASTER) {
          this.log.info('It is me! Promote to master.');
          try {
              await this.promoteToMaster();
          } catch (error) {
              this.log.error('Failed to promoteToMaster', error);
              return { promotedToMaster: false };
          }
          this.log.info('Promoted to master successfully');
          return { promotedToMaster: true };
      }
      return { promotedToMaster: false };
  }

  async onPromotedMaster(from) {
      this.log.info('New master:', from);

      if (this.isMaster && from !== this.uid) {
          try {
              await this.stepDown();
          } catch (error) {
              this.log.error('Failed to step down', error);
              return false;
          }
      }

      return true;
  }

  checkMaster = async () => {
      if (this.isMaster) {
          if (this.masterLock) {
              try {
                  await this.masterLock.extend(this.socket.server.engine.opts.pingTimeout * 3);
                  this.log.debug('Master lock extended.');
              } catch (error) {
                  this.log.error('Failed to extend masterLock', error);
                  this.masterLock = null;
                  try {
                      await this.promoteToMaster();
                  } catch (error2) {
                      this.log.error('Failed to promote back to master', error2);
                      await this.stepDown();
                  }
              }

              try {
                  this.log.info('send hello event to slaves');
                  await this.internalRequest.slave(EVENT_HELLO_FROM_MASTER);
              } catch (error) {
                  this.log.error('Failed while sending ping to slaves', error);
              }
          } else {
              this.log.warn('I\'m master without master lock. Maybe I just step down.');
              await this.stepDown();
          }
      } else {
          try {
              this.log.info('Check if we have active master');
              const uid = await this.getMasterUid();

              if (!uid) {
                  throw new Error('Master not found in replies');
              }

              this.currentMaster = uid;
              this.log.info('Master found', uid);
          } catch (error) {
              this.log.error('No reply from master', error.message);
              await sleep(500);
              await this.startElections();
          }
      }

      this.resetMasterCheckTimeout();
  }

  resetMasterCheckTimeout() {
      let factor = 3;
      if (this.isStandbyMaster) {
          factor = 1.5;
      } else if (this.isMaster) {
          factor = 1;
      }

      const timeout = this.socket.server.engine.opts.pingTimeout * factor;
      clearTimeout(this.masterHealtcheckTimer);
      this.masterHealtcheckTimer = setTimeout(this.checkMaster, timeout);
  }

  async getMasterUid() {
      try {
          const uid = await this.internalRequest.getMaster(EVENT_HELLO_MASTER);
          return uid;
      } catch (error) {
          this.log.debug('getMasterUid() failed', error);
      }

      return null;
  }

  onEmitToIp(from, { ip, event, data }) {
      const connections = this.getConnectionsByIp(ip);
      this.log.debug('emit to ip', ip, 'connection', connections.length);
      connections.forEach(user => user.socket.emit(event, data));
  }

  async hasMaster() {
      try {
          const uid = await this.getMasterUid();
          return !!uid;
      } catch (error) {
          this.log.debug('hasMaster() failed', error);
      }

      return false;
  }

  async startElections(force, filter = [], plannedFailover = false) {
      if (this.isMaster && !force) {
          this.log.debug('do not start elections', 'this.isMaster=', this.isMaster);
          return false;
      }

      if (!CAN_BE_MASTER) {
          this.log.info('We do not have a master, but cannot be a master. Only master candidates can start elections.');
          return true;
      }

      this.log.info('We do not have a master, time to start elections', 'filter out=', filter);

      let lock;
      try {
          if (!this.isMaster && !force && (await this.hasMaster())) {
              this.log.debug('Skip elections, we already have a master');
              return true;
          }

          const timeout = this.socket.adapter.requestsTimeout * 2;
          lock = await redlock.lock(`master-elections:${this.name}`, timeout);

          // On planned redeploy we want replies from all pods to choose the latest version, unplanned election we only need the first
          const expectedCandidates = Math.max(plannedFailover ? MASTER_PODS_COUNT - filter.length : 1, 1);
          this.log.info('Number of candidates expected: ', expectedCandidates);

          const candidates = await this.internalRequest.send(
              EVENT_START_ELECTIONS,
              undefined,
              false,
              true,
              undefined,
              undefined,
              (resp => resp.isSlave === false && resp.response.accept === true),
              expectedCandidates,
          );
          this.log.info('Candidates:', candidates);

          if (!candidates.length) {
              throw new Error('Master candidates not found');
          }

          await lock.extend(timeout);

          if (this.isMaster) {
              await this.stepDown();
              await lock.extend(timeout);
          }

          const newMaster = maxBy(candidates, 'initializedAt');
          this.log.info(`Master elected: ${newMaster.uid} by initializedAt timestamp ${newMaster.initializedAt}`);

          let proposalResponse = await this.internalRequest.send(
              EVENT_END_ELECTIONS,
              { newMaster: newMaster.uid },
              undefined,
              undefined,
              undefined,
              undefined,
              (resp => resp.uid === newMaster.uid),
              1,
          );
          proposalResponse = proposalResponse.filter(s => s.uid === newMaster.uid);
          this.log.info('Elected master proposal response', proposalResponse);

          if (!proposalResponse.length || proposalResponse[0].response.promotedToMaster !== true) {
              throw new Error('New master did not accept new role, start new elections');
          }
          this.log.info('New master accepted role');
          return true;
      } catch (error) {
          this.log.info('Failed to select master', error.message);
      } finally {
          this.log.info('Elections finished, releasing elections lock if exists');
          if (lock) await lock.unlock();
      }
      return false;
  }

  async promoteToMaster() {
      if (!CAN_BE_MASTER) {
          throw new Error('Try to promote master, but I cannot be master!');
      }

      this.log.info('Promote to master');
      this.masterLock = await redlock.lock(`master:${this.name}`, this.socket.server.engine.opts.requestsTimeout * 2);
      this.log.info('Master lock aquired');
      await this.internalRequest.send(EVENT_PROMOTED_MASTER);
      await this.masterLock.extend(this.socket.server.engine.opts.pingTimeout * 3);
      this.resetMasterCheckTimeout();
      this.isMaster = true;

      if (this.enableOnlineCounts) {
          const onlineEmitter = async () => {
              const online = await this.presence.getAndUpdateOnlineCount();
              this.onlineCountUpdateEmitterIntervalTimeout = setTimeout(onlineEmitter, 1500);

              if (this.presence.previousOnlineCount && isEqual(online, this.presence.previousOnlineCount)) {
                  this.log.debug('Ignore update emitting since no changes');
                  return;
              }

              this.log.debug('emitting online counts');
              this.presence.previousOnlineCount = cloneDeep(online);
              this.socket.emit('online', online);
          };
          onlineEmitter();
      }
  }

  async stepDown() {
      await this.stop();
      if (this.masterLock) {
          await this.masterLock.unlock();
          this.masterLock = null;
      }
      this.isMaster = false;
      clearTimeout(this.onlineCountUpdateEmitterIntervalTimeout);
  }

  addUser(user) {
      this.users[user.socketId] = user;
  }

  removeUser(socketId) {
      if (this.users[socketId]) {
          delete this.users[socketId];
      }
  }

  disconnectUsers() {
      Object.values(this.users).forEach((user) => {
          user.socket.disconnect(true);
      });
  }

  isUserConnected(userId) {
      return Object.values(this.presence.users).find(user => user.id === userId);
  }

  getConnectedUserConnections(userId) {
      return Object.values(this.presence.users).filter(user => user.id === userId);
  }

  getConnectionsByIp(ip) {
      return Object.values(this.presence.users).filter(user => user.ip === ip);
  }

  getCurrentState() { // eslint-disable-line class-methods-use-this
      return {}; // placeholder
  }

  onGetCurrentState() {
      try {
          return this.game && (this.isInitialized || this.isMaster) ? this.getCurrentState() : null;
      } catch (error) {
          this.log.warn('getCurrentState state return error', error);
          return null;
      }
  }

  async gracefullyDisconnectSockets() {
      if (!this.shouldGracefullyDisconnectSockets || this.isClosed || !this.socket) {
          return;
      }
      // Disconnect users from WS on SIGTERM
      this.log.info('SIGTERM signal received. Closing all WebSocket connections.');
      const connectedSocketSize = this.socket.sockets.size;
      this.log.info(`Initial sockets: ${connectedSocketSize}`);
      const percentage = 20; // disconnect 20% of sockets at a time
      const batchSize = Math.ceil(connectedSocketSize * (percentage / 100));
      const delay = 2000; // wait 5 seconds between batches

      this.log.info(`Starting disconnection process. Total connections: ${connectedSocketSize}`);
      await new Promise((resolve, reject) => {
          const gracefullyDisconnectSocketsTimeout = setTimeout(() => reject(new Error('Cleanup gracefullyDisconnectSocketsTimeout')), MAX_EXIT_TIMEOUT);
          this.disconnectBatch(batchSize, delay)
              .then(resolve)
              .catch(reject)
              .finally(() => clearTimeout(gracefullyDisconnectSocketsTimeout));
      });
  }

  async disconnectBatch(batchSize, delay) {
      const connectedSocketSize = this.socket.sockets.size;
      const currentConnectedSockets = this.socket.sockets.values();

      if (connectedSocketSize === 0) {
          this.log.info('All sockets disconnected.');
          return;
      }

      this.log.info('Checking if all sockets are disconnected...');
      this.log.info(`Current connected sockets: ${connectedSocketSize}`);
      this.log.info(`Disconnecting ${connectedSocketSize} sockets...`);

      let i;
      let disconnected = 0;
      const size = Math.min(batchSize, connectedSocketSize);
      for (i = 1; i <= size; i++) {
          try {
              const socket = currentConnectedSockets.next().value;
              socket.disconnect(true);
              disconnected += 1;
          } catch (error) {
              this.log.error(`Failed to disconnect socket: ${error}`);
          }
      }

      this.log.info(`Disconnected ${disconnected} connections after ${delay} ms.`);
      await sleep(delay);

      await this.disconnectBatch(batchSize, delay);
  }
}
