import { BetError, ClientError } from '../errors/client';
import Bet from './bet';
import SocketUser from './user';

export default class SocketBetUser extends SocketUser {
    BetClass = Bet;

    betLock = 0;

    betLockChecker = null;

    bets = [];

    createBet() {
        return new this.BetClass(this.user, this.server, this.socket);
    }

    attachAuthorizedListeners() {
        super.attachAuthorizedListeners();
        this.attachAuthorizedListener('place bet', this.onPlaceBet);
    }

    async clear() {
        this.bets = [];
    }

    async afterBetCommit() { // eslint-disable-line class-methods-use-this
        // placeholder
    }

    onPlaceBet = async (data) => {
        if (!this.checkIsAuthenticated()) {
            return;
        }

        // These are the only keys from the user-supplied data that we will use. All other keys should be ignored.
        const filterDataKeys = ['round', 'coin', 'amount', 'security_token', 'currency', 'ts'];
        const filteredData = {};
        filterDataKeys.forEach((key) => { filteredData[key] = data[key]; });

        this.log.debug('place bet', this.userId, filteredData);

        const bet = this.createBet();
        try {
            await this.acquireBetLock(bet);
            bet.setBetData(filteredData);
            bet.preValidate();
            await bet.recordBet();
        } catch (error) {
            let err = error;

            if (error.message.includes('bet_lock_timeout')) {
                this.log.debug('LockError detected', error);
                err = new BetError('We couldn\'t handle your bet in time due to your previous bet not being completely processed. Please try again later.');
            } else if (!(error instanceof ClientError)) {
                err = new BetError();
                this.log.error('unknown error on placing bet', error);
            }

            err.data = { ...err.data, ...data };

            this.socket.emitError(err);
        }

        await this.releaseBetLock();
        await bet.postCommit();
        await this.afterBetCommit();
    }

    async acquireBetLock(bet) {
        const lock = {
            id: bet.id,
            accept() {
                clearTimeout(this.timeout);
                this.resolve();
            },
        };

        const promise = new Promise((resolve, reject) => {
            lock.resolve = resolve;
            lock.timeout = setTimeout(() => {
                reject(new Error('bet_lock_timeout'));
            }, 5000);
        });

        this.bets.push(lock);

        if (this.bets.length === 1) {
            this.bets[0].accept();
        }

        return promise;
    }

    async releaseBetLock() {
        this.bets.shift();
        if (this.bets.length > 0) {
            this.bets[0].accept();
        }
    }
}
