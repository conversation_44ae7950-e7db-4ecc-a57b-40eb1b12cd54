import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import storeClient from './redis';

const options = {
    points: parseInt(process.env.RATE_LIMITER_POINTS || 120, 10),
    duration: parseInt(process.env.RATE_LIMITER_DURATION || 60, 10),
};

const optionsPing = {
    points: parseInt(process.env.RATE_LIMITER_POINTS_PING || 10, 10),
    duration: parseInt(process.env.RATE_LIMITER_DURATION_PING || 10, 10),
};

const rateLimiterMemory = new RateLimiterMemory({
    ...options,
});

const rateLimiterMemoryPing = new RateLimiterMemory({
    ...optionsPing,
});

const rateLimiterMemoryFallback = new RateLimiterMemory({
    ...options,
});

const rateLimiter = new RateLimiterRedis({
    ...options,
    storeClient,
    useRedisPackage: true,
    insuranceLimiter: rateLimiterMemoryFallback,
});

export { rateLimiter, rateLimiterMemory, rateLimiterMemoryPing };
