import path from 'path';
import nodemon from 'nodemon';
import { logger } from './helpers';

const log = logger('server-manager');

const application = path.join(__dirname, 'socket.js');

const shutdownListener = signal => process.on(signal, () => {
    nodemon
        .once('exit', () => {
            log.info('Child exited. Close process.');
            process.exit();
        });
});
shutdownListener('SIGTERM');
shutdownListener('SIGINT');
shutdownListener('SIGUSR2');

const buildFolder = (process.env.BUILD_FOLDER || 'build/').replace(/\/$/, '');

log.info('Starting app with buildFolder', buildFolder);

const nodemonConfig = {
    verbose: true,
    signal: 'SIGTERM',
    ext: 'js,json,yml',
    watch: [
        `${buildFolder}/`,
    ],
    ignore: [
        `${buildFolder}/index.js`,
    ],
};

nodemonConfig.script = application;

const extras = process.argv.indexOf('--');
if (extras > -1) {
    nodemonConfig.args = process.argv.slice(extras + 1);
}

log.debug('nodemonConfig', nodemonConfig);

nodemon(nodemonConfig);

nodemon
    .on('start', () => {
        log.info('App has started');
    })
    .on('quit', () => {
        log.info('App has quitted');
    })
    .on('exit', () => {
        log.info('App has exited');
    })
    .on('crash', () => {
        log.error('App has crashed');
    })
    .on('log', ({ message }) => {
        const pid = message.match(/child pid: ([0-9]+)/);
        if (pid) {
            log.debug('Child process', pid);
        }
    })
    .on('restart', (files) => {
        log.info('App restarted due to: ', files);
    });
