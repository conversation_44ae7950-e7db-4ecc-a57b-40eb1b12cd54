import os from 'os';
import cluster from 'cluster';
import blocked from 'blocked-at';
import { logger, toBoolean } from './helpers';
import { CAN_BE_SLAVE } from './lib/server';
import startServer from './main';

const log = logger('server');

const ENABLE_NODE_CLUSTER = toBoolean(process.env.ENABLE_NODE_CLUSTER);

if (toBoolean(process.env.ENABLE_EVENT_LOOP_MONITORING)) {
    log.warn('Event loop monitoring ENABLED');
    blocked((time, stack) => {
        log.warn('Event loop blocked for', time, 'milliseconds, operation started here:', stack?.[0], ' -- ', stack);
    }, {
        trimFalsePositives: true,
        threshold: 100,
        // resourcesCap: 100000,
        debug: false,
    });
}

const cpus = os.cpus().length;
log.info('Server CPU count', cpus);

if (CAN_BE_SLAVE && cluster.isMaster && ENABLE_NODE_CLUSTER) {
    for (let i = 0; i < cpus; i += 1) {
        log.info('Forking cluster worker', i + 1, '/', cpus);
        cluster.fork();
    }

    cluster.on('exit', (worker, code, signal) => {
        log.info('worker', worker.process.pid, 'died with code', code, 'and signal', signal);
    });
} else {
    if (CAN_BE_SLAVE && ENABLE_NODE_CLUSTER) {
        log.info('Starting cluster worker id=', cluster.worker.id);
    } else {
        log.info(!ENABLE_NODE_CLUSTER ? 'Starting main server without cluster support' : 'Starting master server');
    }

    startServer()
        .then(() => log.info('Server started'))
        .catch((error) => {
            log.error('Failed to start socket server', error);
            process.exit(98);
        });
}
