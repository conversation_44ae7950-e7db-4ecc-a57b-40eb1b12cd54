#!/bin/bash

echo "WARNING! STARTING IN DEV MODE!"
mkdir -p logs
mkdir -p 0x
rm logs/*.log
rm -rf logs/*.0x
rm -rf 0x/*

FILE='index';
if [ "$ENABLE_0X" = "1" ]; then
    FILE='0x';
elif [ "$DISABLE_NODEMON" = "1" ] || [ "$ENABLE_INSPECTOR" = "1" ]; then
    FILE='socket';
fi

start_node() {
  service=$3
  if [ -n "$service" ]; then
      filename=$service.$1
  else
      filename=slave.$1
  fi
  LOGFILE=./logs/$APP_NAME.$filename.log
  echo "LOGFILE: $LOGFILE"
  touch "$LOGFILE"
  date > "$LOGFILE"

  (
    export PORT=$1
    export INSPECT_PORT=$2
    if [ -n "$service" ]; then
        echo "Start master $service on port $PORT"
        export SERVICE_ENABLED=$service
        export MASTER_ENABLED=true
        export SLAVE_ENABLED=false
    else
        echo "Start slave node on port $PORT"
        export SERVICE_ENABLED=""
        export MASTER_ENABLED=false
        export SLAVE_ENABLED=true
    fi

    export BUILD_FOLDER=/app/build/$PORT

    if [ "$FILE" = "0x" ]; then
        export NAME_0X=$filename
    fi

    ln -snf /app/build/base "/app/build/$PORT"
    if [ "$ENABLE_INSPECTOR" = "1" ] ; then
        cp "build/$PORT/$FILE.js" "build/$PORT/inspect_$PORT.js"
        echo "Start inspector via file inspect_$PORT.js"
        node --inspect-brk=0.0.0.0:"$INSPECT_PORT" "build/$PORT/inspect_$PORT.js" >> "$LOGFILE" 2>&1 &
    else
        node "build/$PORT/$FILE.js" >> "$LOGFILE" 2>&1 &
    fi
  )
}

MAX_NODES=3
if [ -n "$NODE_COUNT" ] && [ "$NODE_COUNT" -gt "0" ]; then
    nodes="$NODE_COUNT"
else
    nodes=$MAX_NODES
fi

if [ "$nodes" -gt "$MAX_NODES" ]; then
    nodes="$MAX_NODES"
fi

npm run build -- -d build/base
npm run build -- --watch -d build/base 2>&1 &

sleep 5

inspect_port=9230
port=8001
declare -A services=(
    [chat]="$CHAT_ENABLED"
    [coinflip]="$COINFLIP_ENABLED"
    [roulette]="$ROULETTE_ENABLED"
    [casebattle]="$CASEBATTLE_ENABLED"
    [gameintegration]="$GAMEINTEGRATION_ENABLED"
)
for service in "${!services[@]}"
do
    enabled=${services[$service]}
	if [ "$enabled" = "true" ]; then
        echo "$service ENABLED"
        start_node $port $inspect_port "$service"
        if [ "$DISABLE_STANDBY_MASTER" != "1" ] ; then
            (( port++ ))
            (( inspect_port++ ))
            start_node "$port" "$inspect_port" "$service"
        fi
    else
        echo "$service NOT ENABLED"
    fi
    (( port++ ))
    (( inspect_port++ ))
done

if [ -z "$FIRST_BACKEND_PORT" ]; then
    port=7001
else
    port=$FIRST_BACKEND_PORT
fi

# shellcheck disable=SC2034
for i in $(seq 1 $nodes); do
    start_node "$port" "$inspect_port"
    (( port++ ))
    (( inspect_port++ ))
done

tail -f /dev/null
