apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-pricing-node-configs
data:
  .env: |-
    INIT=true
    GAMES=CS:GO
    POSTGRES_HOST=postgres-01-s.cobrxc45koyk.us-east-2.rds.amazonaws.com
    POSTGRES_PORT=5432
    POSTGRES_USER=csgoe_kube_shared_ps
    POSTGRES_DB=pricing-node
    POSTGRES_POOL_SIZE=150
    PROXY_USER=sp623c7085
    PROXY_HOST=gate.dc.smartproxy.com
    PROXY_PORT=20000
    REDIS_HOST=redis-k-03-s.jsp1se.ng.0001.use2.cache.amazonaws.com
    REDIS_PORT=6379
    PGADMIN_DEFAULT_EMAIL=<EMAIL>
    ENV=production
    NODE_ENV=production
    SKINPORT_CLIENTID=941cf860ba464ee9904b29835645f2c6
    S3_HOST=s3.us-east-2.amazonaws.com
    S3_ACCOUNT=************
    S3_KEY=AKIA2KUZWYYCNCWYOTD5
    S3_BUCKET=csgoempire-pricig-node-s
    DMARKET_PUB=fb183f6ee64d6bfaa4d80fb75223191776e96a9c2626f0d67b1221919d51c819
    PRICING_CRON="0 30 */4 * * *"
    MATERIALIZED_VIEW_CRON="0 30 * * * *"
    REQUEST_CONCURRENCY=3
    INTERNAL_HOSTNAME=csgoempire-pricing-node
    INTERNAL_API_HOST=csgoempire-bots-backend
    PREDICT_SERVICE=buffv2
    LOG_LEVELS="log,debug,warn,error"
    NO_COLOR="true"
    DEFENSE_MODE=false
    PRIMARY_MARKETPLACES=buff,buff_buy,csgoroll
