apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-pricing-node-configs
data:
  .env: |-
    INIT=true
    GAMES=CS:GO
    POSTGRES_HOST=rds-postgres-01-p.cmy85lc4rv8t.us-east-2.rds.amazonaws.com
    POSTGRES_PORT=5432
    POSTGRES_USER=csgoe_kube_shared_ps
    POSTGRES_DB=pricing-node
    POSTGRES_POOL_SIZE=800
    PROXY_USER=sp623c7085
    PROXY_HOST=gate.dc.smartproxy.com
    PROXY_PORT=20000
    REDIS_HOST=redis-k-03-p.ivly0u.ng.0001.use2.cache.amazonaws.com
    REDIS_PORT=6379
    ENV=production
    NODE_ENV=production
    SKINPORT_CLIENTID=941cf860ba464ee9904b29835645f2c6
    S3_HOST=s3.us-east-2.amazonaws.com
    S3_ACCOUNT=************
    S3_KEY=AKIAVK3GNUQZJLBM4OLE
    S3_BUCKET=csgoempire-pricig-node-prod
    DMARKET_PUB=7d7b68de4515c821d58633abaeed74181784d6a0b8e7f0cfc8b7ecfc2c1c89f1
    PRICING_CRON="0 30 * * * *"
    MATERIALIZED_VIEW_CRON="0 45 * * * *"
    REQUEST_CONCURRENCY=10
    NODE_OPTIONS="--max_old_space_size=4096"
    INTERNAL_HOSTNAME=csgoempire-pricing-node
    INTERNAL_API_HOST=csgoempire-bots-backend
    LOG_LEVELS="log,debug,warn,error"
    NO_COLOR="true"
    PREDICT_SERVICE=buffv2
    DEFENSE_MODE=false
    PRIMARY_MARKETPLACES=buff,buff_buy,csgoroll
