apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-pricing-node-secrets
    namespace: default
type: Opaque
stringData:
    POSTGRES_PASSWORD: ENC[AES256_GCM,data:RJtL9E9bII+fukKn,iv:BfMfkhX4klghLj6T9UcFsucKOhOLJ3D0rhdNfSHNRyQ=,tag:kkX5n7YTVGUk3d1VhuPqvg==,type:str]
    PGADMIN_DEFAULT_PASSWORD: ENC[AES256_GCM,data:5/Rl4bI=,iv:DYkg8Jf5TXtn3QF0y09LAU2nZPTfKcVrvcUtNoQ9V5I=,tag:FEyOUANReo1C7RUozfVAuA==,type:str]
    BITSKINS_APIKEY: ENC[AES256_GCM,data:vvSOo0QneeWoJzK8IEBh8okiuH4NFgT/P4oe6YF1xFAFE1wG,iv:/cXt26nR8v4D1i2HH+tHLrDNTRfQnVGYaVBxYsgS9ZA=,tag:k/wcpRoTxkD+DFLw1DgzCA==,type:str]
    BITSKINS_SECRET: ENC[AES256_GCM,data:GTslV18Jq4G6l5Z9grykJg==,iv:OdLhgDfzUDp0lVsbzGfbGNi3AVfrWzl+wLTRXVnmURU=,tag:ZLqIa+/FI8cjgEM0F0Q8Aw==,type:str]
    SKINPORT_SECRET: ENC[AES256_GCM,data:6n9h2sxlXPeCba33mz2ykjEbt5nGGvFCwDxdCC9nL6/Il9j2YZF0EFDOWrDITXoXaRX8P+XVNJzBDAbiVIPCmw99rGcZt2yoQQ9n+J/OTST/jhc7qAFH9w==,iv:SBRr/NlRHEHyx4iZEFhfS1zn1+iEEuPjKtyA6y87tA4=,tag:YsHzUU4D7GtemupeAuWxnQ==,type:str]
    S3_SECRET: ENC[AES256_GCM,data:NeAj/a9NZu5yUiFKSC5DtjfnvFBNmNk3BdvJaufaNGK3SAC/n/wMyg==,iv:PL77AAqVbkiOqLAZ7DCbU2ZTfga6NHmDAkCHdDpvCsY=,tag:PsD3mJ0/9kN4PpD/bdvqDg==,type:str]
    DMARKET_SECRET: ENC[AES256_GCM,data:/2b4yJOCQL+gG3vW/dg321Ny+y+QacS1UFgzOdkGCsgJsXlXTNvHucHcjIQbyYWIvxZQ+62cE9krsI7h4ELd5EQOKtNiJe0Amx/uH+O3xEULPio2jLWLXUhWzyKGSrZKSOm7NWFlp4PBE0IYkS+7/kxiilzKJEK9jG/rbYN59IM=,iv:BIMDnkySLYbZOrmY9M+YCVqJp9XGotHcFoZaPowjqVA=,tag:yAmK6Ugxx3AqrBYIidhmpg==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:orQBMRqqp+zS4YltqluKemJYrNg=,iv:oxUAtu/hu6MbtObDi0d68M82hx66+CXH19rq4nxVq5k=,tag:D5KVRdWiT40+BRjOnySdCg==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/042a6a43-65a2-44a3-abe7-9f17ff1b7677
          created_at: "2023-11-13T12:59:05Z"
          enc: AQICAHhCoM3M4b9kgj3WXOlXYGSpssDPTNBbrRl46kn/pVYd9wH/MuzkofhHBcpW53s/u6vfAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMMkTWv1nqqxUAVsXvAgEQgDvVTrcCDOk3Wut+bnrQ8Rt2KoqDtsuPOfwgc764bjhfz0B5jMMFQlAdCc1YL5Kr980SFCUwH02eRQfSZw==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2023-11-13T12:59:06Z"
    mac: ENC[AES256_GCM,data:kD71hRE5ooo4bTb872Yi3ke8nqGmNgXvDYyIXJBPyf9OVbrQCyYE9B2rEmICbHEctVk/H+G5lKGWt75kq2pRZmKzfw1zOC+KqHeCi3wWRJ5+vN28qAjVmr5Aipn0gECj7gYRuG4Xq0KlBawwLCcu7+3JRAdob9N9pHDHw5WqqPU=,iv:928haaIDCusXaf1z3COHPgQ7mrbdYGvreH8doI/nfDA=,tag:cNZqEkDkUP1VahY0nw55eA==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
