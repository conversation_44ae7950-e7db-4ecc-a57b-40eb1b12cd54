apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-pricing-node-configs
  namespace: default
data:
  .env: |-
    INIT=true
    GAMES=CS:GO
    POSTGRES_HOST=postgres-01-d.cmvhmtfqj1tw.us-east-2.rds.amazonaws.com
    POSTGRES_PORT=5432
    POSTGRES_USER=csgoe_kube_shared_ps
    POSTGRES_DB=pricing-node
    POSTGRES_POOL_SIZE=150
    PROXY_USER=sp623c7085
    PROXY_HOST=gate.dc.smartproxy.com
    PROXY_PORT=20000
    REDIS_HOST=redis-k-03-d.lh2tb1.ng.0001.use2.cache.amazonaws.com
    REDIS_PORT=6379
    PGADMIN_DEFAULT_EMAIL=<EMAIL>
    ENV=development
    NODE_ENV=development
    SKINPORT_CLIENTID=941cf860ba464ee9904b29835645f2c6
    S3_HOST=s3.us-east-2.amazonaws.com
    S3_ACCOUNT=************
    S3_KEY=AKIA24GZNALWZDKIDWVK
    S3_BUCKET=csgoempire-pricig-node-d
    DMARKET_PUB=47022758ebb36ba29c61476ae374dc34797bc3c084d583b0d51edc3560abb570
    PRICING_CRON="0 30 */12 * * *"
    MATERIALIZED_VIEW_CRON="0 45 */12 * * *"
    REQUEST_CONCURRENCY=10
    INTERNAL_HOSTNAME=csgoempire-pricing-node
    INTERNAL_API_HOST=csgoempire-backend
    PREDICT_SERVICE=buffv2
    LOG_LEVELS="log,debug,warn,error"
    NO_COLOR="true"
    DEFENSE_MODE=false
    PRIMARY_MARKETPLACES=buff,buff_buy,csgoroll
  NODE_ENV: development
