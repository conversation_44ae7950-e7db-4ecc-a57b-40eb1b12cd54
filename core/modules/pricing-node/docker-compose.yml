version: "3.9"

# defaults to csgo<PERSON><PERSON>, override with COMPOSE_PROJECT_NAME
name: csgoempire

networks:
  app:
    name: ${CSGOEMPIRE_DOCKER_NETWORK_NAME:-${APP_NAME:-csgoempire}}

services:
  mock-s3:
    container_name: csgoempire-pricing-mock-s3
    image: localstack/localstack:latest
    ports:
      - "4566-4583:4566-4583"
    env_file:
        - .env.local
        - .env
    environment:
      - AWS_DEFAULT_REGION=us-east-2
      - EDGE_PORT=4566
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - ./.localstack:/tmp/localstack
      - /var/run/docker.sock:/var/run/docker.sock
      - ./.aws/buckets.sh:/etc/localstack/init/ready.d/buckets.sh
    networks:
        app:
            aliases:
                - csgoempire-pricing-node-s3

  postgres:
    container_name: csgoempire-pricing-postgres
    image: postgres:latest
    restart: always
    command: postgres -c 'max_connections=1000'
    ports:
      - "5432:5432"
    networks:
        app:
            aliases:
                - csgoempire-pricing-node-postgres
    env_file:
      - .env.local
      - .env

  redis:
    container_name: csgoempire-pricing-redis
    image: redis:latest
    restart: always
    ports:
      - 63799:6379
    networks:
        app:
            aliases:
                - csgoempire-pricing-node-redis

  app:
    container_name: csgoempire-pricing-node
    build:
      context: ./
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    env_file:
      - .env.local
      - .env
    command: npm run start:dev
    volumes:
      - ./app/src:/app/src
      - ./app/package.json:/app/package.json
      - ./app/yarn.lock:/app/yarn.lock
      - ./app/nest-cli.json:/app/nest-cli.json
      - ./app/tsconfig.build.json:/app/tsconfig.build.json
      - ./app/tsconfig.eslint.json:/app/tsconfig.eslint.json
      - ./app/tsconfig.json:/app/tsconfig.json
    networks:
      app:
        aliases:
          - csgoempire-pricing-node
