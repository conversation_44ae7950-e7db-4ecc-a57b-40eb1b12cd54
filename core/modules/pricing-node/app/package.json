{"name": "pricing-node", "version": "0.0.1", "engines": {"node": ">=16.0.0"}, "description": "", "author": "", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint -c .eslintrc.js .", "test": "npm run lint && npm run jest", "jest": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.370.0", "@nestjs/axios": "^2.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.2", "@nestjs/core": "^9.0.0", "@nestjs/event-emitter": "^1.4.1", "@nestjs/microservices": "^9.3.12", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^2.2.0", "@nestjs/terminus": "^9.2.2", "@nestjs/typeorm": "^9.0.1", "axios": "^1.3.4", "bee-queue": "^1.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "fast-glob": "^3.2.12", "https-proxy-agent": "^5.0.1", "ioredis": "^5.3.1", "notp": "^2.0.3", "pg": "^8.9.0", "redis": "^4.6.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "socks-proxy-agent": "^7.0.0", "thirty-two": "^1.0.2", "tweetnacl": "^1.0.3", "typeorm": "^0.3.12", "typeorm-naming-strategies": "^4.1.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/jest": "29.2.4", "@types/node": "20.4.2", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.2.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "jest": "29.3.1", "prettier": "^2.8.4", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^5.1.6", "yarn": "^1.22.19"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}