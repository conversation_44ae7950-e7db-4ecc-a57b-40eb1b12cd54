{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "esModuleInterop": false, "paths": {"@app/*": ["./src/*", "./assets/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.json", "test/**/*.ts", "*.js"], "exclude": ["node_modules", "dist"]}