// import { Test, TestingModule } from '@nestjs/testing';
// import { INestApplication } from '@nestjs/common';
// import * as request from 'supertest';
// import { BaseModule as AppModule } from '../src/base.module';

// TODO: Write tests
describe('AppController (e2e)', () => {
	// let app: INestApplication;

	// beforeEach(async () => {
	// 	const moduleFixture: TestingModule = await Test.createTestingModule({
	// 		imports: [AppModule],
	// 	}).compile();

	// 	app = moduleFixture.createNestApplication();
	// 	await app.init();
	// });

	// it('/helth (GET)', () => request(app.getHttpServer())
	// 		.get('/health')
	// 		.expect(200)
	// 		.expect({ status: true }));

    it('placeholder', () => {
        expect(true).toBe(true); // placeholder for the pipeline
      });
});
