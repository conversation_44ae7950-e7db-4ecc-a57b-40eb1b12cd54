module.exports = {
    root: true,
    plugins: ['@typescript-eslint', 'import', 'prettier'],
    extends: [
        'airbnb-base',
        'airbnb-typescript/base',
        'plugin:@typescript-eslint/recommended',
        'prettier',
    ],
    "rules": {
        "import/prefer-default-export": [
            ( "off" ),
            { "target": "any" }
        ],
        "no-unused-vars": "off",
        "@typescript-eslint/no-unused-vars": "error",
        "object-curly-spacing": ["error", "always"],
        semi: ["error", "always"],
    },
    parser: '@typescript-eslint/parser',
    parserOptions: {
        project: './tsconfig.eslint.json',
    },
    env: {
        node: true,
    },
};
