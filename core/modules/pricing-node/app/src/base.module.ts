import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import { ScheduleModule } from '@nestjs/schedule';
import { ModuleLoaderModule } from './module-loader/module-loader.module';

@Module({
    imports: [
        ConfigModule.forRoot(),
        ScheduleModule.forRoot(),
        ModuleLoaderModule.register({
          name: 'common-modules',
          /**
           * Make sure the path resolves to the **DIST** subdirectory, (we are no longer in TS land but JS land!)
           */
          path: path.resolve(__dirname, './modules/'),
          fileSpec: '**/*.module.js',
        }),
    ],
})
export class BaseModule { }
