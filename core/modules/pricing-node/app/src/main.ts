import { LogLevel } from '@nestjs/common/services/logger.service';
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import * as cors from 'cors';
import { BaseModule } from './base.module';

Error.stackTraceLimit = 1000;

async function bootstrap() {
	const logLevels: LogLevel[] = process.env.LOG_LEVELS?.split(/\s*,\s*/) as LogLevel[] || ['log', 'debug', 'error', 'verbose', 'warn'];
	const app = await NestFactory.create(BaseModule, {
		logger: logLevels
	});

	const allowedOrigins = [
		'http://csgoempire.local:8081',
		'http://localhost:3000',
		'https://csgoempire.com',
		'https://csgoempire.tv',
		'https://csgoempiretr.com',
		'https://csgoempire88.com',
		'https://csgoempirev2.cam',
		'https://csgoempire.io',
		'https://csgoempire.info',
		'https://csgoempire.vip',
		'https://csgoempire.fun',
		'https://csgoempire.biz',
		'https://csgoempire.biz',
		'https://csgoempire.vegas',
		'https://csgoempire.link',
		`http://${process.env.INTERNAL_HOSTNAME}`
	];

	// enable API Access if dev mode
	if (process.env.npm_lifecycle_event === 'start:dev') {
		allowedOrigins.push(undefined);
	}

	const corsOptions = {
		credentials: true,
		origin: (origin, callback) => (!origin || allowedOrigins.includes(origin)) ? callback(null, true) : callback({ status: false, message: 'Origin not allowed' }, true)
	};

	app.use(cors(corsOptions));

	app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

	app.enableShutdownHooks();
	await app.listen(3000);
}

bootstrap();
