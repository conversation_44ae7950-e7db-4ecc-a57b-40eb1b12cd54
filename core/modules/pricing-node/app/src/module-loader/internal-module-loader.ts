import { Logger, DynamicModule } from '@nestjs/common';
import * as fb from 'fast-glob';
import * as path from 'path';
import {
    IModuleLoaderOptions,
} from './module-loader-defs';

interface IModuleInfo {
    name: string;
    module: DynamicModule;
}

/**
 * @description helper static class to load modules dynamically.
 */
export class InternalModuleLoader {
    static readonly logger = new Logger(InternalModuleLoader.name);

    /**
     * @param _options for GLOB searches
     * @returns a Promise thats resolves to a list of name and module references based on _options filespec
     */
    static async loadModules(
        _options: IModuleLoaderOptions,
    ): Promise<Array<IModuleInfo>> {
        return new Promise((resolve) => {
            this.getModuleFileNames(_options).then((filePaths: Array<string>) => {
                if (filePaths.length === 0) {
                    resolve([]);
                } else {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const loadedModules: Array<Promise<any>> = filePaths.map((filePath) =>
                        this.loadModule(filePath),
                    );
                    if (loadedModules.length === 0) {
                        resolve([]);
                    } else {
                        const moduleInfos: Array<IModuleInfo> = [];
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        Promise.all(loadedModules).then((modules: Array<any>) => {
                            for (let i = 0; i < modules.length; i += 1) {
                                const module = modules[i];
                                const moduleField = Object.keys(module).find(
                                    (key) => key.indexOf('Module') >= 0,
                                );
                                if (moduleField) {
                                    moduleInfos.push({
                                        name: moduleField,
                                        module: module[moduleField],
                                    });
                                }
                            }
                            resolve(moduleInfos);
                        });
                    }
                }
            });
        });
    }

    /**
     * @description Uses native import() to dynamicly load a module
     * @param modulePath
     * @returns a Promise thats resolves to module loaded
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private static async loadModule(modulePath: string): Promise<any> {
        return import(modulePath);
    }

    /**
     * @description Uses FatsGlob to load the filenames for the modules
     * @param _options for GLOB searches
     * @returns a list of module's file paths
     */
    private static async getModuleFileNames(
        _options: IModuleLoaderOptions,
    ): Promise<Array<string>> {
        const spec: Array<string> = (
            typeof _options.fileSpec === 'string'
                ? [_options.fileSpec]
                : _options.fileSpec
        ).map((fileSpec) => path.join(_options.path, fileSpec));
        const options: fb.Options = {
            onlyFiles: true,
        };
        if (_options.depht) {
            options.deep = _options.depht < 0 ? Infinity : _options.depht;
        }
        if (_options.ignoreSpec) {
            options.ignore = Array.isArray(_options.ignoreSpec)
                ? _options.ignoreSpec
                : [_options.ignoreSpec];
        }
        this.logger.log(`**Module Loader FileSpec**: "${spec}"`);

        return fb(spec, options);
    }
}
