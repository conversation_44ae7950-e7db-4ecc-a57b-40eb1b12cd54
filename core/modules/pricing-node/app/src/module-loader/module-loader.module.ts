import { Module, DynamicModule } from '@nestjs/common';
import {
    MODULE_LOADER,
    MODULE_LOADER_OPTIONS,
    MODULE_LOADER_NAMES,
    IModuleLoaderOptions,
} from './module-loader-defs';
import { ModuleLoaderService } from './module-loader.service';
import { InternalModuleLoader } from './internal-module-loader';

export const moduleLoaderFactory = {
    provide: MODULE_LOADER,
    useFactory: () => { },
    inject: [ModuleLoaderService],
};

@Module({})
export class ModuleLoaderModule {
    /**
     * @description Load Modules dynamically via GLOBs and native import() function.
     * @param moduleLoaderOptions options for GLOB searches
     */
    public static async register(
        moduleLoaderOptions: IModuleLoaderOptions,
    ): Promise<DynamicModule> {
        const moduleInfos = await InternalModuleLoader.loadModules(
            moduleLoaderOptions,
        );
        const modules = moduleInfos.map((moduleInfo) => moduleInfo.module);
        const moduleNames = moduleInfos.map((moduleInfo) => moduleInfo.name);

        return {
            module: ModuleLoaderModule,
            imports: [...modules],
            providers: [
                {
                    provide: MODULE_LOADER_OPTIONS,
                    useValue: moduleLoaderOptions,
                },
                {
                    provide: MODULE_LOADER_NAMES,
                    useValue: moduleNames,
                },
                ModuleLoaderService,
                moduleLoaderFactory,
            ],
        };
    }
}
