import { Injectable, Inject, Scope } from '@nestjs/common';
import {
    MODULE_LOADER_OPTIONS,
    MODULE_LOADER_NAMES,
    IModuleLoaderOptions,
} from './module-loader-defs';

@Injectable({
    scope: Scope.TRANSIENT,
})
export class ModuleLoaderService {
    constructor(
        @Inject(MODULE_LOADER_OPTIONS) private _options: IModuleLoaderOptions,
        @Inject(MODULE_LOADER_NAMES) private _moduleNames: Array<string>,
    ) { }

}
