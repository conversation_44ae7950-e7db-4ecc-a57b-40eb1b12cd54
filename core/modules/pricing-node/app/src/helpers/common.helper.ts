import { randomBytes } from 'crypto';
import { lastValueFrom, defer, from } from 'rxjs';
import { mergeAll } from 'rxjs/operators';

export const toBoolean = (val) => {
    const falsy = /^(?:f(?:alse)?|no?|0+)$/i;
    return !falsy.test(val) && !!val;
};

/**
 * Pause the process for x ms
 * @param millis sleep ms
 * @returns Promise
 */
export const sleep = (millis) => new Promise((resolve) => {
    setTimeout(resolve, millis);
});

export const phases = [
    'Emerald',
    'Sapphire',
    'Ruby',
    'Black Pearl',
    'Phase 1',
    'Phase 2',
    'Phase 3',
    'Phase 4'
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const mapLimit = async (items: any[], callback: (any), limit = 20) => {
    if (!items.length) {
        return;
    }

    const observables = items.map(item => defer(() => callback(item)));
    await lastValueFrom(from(observables).pipe(mergeAll(limit)));
};

export const dopplerPhases = {
    418: 'Phase 1',
    419: 'Phase 2',
    420: 'Phase 3',
    421: 'Phase 4',
    415: 'Ruby',
    416: 'Sapphire',
    417: 'Black Pearl',
    569: 'Phase 1',
    570: 'Phase 2',
    571: 'Phase 3',
    572: 'Phase 4',
    568: 'Emerald',
    618: 'Phase 2',
    619: 'Sapphire',
    617: 'Black Pearl',
    852: 'Phase 1',
    853: 'Phase 2',
    854: 'Phase 3',
    855: 'Phase 4',
    1119: 'Emerald',
    1120: 'Phase 1',
    1121: 'Phase 2',
    1122: 'Phase 3',
    1123: 'Phase 4',
};

export const calculatePercentageDifference = (oldValue, newValue) => (((newValue - oldValue) / oldValue) * 100);

export const steamMarketHeaders = () => {
    // Define the number of bytes you want to generate (12 in this case)
    const byteCount = 12;
    // Generate random bytes
    const randomBytesBuffer = randomBytes(byteCount);
    // Convert the random bytes to a hexadecimal string
    const hexString = randomBytesBuffer.toString('hex');

    return {
        Connection: 'keep-alive',
        Accept: '*/*',
        'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 10.0; en-US; Valve Steam Client/default/1668138960; ) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        Referer: "https://steamcommunity.com/market/",
        'Accept-Language': 'en-US,en;q=0.9',
        // Despite its name, the Steam "sessionid" cookie is just a CSRF token. It's randomly generated, so sending
        // one of our own crafting is perfectly fine and can't be detected as a spoof.
        Cookie: `sessionid=${hexString};`,
    };
};
