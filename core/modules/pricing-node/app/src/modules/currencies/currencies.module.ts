import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Currency } from '../database/entities/currency/currency.entity';
import { CurrenciesService } from './currencies.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([Currency]),
		HttpModule,
	],
	providers: [CurrenciesService],
	exports: [CurrenciesService],
})
export class CurrenciesModule {}
