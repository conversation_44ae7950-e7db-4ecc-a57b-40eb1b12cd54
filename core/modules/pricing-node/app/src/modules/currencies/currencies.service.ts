import { HttpService } from '@nestjs/axios';
import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { lastValueFrom } from 'rxjs';
import { Repository } from 'typeorm';
import { mapLimit } from '@app/helpers/common.helper';
import { Currency } from '../database/entities/currency/currency.entity';

/**
 * Currencies Service
 */
@Injectable()
export class CurrenciesService implements OnApplicationBootstrap {
	private currencyEndpoint =
		'https://query1.finance.yahoo.com/v7/finance/spark?symbols=USDCNY=X,USDEUR=X';

	constructor(
		@InjectRepository(Currency)
		private readonly currencyRepository: Repository<Currency>,
		private readonly httpService: HttpService,
	) { }

	async onApplicationBootstrap() {
		await this.updateCurrencies();
	}

	/**
	 * Update Currencies rates
	 * @Cron updating every 15 minutes
	 */
	@Cron('0 */15 * * * *')
	private async updateCurrencies(): Promise<void> {
		const currencies = (
			await lastValueFrom(this.httpService.get(this.currencyEndpoint))
		).data;

		await mapLimit(currencies.spark.result, data => this.currencyRepository.upsert(
			{
				symbol: data.response[0].meta.currency,
				rate: data.response[0].meta.regularMarketPrice,
			},
			{
				conflictPaths: ['symbol'],
			},
		));

	}

	/**
	 * Get the currency from DB
	 * @param symbol Currency Symbol
	 * @returns Currency Entity
	 */
	public async getCurrency(symbol: string): Promise<Currency> {
		const result = await this.currencyRepository.findOne({
			where: {
				symbol,
			},
		});

		if (result) {
			return result;
		}

		/**
		 * Edge case, usually at the first run we don't have currency rates in the db
		 */
		await this.updateCurrencies();
		return this.getCurrency(symbol);

	}
}
