import {
	<PERSON><PERSON>,
	<PERSON>ieType,
} from '@app/modules/database/entities/cookie/cookie.entity';
import { Game } from '@app/modules/database/entities/game/game.entity';
import { ItemId } from '@app/modules/database/entities/item/item-id.entity';
import { ItemPrice } from '@app/modules/database/entities/item/item-price.entity';
import { Item } from '@app/modules/database/entities/item/item.entity';
import { Marketplace } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, MoreThan, Repository } from 'typeorm';

interface Meta {
	key: string;
	name: string;
	games: string[];
}

/**
 * Marketplace Service
 */
@Injectable()
export class MarketplaceService {

	private queue = [];

	private readonly maxQueueSize = 100;

	private to;

	private readonly logger = new Logger();

	private readonly priceThreshold = 5; // 5 USD cent ($0.05)

	constructor(
		@InjectRepository(ItemPrice)
		private readonly itemPriceRepository: Repository<ItemPrice>,
		@InjectRepository(Item)
		private readonly itemRepository: Repository<Item>,
		@InjectRepository(Game)
		private readonly gameRepository: Repository<Game>,
		@InjectRepository(Marketplace)
		private readonly marketplaceRepository: Repository<Marketplace>,
		@InjectRepository(Cookie)
		private readonly cookieRepository: Repository<Cookie>,
		@InjectRepository(ItemId)
		private readonly itemIdRepository: Repository<ItemId>,
	) { }

	/**
	 * Processing metadata for marketplace and returing the marketplace entity
	 * @param meta Marketplace Metadata
	 */
	public async getMarketplace(meta: Meta): Promise<Marketplace> {
		let marketplace = await this.marketplaceRepository.findOne({
			where: {
				key: meta.key,
			},
		});

		const games = await this.gameRepository
			.createQueryBuilder('game')
			.where('short_name IN (:...games)', { games: meta.games })
			.getMany();

		marketplace = await this.marketplaceRepository.save({
			...meta,
			...marketplace,
			games,
		});

		return marketplace;
	}

	/**
	 * Store Marketplace Price
	 * @param marketplace Marketplace Entity
	 * @param item Item Entity
	 * @param price USD price
	 * @param count Listing count for the item
	 * @returns Boolean true/false
	 */
	public async storePrice(
		marketplace: Marketplace,
		item: Item,
		price: number,
		count = 0,
	): Promise<boolean> {
		if (!item) {
			// edge case
			return false;
		}

		if (Number.isNaN(price)) {
			return false;
		}

		// Skip wrongly priced items
		if (price > 1000000 * 100) { // 1M USD
			return false;
		}

		const lastPrice = await this.itemPriceRepository.findOne({
			where: {
				item,
				marketplace,
				created_at: MoreThan(new Date(new Date().getTime() - 24 * 60 * 60 * 1000)) // Created At more than 1h
			},
			order: {
				created_at: 'DESC',
			},
		});

		if (lastPrice) {
			let change = lastPrice.price - price;
			if (change < 0) {
				change *= -1;
			}
			if (change <= this.priceThreshold) {
				// the item not changed at all or not reached the price threshold, returning false
				return false;
			}
		}

		// console.log(`${item?.market_hash_name} changed. change: ${lastPrice?.price - price} ${lastPrice?.price} => ${price}, ${lastPrice?.count} => ${count}`);


		this.queue.push({
			marketplace,
			item,
			price,
			count,
		});

		await this.saveQueue();

		/**
		 * Saving the last prices, and dont let them hang for x minutes
		 */
		clearTimeout(this.to);

		this.to = setTimeout(async () => {
			await this.saveQueue(true);
		}, 30 * 1000);

		return true;
	}

	/**
	 * Trying to save the actual queue
	 * @param force boolean - set true to force the save
	 */
	private async saveQueue(force = false) {
		if (this.queue.length < this.maxQueueSize && !force) {
			return;
		}

		const { queue } = this;
		this.queue = [];
		try {
			await this.itemPriceRepository.save(queue);
		} catch (e) {
			await this.error(e);
		}
	}

	/**
	 * Logging errors for marketplace parsers
	 * @param error error Object
	 * @param marketplace Marketplace Entity
	 */
	public async error(
		error: Error,
		contextName: string = MarketplaceService.name
	): Promise<void> {
		this.logger.error(error.message, error.stack, contextName);
		// TODO: Discord, Slack & DB logging
	}

	/**
	 * Get item by market name
	 * @param marketHashName Item Market name
	 * @param appId App Id
	 * @returns Item
	 */
	public async getItemByHashName(
		marketHashName: string,
		appId: number,
	): Promise<Item> {
		return this.itemRepository.findOne({
			where: {
				market_hash_name: marketHashName,
				game: {
					app_id: appId,
				},
			},
			cache: 60 * 1000,
		});
	}

	/**
	 * Get item by 3rd party key & value
	 * @param key string
	 * @param value string
	 * @returns Item | undefined
	 */
	public async getItemByItemId(
		key: string,
		value: string,
	): Promise<Item> {
		const itemId = await this.itemIdRepository.findOne({
			relations: ['item'],
			where: {
				key,
				value
			},
			cache: 60 * 1000,
		});
		return itemId?.item;
	}

	/**
	 * Get Doppler Items
	 * @returns Item[]
	 */
	public async getDopplers(): Promise<Item[]> {
		return this.itemRepository.find({
			where: {
				market_hash_name: Like('%Doppler%'),
			},
			cache: 60 * 1000,
		});
	}

	/**
	 * Get a cookie value from DB
	 * @param cookieType Type of the cookie, ex: CookieType.Buff163
	 * @returns Cookie value
	 */
	public async getCookie(cookieType: CookieType): Promise<string | false> {
		const cookie = await this.cookieRepository.findOne({
			where: {
				type: cookieType,
				status: true,
			},
			order: {
				requests: 'ASC',
			},
			cache: false,
		});

		if (!cookie) {
			return false;
		}

		cookie.requests += 1;

		await this.cookieRepository.save(cookie);

		return cookie.cookie;
	}

	/**
	 * Update Cookie status to false if its expired or banned.
	 * @param cookieValue Cookie value to update its status
	 */
	public async setCookieStatus(cookieValue: string): Promise<void> {
		await this.cookieRepository.update(
			{
				cookie: cookieValue,
			},
			{
				status: false,
			},
		);

		const cookie = await this.cookieRepository.findOne({
			where: {
				cookie: cookieValue,
			},
		});

		const count = await this.cookieRepository.count({
			where: {
				status: true,
				type: cookie.type,
			},
		});

		await this.error(
			new Error(`Cookie Expired for ${cookie.type}, cookies left: ${count}`),
			MarketplaceService.name,
		);
	}


	/**
	 *
	 * @param item Item Entity
	 * @param key Key
	 * @param value Value
	 */
	public async saveCustomId(
		itemParam: Item | string,
		key: string,
		value: string,
		appId = 730,
	): Promise<void> {
		const item = typeof itemParam === 'string' ? await this.getItemByHashName(itemParam, appId) : itemParam;

		if (!item) {
			return;
		}

		if (!item.ids) {
			item.ids = [];
		}

		const already = item?.ids.find((id) => id.key === key);

		if (already) {
			return;
		}

		await this.itemIdRepository.save({
			key,
			value,
			item,
		});
	}

	/**
	 * Get all custom Id by key
	 * @param key string
	 * @returns string[]
	 */
	async getItemIds(key: string): Promise<string[]> {
		return (await this.itemIdRepository.find({
			where: {
				key
			}
		})).map(itemId => itemId.value);
	}
}
