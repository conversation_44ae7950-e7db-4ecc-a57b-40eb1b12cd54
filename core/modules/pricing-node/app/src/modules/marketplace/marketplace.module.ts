import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { MarketplaceService } from './marketplace.service';
import { Item } from '../database/entities/item/item.entity';
import { Marketplace } from '../database/entities/marketplace/marketplace.entity';
import { Game } from '../database/entities/game/game.entity';
import { ItemPrice } from '../database/entities/item/item-price.entity';
import { ItemId } from '../database/entities/item/item-id.entity';
import { Cookie } from '../database/entities/cookie/cookie.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            Item,
            Marketplace,
            Game,
            ItemPrice,
            ItemId,
            Cookie,
        ]),
        HttpModule,
    ],
    providers: [
        MarketplaceService,
    ],
    exports: [
        MarketplaceService,
    ],
})
export class MarketplaceModule { }
