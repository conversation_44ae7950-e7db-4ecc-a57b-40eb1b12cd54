import { Controller, Get, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { HealthCheckService, HealthCheck, TypeOrmHealthIndicator, MicroserviceHealthIndicator } from '@nestjs/terminus';
import { Transport, RedisOptions } from '@nestjs/microservices';
import { DataSource } from 'typeorm';

@Controller('health')
export class HealthController {
    private readonly logger = new Logger('health');

    constructor(
        private health: HealthCheckService,
        private db: TypeOrmHealthIndicator,
        private microservice: MicroserviceHealthIndicator,
        @InjectDataSource() private dataSource: DataSource
    ) {}

    @Get()
    @HealthCheck()
    check() {
        this.logger.verbose('Health check');
        return this.health.check([
            async () => {
                if (!this.dataSource.manager.connection.isInitialized) {
                    this.logger.warn('Re-Initializing database connection');
                    await this.dataSource.manager.connection.initialize();
                }
                await this.dataSource.query('SELECT 1');
                return this.db.pingCheck(process.env.POSTGRES_DB, {
                    timeout: 3000
                });
            },
            async () =>
                this.microservice.pingCheck < RedisOptions > ('redis', {
                    transport: Transport.REDIS,
                    options: {
                        host: process.env.REDIS_HOST,
                        port: parseInt(process.env.REDIS_PORT, 10),
                        retryAttempts: 2,
                    },
                }
            ),
        ]);
    }
}
