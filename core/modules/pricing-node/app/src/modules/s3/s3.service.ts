import type { Readable } from 'node:stream';
import { Injectable, Logger } from '@nestjs/common';
import { GetObjectCommand, PutObjectCommand, S3, GetObjectOutput } from '@aws-sdk/client-s3';
import { Prediction } from "@app/modules/predict/predict.models";

@Injectable()
export class S3Service {

    private readonly defaultPredictionsFileName = 'csgo_predictionsv2.json';

    private S3;

    private readonly logger = new Logger('S3Service');

    constructor() {
        this.S3 = new S3({
            region: 'us-east-2',
            credentials: {
                accessKeyId: process.env.S3_KEY,
                secretAccessKey: process.env.S3_SECRET,
            },
            endpoint: process.env.S3_ENDPOINT,
            forcePathStyle: !!process.env.S3_ENDPOINT,
        });
    }

    async put(fileName: string, body, predictServiceName: string = null) {

        const jsonBuffer = Buffer.from(JSON.stringify(body), 'utf8');

        const uploadParams = {
            Bucket: process.env.S3_BUCKET,
            Key: fileName,
            Body: jsonBuffer,
            ContentType: 'application/json'
        };

        const command: PutObjectCommand = new PutObjectCommand(uploadParams);
        try {
            await this.S3.send(command);
            this.logger.verbose(`File uploaded successfully: ${fileName}`);
        } catch (error) {
            this.logger.error('Error uploading file:', error);
        }

        if (process.env.PREDICT_SERVICE === predictServiceName) {
            await this.put(this.defaultPredictionsFileName, body);
        }
    }

    async get(fileName: string) : Promise<Prediction> {
        const command: GetObjectCommand = new GetObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: fileName,
        });

        const response: GetObjectOutput = await this.S3.send(command);
        const stream: Readable = response.Body as Readable;

        const buffer: Buffer = await (new Promise<Buffer>((resolve, reject): void => {
            const chunks: Buffer[] = [];
            stream.on('data', chunk => chunks.push(chunk));
            stream.once('end', () => resolve(Buffer.concat(chunks)));
            stream.once('error', reject);
        }));

        return <Prediction> JSON.parse(buffer.toString('utf-8'));
    }

    async test() {
        const params = {
            Bucket: process.env.S3_BUCKET,
        };

        this.S3.listObjects(params, (err, data) => {
            if (err) {
                return `There was an error opening the bucket: ${  err.message}`;
            }

            this.logger.verbose(data.Contents);
            return null;
        });
    }
}
