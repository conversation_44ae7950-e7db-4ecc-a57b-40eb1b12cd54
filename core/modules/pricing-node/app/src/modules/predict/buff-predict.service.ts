import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { <PERSON>ron } from "@nestjs/schedule";
import { phases } from "@app/helpers/common.helper";
import { ItemPrediction, Prediction } from "./predict.models";
import { S3Service } from "../s3/s3.service";
import { PriceCalcNew } from "../database/views/price-calc-new.view";
import { PredictService } from "./predict.service";
import { Marketplaces } from "../database/entities/marketplace/marketplace.entity";



@Injectable()
export class BuffPredictService {
    public readonly SERVICE_NAME = 'buff';

    private readonly s3FileName = 'csgo_predictions_buff.json';

    private readonly s3DebugFileName = 'csgo_predictions_buff_debug.json';

    constructor(
        @InjectRepository(PriceCalcNew)
        private readonly priceCalcNewRepository: Repository<PriceCalcNew>,
        private readonly s3Service: S3Service,
        private readonly oldPredictService: PredictService,
    ) { }

    /**
     * Saving Predictions to S3, not yet tested.
     * @Cron every hour
     */
    @Cron('0 0 * * * *')
    private async savePrices() {
        if (process.env.PREDICT_SERVICE !== this.SERVICE_NAME) {
            // ignore if this service is not enabled
            return;
        }

        await this.s3Service.put(this.s3FileName, await this.getPredictions(false), this.SERVICE_NAME);
        await this.s3Service.put(this.s3DebugFileName, await this.getPredictions(true));
    }

    /**
     * Serve Predictions to controller
     */
    async getPredictions(debug = false, cached = false): Promise<Prediction> {
        if (cached) {
            return this.s3Service.get(debug ? this.s3DebugFileName : this.s3FileName);
        }

        return {
            latest_update: new Date().getTime(),
            app_id: 730, // hardcoded
            result: await this.predict(debug)
        };
    }

    /**
     * Predict the prices
     * @returns ItemPrediction[]
     */
    private async predict(debug = false) {
        /**
         * Getting all calculated prices
         */
        const calculations = await this.priceCalcNewRepository
            .createQueryBuilder('prices')
            .getMany();

        const itemPrices = {};

        const ownCalculations = await this.oldPredictService.getPredictions(false);

        calculations.forEach(price => {
            if (!itemPrices[price.market_hash_name]) {
                itemPrices[price.market_hash_name] = [];
            }
            itemPrices[price.market_hash_name].push(price);
        });

        const outPrices: ItemPrediction[] = [];

        Object.keys(itemPrices).forEach(itemName => {
            const prices = itemPrices[itemName];

            const usedPrices = {
                buffBuy: prices.find(p => p.marketplace_key === Marketplaces.BuffBuy)?.current_price,
                buff: prices.find(p => p.marketplace_key === Marketplaces.Buff)?.current_price,
                roll: prices.find(p => p.marketplace_key === Marketplaces.CSGORoll)?.current_price,
                own: ownCalculations.result.find(p => p.market_name === itemName)?.predictions[0]?.prediction,
            };

            let liquidity = Math.min(Math.max(((usedPrices.buffBuy || 1) / (usedPrices.buff || 1)) - .13, 0.1), 1);

            if (!usedPrices.buffBuy || !usedPrices.buff) {
                liquidity = 0.1;
            }

            const { price, methods, prices: priceHistory } = this.getPrice({
                ...usedPrices,
                liquidity,
            });

            const cleanItemName = this.cleanName(itemName);

            if (!outPrices[cleanItemName]) {
                outPrices[cleanItemName] = {
                    market_name: cleanItemName,
                    predictions: [],
                };
            }

            const data = {
                phases: [itemPrices[itemName][0].paint_id].filter(i => i > 0),
                prediction: price,
                is_unreliable_price: false,
            };

            const debugData = debug ? {
                prices: usedPrices,
                method: methods,
                priceHistory,
                liquidity,
            } : {};

            const prediction = { ...data, ...debugData };

            outPrices[cleanItemName].predictions.push(prediction);
        });

        return Object.values(outPrices);
    }

    // eslint-disable-next-line class-methods-use-this
    private getWeightenedAverage(prices) {
        let total = 0;
        let totalWeight = 0;

        prices.forEach(price => {
            total += price.price * price.weight;
            totalWeight += price.weight;
        });

        return total / totalWeight;
    }

    // eslint-disable-next-line class-methods-use-this
    private getPrice({ buffBuy, buff, roll, own, liquidity }) {
        let currentPrice = buffBuy;

        const methods = [];
        const prices = [];

        if (liquidity >= 0.9) {
            methods.push('buff');
            currentPrice = buff;
            prices.push(currentPrice);
        } else if (liquidity >= 0.8) {
            methods.push('weightedAvg1');
            currentPrice = this.getWeightenedAverage([{ price: buffBuy || 1, weight: 1.3 }, { price: buff || 1, weight: 1.7 }]);
            prices.push(currentPrice);
        } else if (liquidity >= 0.7) {
            methods.push('weightedAvg2');
            currentPrice = this.getWeightenedAverage([{ price: buffBuy || 1, weight: 1.6 }, { price: buff || 1, weight: 1.4 }]);
            prices.push(currentPrice);
        } else if (liquidity >= 0.6) {
            if (own && roll && own < roll) {
                methods.push('ownCalculation');
                currentPrice = own;
                prices.push(currentPrice);
            } else {
                methods.push('roll -2%');
                currentPrice = roll * 0.98;
                prices.push(currentPrice);
            }
        } else {
            const calcPrices = [buff, roll];
            if (own !== buffBuy && own > buffBuy) {
                calcPrices.push(own);
            }
            methods.push('Min');
            currentPrice = Math.round(this.getMinPrice(calcPrices));
            prices.push(currentPrice);
        }

        if (roll > 10000 * 100 && liquidity === 0.1) {
            methods.push('roll base high tier');
            currentPrice = roll;
            prices.push(currentPrice);
        }

        let price = Math.round(currentPrice * this.getPriceModifier(currentPrice));

        // calculate if buffBuy and price is 2% or more apart
        if (buffBuy && price && ((buffBuy - price) / buffBuy > 0.05)) {
            price = Math.round(buffBuy * 0.95);
            methods.push('buffBuyOverride');
            prices.push(price);
        }


        return { price, methods, prices };
    }

    // eslint-disable-next-line class-methods-use-this
    private getMinPrice(prices) {
        return Math.min(...prices.filter(i => i && i > 0 && !Number.isNaN(i)));
    }

    // eslint-disable-next-line class-methods-use-this
    private getPriceModifier(price) {
        if (price >= 250000) { // $2500
            return 0.95;
        }
        if (price >= 200000) {
            return 0.955;
        }
        if (price >= 150000) {
            return 0.96;
        }
        if (price >= 100000) {
            return 0.9625;
        }
        if (price >= 50000) {
            return 0.9650;
        }
        if (price >= 10000) {
            return 0.9675;
        }
        if (price >= 5000) {
            return 0.97;
        }
        return 0.975;
    }

    // eslint-disable-next-line class-methods-use-this
    private cleanName(name: string) {
        const phase = phases.find(p => name.includes(p));
        let newName = phase ? name.replace(` - ${phase}`, '') : name;

        // Some marketplace uses bad names for specific capsules.
        newName = newName.replace('Holo-Foil', 'Holo/Foil');

        return newName;
    }

}
