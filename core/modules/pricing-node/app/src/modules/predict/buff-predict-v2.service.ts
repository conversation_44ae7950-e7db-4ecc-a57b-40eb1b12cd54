
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { <PERSON>ron } from "@nestjs/schedule";
import { phases, toBoolean } from "@app/helpers/common.helper";
import { ItemPrediction, Prediction } from "./predict.models";
import { S3Service } from "../s3/s3.service";
import { PriceCalcNew } from "../database/views/price-calc-new.view";
import { PredictService } from "./predict.service";
import { Marketplaces, primaryMarketPlaces } from "../database/entities/marketplace/marketplace.entity";



@Injectable()
export class BuffPredictV2Service {
    public readonly SERVICE_NAME = 'buffv2';

    private readonly s3FileName = 'csgo_predictions_buff_v2.json';

    private readonly s3DebugFileName = 'csgo_predictions_buff_v2_debug.json';

    private readonly steamModifier = 0.7;

    private readonly rollModifier = 0.8;

    private readonly buffModifier = 0.96;

    private readonly ownModifier = 0.9;

    private readonly buffModifierUnder60 = 0.6;

    private readonly steamBuyOrderThreshold = 1750 * 100;

    constructor(
        @InjectRepository(PriceCalcNew)
        private readonly priceCalcNewRepository: Repository<PriceCalcNew>,
        private readonly s3Service: S3Service,
        private readonly oldPredictService: PredictService,
    ) { }

    /**
     * Saving Predictions to S3, not yet tested.
     * @Cron every hour
     */
    @Cron('0 0 * * * *')
    private async savePrices() {
        if (process.env.PREDICT_SERVICE !== this.SERVICE_NAME) {
            // ignore if this service is not enabled
            return;
        }

        await this.s3Service.put(this.s3FileName, await this.getPredictions(false), this.SERVICE_NAME);
        await this.s3Service.put(this.s3DebugFileName, await this.getPredictions(true));
    }

    /**
     * Serve Predictions to controller
     */
    async getPredictions(debug = false, cached = false): Promise<Prediction> {
        if (cached) {
            return this.s3Service.get(debug ? this.s3DebugFileName : this.s3FileName);
        }

        return {
            latest_update: new Date().getTime(),
            app_id: 730, // hardcoded
            result: await this.predict(debug),
            version: 'v3',
        };
    }

    /**
     * Predict the prices
     * @returns ItemPrediction[]
     */
    private async predict(debug = false) {
        /**
         * Getting all calculated prices
         */
        const calculations = await this.priceCalcNewRepository
            .createQueryBuilder('prices')
            .where('marketplace_key IN(:...primaryMarketPlaces)', { 'primaryMarketPlaces': primaryMarketPlaces })
            .getMany();

        const itemPrices = {};

        const ownCalculations = await this.oldPredictService.getPredictions(false);

        calculations.forEach(price => {
            if (!itemPrices[price.market_hash_name]) {
                itemPrices[price.market_hash_name] = [];
            }
            itemPrices[price.market_hash_name].push(price);
        });

        const outPrices: ItemPrediction[] = [];

        Object.keys(itemPrices).forEach(itemName => {
            const prices = itemPrices[itemName];

            const usedPrices = {
                buffBuy: prices.find(p => p.marketplace_key === Marketplaces.BuffBuy)?.current_price,
                buff: prices.find(p => p.marketplace_key === Marketplaces.Buff)?.current_price,
                roll: prices.find(p => p.marketplace_key === Marketplaces.CSGORoll)?.current_price,
                steam: prices.find(p => p.marketplace_key === Marketplaces.SteamCommunityMarket)?.current_price,
                steamBuy: prices.find(p => p.marketplace_key === Marketplaces.SteamCommunityMarketBuy)?.current_price,
                own: ownCalculations.result.find(p => p.market_name === itemName)?.predictions[0]?.prediction,
            };

            let liquidity = Math.min(Math.max(((usedPrices.buffBuy || 1) / (usedPrices.buff || 1)) - .13, 0.1), 1);

            if (!usedPrices.buffBuy || !usedPrices.buff) {
                liquidity = 0.1;
            }

            const { price, methods } = this.getPrice({
                ...usedPrices,
                liquidity,
            });

            const cleanItemName = this.cleanName(itemName);

            if (!outPrices[cleanItemName]) {
                outPrices[cleanItemName] = {
                    market_name: cleanItemName,
                    predictions: [],
                };
            }

            const data = {
                phases: [itemPrices[itemName][0].paint_id].filter(i => i > 0),
                prediction: price,
                is_unreliable_price: false,
            };

            const debugData = debug ? {
                prices: usedPrices,
                method: methods,
                liquidity,
            } : {};

            const prediction = { ...data, ...debugData };

            outPrices[cleanItemName].predictions.push(prediction);
        });

        return Object.values(outPrices);
    }

    // eslint-disable-next-line class-methods-use-this
    private getPrice({ buffBuy, buff, roll, steam, steamBuy, own, liquidity }) {

        if ((liquidity >= .7 && toBoolean(process.env.DEFENSE_MODE)) || liquidity >= .6) {
            const minPrices = [];

            if (buffBuy) {
                minPrices.push(Math.round(buffBuy * this.buffModifier));
            }
            if (own) {
                minPrices.push(own);
            }

            return { price: Math.min(...minPrices), methods: ['liqudity >= 0.7 min BuffBuy 0.96 OR own'] };
        }

        if (liquidity >= .6 && toBoolean(process.env.DEFENSE_MODE)) {
            return { price: Math.round((buffBuy + buff) / 2 * this.buffModifier), methods: ['liquidity >= 60 Math.round((buffBuy + buff) / 2 * this.buffModifierUnder60)'] };
        }
        // check if roll have a price for this item
        if (roll && roll > 1500000) { // $15000
            return { price: Math.round(roll * this.rollModifier), methods: ['liquidity < 60 Roll'] };
        }

        // check if the gap between buff and buffbuy is lower than 20%
        if (buffBuy && buff && buffBuy / buff >= 0.8) {
            return { price: Math.round(buffBuy * this.buffModifier), methods: ['BuffBuy < 80% Buff'] };
        }

        if (steamBuy > this.steamBuyOrderThreshold && buffBuy > steamBuy && buff > steamBuy) {
            return { price: Math.round(buff * this.buffModifierUnder60), methods: ['No roll price', 'No Buff price', 'SteamBuy > 1750'] };
        }

        if (steam) {
            return { price: Math.round(steam * this.steamModifier), methods: ['No roll price', 'liquidity < 60 Steam'] };
        }

        if (steamBuy) {
            return { price: Math.round(steamBuy * this.steamModifier), methods: ['No roll price', 'No Steam listing', 'liquidity < 60 SteamBuy'] };
        }

        if (own) {
            return { price: Math.round(own * this.ownModifier), methods: ['No roll', 'No Steam listing', 'No Steam Buyorder', 'liquidity < 60 Own'] };
        }

        // failover to buff buy
        return { price: Math.round(buffBuy && buffBuy > 0 ? buffBuy * this.buffModifier : own * this.ownModifier), methods: ['liquidity < 60 BuffBuy Failover'] };
    }

    // eslint-disable-next-line class-methods-use-this
    private cleanName(name: string) {
        const phase = phases.find(p => name.includes(p));
        let newName = phase ? name.replace(` - ${phase}`, '') : name;

        // Some marketplace uses bad names for specific capsules.
        newName = newName.replace('Holo-Foil', 'Holo/Foil');

        return newName;
    }

}
