import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { <PERSON>ron } from "@nestjs/schedule";
import { phases } from "@app/helpers/common.helper";
import { Marketplace } from "../database/entities/marketplace/marketplace.entity";
import { Modifier, ModifierDir } from "../database/entities/modifier/modifier.entity";
import { PriceCalcNew } from "../database/views/price-calc-new.view";
import { ItemPrediction, Prediction } from "./predict.models";
import { S3Service } from "../s3/s3.service";



@Injectable()
export class PredictService {

    public readonly SERVICE_NAME = 'default';

    public readonly s3DefaultPredictionsFileName = 'csgo_predictionsv2.json';

    private readonly maxThreshold = 15;

    private readonly s3FileName = 'csgo_predictionsv2_custom.json';

    private readonly s3DebugFileName = 'csgo_predictionsv2_custom_debug.json';

    constructor(
        @InjectRepository(PriceCalcNew)
        private readonly priceCalcRepository: Repository<PriceCalcNew>,
        @InjectRepository(Modifier)
        private readonly modifierRepository: Repository<Modifier>,
        @InjectRepository(Marketplace)
        private readonly marketplaceRepository: Repository<Marketplace>,
        private readonly s3Service: S3Service,
    ) { }

    /**
     * Saving Predictions to S3
     * @Cron every hour
     */
    @Cron('0 0 * * * *')
    private async savePrices() {
        if (process.env.PREDICT_SERVICE !== this.SERVICE_NAME) {
            // ignore if this service is not enabled
            return;
        }

        await this.s3Service.put(this.s3FileName, await this.getPredictions(false), this.SERVICE_NAME);
        await this.s3Service.put(this.s3DebugFileName, await this.getPredictions(true));
    }

    /**
     * Serve Predictions to controller
     */
    async getPredictions(debug = false, cached = false): Promise<Prediction> {
        if (cached) {
            return this.s3Service.get(debug ? this.s3DebugFileName : this.s3FileName);
        }

        return {
            latest_update: new Date().getTime(),
            app_id: 730, // hardcoded
            result: await this.predict(debug)
        };
    }

    /**
     * Predict the prices
     * @returns ItemPrediction[]
     */
    private async predict(debug = false): Promise<ItemPrediction[]> {
        /**
         * Getting all calculated prices
         */
        const calculations = await this.priceCalcRepository
            .createQueryBuilder('prices')
            .getMany();

        /**
         * Aggregate prices
         */

        const itemListingPrices = {};
        const itemBuyOrderPrices = {};

        calculations.forEach(price => {
            /**
             * Separate buy order prices from listing prices
             */
            if (
                price.marketplace_key.includes('_buy') ||
                price.marketplace_key.includes('_sale')
            ) {
                if (!itemBuyOrderPrices[price.market_hash_name]) {
                    itemBuyOrderPrices[price.market_hash_name] = [];
                }
                itemBuyOrderPrices[price.market_hash_name].push(price);
            } else {
                if (!itemListingPrices[price.market_hash_name]) {
                    itemListingPrices[price.market_hash_name] = [];
                }
                itemListingPrices[price.market_hash_name].push(price);
            }
        });

        const outPrices = await this.getPrices(itemListingPrices, itemBuyOrderPrices);

        return Object.keys(outPrices).map(itemName => ({
            ...outPrices[itemName],
            predictions: outPrices[itemName].predictions.map(prediction => ({
                ...prediction,
                prices: debug ? prediction.prices : undefined,
            }))
        }));
    }

    private async getPrices(itemListingPrices, itemBuyOrderPrices) {
        const outPrices = {};

        /**
         * Loading marketplaces
         */
        const marketplaces = await this.marketplaceRepository.find();

        /**
         * Getting all price modifiers from db
         */
        const modifiers = await this.modifierRepository.find({
            where: {
                status: true
            }
        });

        Object.keys(itemListingPrices).forEach(itemName => {
            const prices = itemListingPrices[itemName].reduce((result, itemPrice) => {
                const { price, appliedModifiers } = this.applyModifier(itemPrice, modifiers);

                let weightModifier = 0;
                if (itemName.includes('Doppler (') && itemPrice.marketplace_key.includes('buff')) {
                    weightModifier = 70;
                }

                result.push({
                    marketplace_key: itemPrice.marketplace_key,
                    // +1 to avoid 0 weight and weightModifier to make doppler prices more important than others (buff)
                    weight: marketplaces.find(marketplace => marketplace.key === itemPrice.marketplace_key).weight + itemPrice.count + 1 + weightModifier,
                    applied_modifiers: appliedModifiers,
                    price,
                    original_price: itemPrice.current_price,
                });

                return result;

            }, []).filter(price => price.price > 0);

            // If there is only one or two marketplace price, we will use the buy order price and mark the price as unreliable
            const unreliable = prices.length <= 2 && !itemName.includes('Doppler');

            /**
             * Failover, we will use buy order prices
             */

            // we will use buy order price on some circumstances
            // 1. if there is no listing prices
            // 3. if the item is not doppler
            // 4. if there is buy order prices
            if (
                unreliable &&
                itemBuyOrderPrices[itemName]
            ) {
                const highestFailover = Math.max(...itemBuyOrderPrices[itemName].map(price => price.current_price).filter(price => price !== 0));

                if (!highestFailover || highestFailover === -Infinity) {
                    prices.push({
                        marketplaceKey: 'not-found',
                        weight: 0.1,
                        price: 0,
                    });
                    return;
                }

                const itemPrice = itemBuyOrderPrices[itemName].find(price => price.current_price === highestFailover);

                const { price, appliedModifiers } = this.applyModifier(itemPrice, modifiers);

                prices.push({
                    marketplace_key: itemPrice.marketplace_key,
                    weight: marketplaces.find(marketplace => marketplace.key === itemPrice.marketplace_key).weight + itemPrice.count,
                    price,
                    applied_modifiers: appliedModifiers,
                });
            }

            const cleanItemName = this.cleanName(itemName);

            if (!outPrices[cleanItemName]) {
                outPrices[cleanItemName] = {
                    market_name: cleanItemName,
                    predictions: [],
                };
            }

            outPrices[cleanItemName].predictions.push({
                phases: [itemListingPrices[itemName][0].paint_id].filter(i => i > 0),
                prediction: this.selectPrice(prices),
                is_unreliable_price: unreliable,
                prices,
            });
        });

        return outPrices;
    }

    // eslint-disable-next-line class-methods-use-this
    private cleanName(name: string) {

        const phase = phases.find(p => name.includes(p));
        let newName = phase ? name.replace(` - ${phase}`, '') : name;

        // Some marketplace uses bad names for specific capsules.
        newName = newName.replace('Holo-Foil', 'Holo/Foil');

        return newName;
    }

    private selectPrice(rawPrices: { price: number, weight: number }[]) {
        const prices = rawPrices.sort((a, b) => a.price - b.price);

        if (prices.length === 0) {
            return false;
        }

        // Find the lowest price
        const basePrice = Math.min(...prices.map(price => price.price).filter(price => price !== 0));

        const filteredPrices = [];
        const filteredWeights = [];

        prices.forEach(({ price, weight }) => {
            if (price === basePrice) {
                filteredPrices.push(price);
                filteredWeights.push(weight);
                return;
            }

            // Calculate the percent difference between the current price and the base price
            const percentDiff = Math.abs(price - basePrice) / basePrice * 100;

            if (percentDiff > this.maxThreshold) {
                // If the current price is more than X% away from the base price, exclude it
                return;
            }

            // Otherwise, include it in the filtered list along with its corresponding weight
            filteredPrices.push(price);
            filteredWeights.push(weight);
        });

        const weightedSum = filteredPrices.reduce((acc, price, i) => acc + price * filteredWeights[i], 0);
        const sumOfWeights = filteredWeights.reduce((acc, weight) => acc + weight, 0);

        return Math.round(weightedSum / sumOfWeights);
    }

    /**
     * Apply price modifiers
     * @param priceCalc PriceCalc
     * @param modifiers Modifier[]
     * @returns number
     */
    private applyModifier(priceCalc: PriceCalcNew, modifiers: Modifier[]): { price: number, appliedModifiers: Modifier[] } {
        let pred = priceCalc.current_price;

        const appliedModifiers = [];
        modifiers.forEach((modifier) => {
            const prePred = pred;

            pred = this.doModify(modifier, priceCalc, pred);

            if (prePred !== pred) {
                appliedModifiers.push(modifier.name);
            }
        });

        return {
            price: Math.round(pred),
            appliedModifiers
        };
    }

    /**
     * Modify the price by modifiers
     * @param field string
     * @param modifier Modifier
     * @param priceCalc PriceCalc
     * @param pred number
     * @returns number
     */
    // eslint-disable-next-line class-methods-use-this
    private doModify(modifier: Modifier, priceCalc: PriceCalcNew, pred: number) {
        const field = modifier.type;

        // eslint-disable-next-line default-case
        switch (modifier.dir) {
            case ModifierDir.GreaterThan:
                if (priceCalc[field] > parseInt(modifier.rule, 10)) {
                    return pred * modifier.modifier;
                }
                break;
            case ModifierDir.LowerThan:
                if (priceCalc[field] < parseInt(modifier.rule, 10)) {
                    return pred * modifier.modifier;
                }
                break;
            case ModifierDir.GreaterThanOrEqual:
                if (priceCalc[field] >= parseInt(modifier.rule, 10)) {
                    return pred * modifier.modifier;
                }
                break;
            case ModifierDir.LowerThanOrEqual:
                if (priceCalc[field] <= parseInt(modifier.rule, 10)) {
                    return pred * modifier.modifier;
                }
                break;
            case ModifierDir.Include:
                if (priceCalc[field].includes(modifier.rule)) {
                    return pred * modifier.modifier;
                }
                break;
            case ModifierDir.NotInclude:
                if (!priceCalc[field].includes(modifier.rule)) {
                    return pred * modifier.modifier;
                }
                break;
            case ModifierDir.Equal:
                if (priceCalc[field] === modifier.rule) {
                    return pred * modifier.modifier;
                }
                break;

            case ModifierDir.Between:
                if (priceCalc[field] >= parseFloat(modifier.rule) && priceCalc[field] <= parseFloat(modifier.rule2)) {
                    return pred * modifier.modifier;
                }
                break;
        }

        return pred;
    }

}
