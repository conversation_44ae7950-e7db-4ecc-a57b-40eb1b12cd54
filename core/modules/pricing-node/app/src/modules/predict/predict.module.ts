import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpInterceptModule } from '../http-intercept/http-intercept.module';
import { CurrenciesModule } from '../currencies/currencies.module';
import { Game } from '../database/entities/game/game.entity';
import { ItemId } from '../database/entities/item/item-id.entity';
import { ItemPrice } from '../database/entities/item/item-price.entity';
import { Item } from '../database/entities/item/item.entity';
import { Marketplace } from '../database/entities/marketplace/marketplace.entity';
import { Modifier } from '../database/entities/modifier/modifier.entity';
import { PredictService } from './predict.service';
import { S3Module } from '../s3/s3.module';
import { BuffPredictService } from './buff-predict.service';
import { BuffPredictV2Service } from './buff-predict-v2.service';
import { PriceCalcNew } from '../database/views/price-calc-new.view';

@Module({
    imports: [
        ScheduleModule.forRoot(),
        TypeOrmModule.forFeature([
            ItemPrice,
            Item,
            ItemId,
            Game,
            Marketplace,
            Modifier,
            PriceCalcNew,
        ]),
        HttpModule,
        CurrenciesModule,
        HttpInterceptModule,
        S3Module,
    ],
    providers: [
        PredictService,
        BuffPredictService,
        BuffPredictV2Service,
    ],
    exports: [
        PredictService,
        BuffPredictService,
        BuffPredictV2Service,
    ]
})
export class PredictModule { }
