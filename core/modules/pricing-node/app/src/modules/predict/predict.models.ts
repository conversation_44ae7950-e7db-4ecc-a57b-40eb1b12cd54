export interface Calc {
    prices_id: number;
    prices_current_price: number;
    prices_min_price: number;
    prices_max_price: number;
    prices_avg_price: number;
    prices_lag_price: number;
    prices_count: number;
    prices_item_id: number;
    prices_marketplace_key: string;
    prices_created_at: Date;
    prices_rsi: number;
    prices_liquidity: number;
    item_market_hash_name: string;
}

export interface Price {
    marketplace_key: string;
    weight: number;
    applied_modifiers: string[];
    price: number;
    original_price: number;
}

export interface PricePrediction {
    phases: number[];
    prediction: number;
    marketplaceKey: string;
    is_unreliable_price: boolean;
    prices: Price[];
}

export interface ItemPrediction {
    market_name: string;
    predictions: PricePrediction[];
}

export interface Prediction {
    latest_update: number;
    app_id: number;
    result: ItemPrediction[];
    version?: string;
}
