import { Lo<PERSON>, <PERSON><PERSON><PERSON>, OnApplicationBootstrap } from '@nestjs/common';
import { HttpService, HttpModule as BaseHttpModule } from '@nestjs/axios';
import * as httpsProxyAgent from 'https-proxy-agent';
import { InternalAxiosRequestConfig } from 'axios';
import { firstValueFrom } from 'rxjs';

const PROXY_POOL = process.env.PROXY_POOL || 'pricing';

@Module({
    imports: [BaseHttpModule],
    exports: [BaseHttpModule],
})
export class HttpInterceptModule implements OnApplicationBootstrap {
    private readonly logger = new Logger(HttpInterceptModule.name);

    private proxiedDomains = [
        'buff.163.com',
        'buff.market',
        'steamcommunity.com/market',
        // 'query1.finance.yahoo.com'
        // 'api.steamapis.com',
        // 'market.csgo.com',
    ];

    private cachedProxies: { [key: string]: string; } = {};

    constructor(private readonly httpService: HttpService) { }

    public async onApplicationBootstrap(): Promise<void> {
        const axios = this.httpService.axiosRef;

        axios.interceptors.request.use((config: InternalAxiosRequestConfig) => ({
            ...config,
            metadata: {
                startDate: new Date(),
            },
        }));

        axios.interceptors.response.use(
            (response) => {
                const { config } = response;

                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const { metadata } = config as any;

                const duration = new Date().getTime() - metadata.startDate.getTime();

                this.logger.verbose(`${response.config.method.toUpperCase()} ${response.config.url} ${duration}ms`);

                return response;
            },
            async (error) => {
                const newConfig = {
                    ...error.config,
                    retries: error.config.retries || {
                        count: 0,
                    },
                };

                newConfig.retries.count += 1;

                if (newConfig.retries.count > 5) {
                    this.logger.verbose('Too many retries, give up');
                    return Promise.reject(error);
                }

                // this.logger.error(`Failed to fetch ${error.config.url} - ${error.message}`); remove logging due to service not being used currently.

                if (error.response && error.response.status === 404 && error.config.url.includes(process.env.INTERNAL_API_HOST)) {
                    this.logger.error(`Error message: ${error.response.data.message}`);
                    await HttpInterceptModule.delay(30000); // Delay for 30 seconds and try again
                }

                // Check if the request was made using a proxy
                if (error.config.httpsAgent && error.config.metadata?.retries < 3) {
                    this.logger.verbose('---> Setting Bad Proxy, Get new one');
                    const oldProxyUrl = `${error.config.httpsAgent.proxy.host}:${error.config.httpsAgent.proxy.port}`;
                    this.logger.verbose(`Old proxy failed: ${oldProxyUrl}`);

                    const newProxy = await this.setBadProxy(oldProxyUrl, error.config.url);

                    this.logger.verbose(`Using new proxy: ${newProxy}`);
                    const httpsAgent = httpsProxyAgent(`http://${newProxy}`);
                    newConfig.httpsAgent = httpsAgent;
                }

                return axios(newConfig);
            },
        );

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        axios.interceptors.request.use(async (config: any) => {
            this.logger.verbose(`${config.method.toUpperCase()} ${config.url}`);

            if (this.proxiedDomains.some((domain) => config.url.includes(domain))) {
                const proxyUrl = await this.getNewProxy(config.url);
                this.logger.verbose(`Using new proxy: ${proxyUrl}`);

                const httpsAgent = httpsProxyAgent(`http://${proxyUrl}`);

                return {
                    ...config,
                    httpsAgent,
                    timeout: 15000,
                    headers: {
                        ...config.headers,
                        'User-Agent':
                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/111.0.1661.44',
                    },
                };
            }

            return config;
        });
    }

    private async getNewProxy(urlForProxy: string): Promise<string> {
        this.logger.verbose('--> getNewProxy called');

        if (this.cachedProxies[urlForProxy]) {
            this.logger.verbose(`--> using cached proxy: ${this.cachedProxies[urlForProxy]}`);
            return this.cachedProxies[urlForProxy];
        }

        const proxy = await this.proxyApi(null);

        if (!proxy) {
            return '';
        }

        this.cachedProxies[urlForProxy] = proxy;
        return this.cachedProxies[urlForProxy];
    }

    private async setBadProxy(oldProxy: string, urlForProxy: string): Promise<string> {
        this.logger.verbose('--> setBadProxy called');

        const proxy = await this.proxyApi(oldProxy);

        if (!proxy) {
            return '';
        }

        this.cachedProxies[urlForProxy] = proxy;
        return this.cachedProxies[urlForProxy];
    }

    private static delay(ms: number): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(resolve, ms);
        });
    }

    private async proxyApi(oldProxy: null|string): Promise<string> {
        const method = oldProxy ? 'bad' : 'get';
        const path = `/api/v2/internal/proxy/${method}`;
        let url = `http://${process.env.INTERNAL_API_HOST}${path}?pool=${PROXY_POOL}`;

        if (oldProxy) {
            url += `proxy=${oldProxy}&getnew=true`;
        }

        const response = await firstValueFrom(
            this.httpService.post(url, {}, {
                headers: {
                    'x-empire-socket-api-token': process.env.INTERNAL_API_TOKEN,
                    'x-empire-socket-user-id': 'general',
                }
            }),
        );

        if (!response.data.success || !response.data.data.proxy) {
            this.logger.error('Failed to fetch new proxy');
        }

        return response.data.data.proxy;
    }
}
