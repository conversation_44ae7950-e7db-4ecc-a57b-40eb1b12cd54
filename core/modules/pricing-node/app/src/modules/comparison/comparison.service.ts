import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';
import { PredictService } from '../predict/predict.service';

/**
 * -------------------------------------------------------
 * This service is used to compare the old and new predictions
 * and return the difference in percentages
 * Before it went live.
 * It can be removed after a while
 * -------------------------------------------------------
 */

@Injectable()
export class ComparisonService {
    private liveEndpoint = 'https://csgoempire.tv/api/v2/trading/pricing';

    constructor(
        private readonly httpService: HttpService,
        private readonly predictService: PredictService,
    ) { }

    async compare() {

        const livePredictions = (await lastValueFrom(this.httpService.get(this.liveEndpoint))).data;

        // for local testing only!
        // const predictions = (await lastValueFrom(this.httpService.get('https://csgoempire-pricig-node-prod.s3.us-east-2.amazonaws.com/csgo_predictionsv2_debug.json'))).data;

        const predictions = await this.predictService.getPredictions();

        const prices = {};

        livePredictions.result.forEach(item => {
            item.predictions.forEach(prediction => {

                if (!prices[item.market_name]) {
                    prices[item.market_name] = {};
                }

                if (prediction.phases.length > 0) {
                    prediction.phases.forEach(phase => {
                        prices[item.market_name][`old_${phase}`] = Math.round(prediction.prediction * (1 / 1.14));
                    });
                } else {
                    prices[item.market_name].old = Math.round(prediction.prediction * (1 / 1.14));
                }
            });
        });

        predictions.result.forEach(item => {
            item.predictions.forEach(prediction => {

                if (!prices[item.market_name]) {
                    prices[item.market_name] = {};
                }

                prices[item.market_name].percentageDiff = -100;

                const usdPrediction = Math.round(prediction.prediction * (1 / 1.14));
                if (prediction.phases.length > 1) {
                    prediction.phases.forEach(phase => {
                        prices[item.market_name][`new_${phase}`] = usdPrediction;
                        prices[item.market_name].marketplace = prediction.marketplaceKey;
                        prices[item.market_name].percentageDiff = ((usdPrediction - prices[item.market_name][`old_${phase}`]) / prices[item.market_name][`old_${phase}`]) * 100;
                    });
                } else {
                    prices[item.market_name].new = usdPrediction;
                    prices[item.market_name].marketplace = prediction.marketplaceKey;
                    prices[item.market_name].percentageDiff = ((usdPrediction - prices[item.market_name].old) / prices[item.market_name].old) * 100;

                }

            });
        });

        const data = Object.keys(prices).map(itemName => ({
            itemName,
            ...prices[itemName]
        }));


        const diffs = data.filter(a => a.percentageDiff).sort((a, b) => a.percentageDiff - b.percentageDiff);

        return {
            diffs,
            missing_items: data.filter(a => !a.new).map(a => a.itemName),
            new_items: data.filter(a => !a.old).map(a => a.itemName),
        };

    }

}
