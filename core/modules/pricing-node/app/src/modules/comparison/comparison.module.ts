import { Module } from '@nestjs/common';
import { ComparisonService } from './comparison.service';
import { PredictModule } from '../predict/predict.module';
import { HttpInterceptModule } from '../http-intercept/http-intercept.module';
import { ComparisonV2Service } from './comparison-v2.service';

@Module({
    imports: [
        PredictModule,
        HttpInterceptModule,
    ],
    providers: [ComparisonService, ComparisonV2Service],
    exports: [ComparisonService, ComparisonV2Service],
})
export class ComparisonModule { }
