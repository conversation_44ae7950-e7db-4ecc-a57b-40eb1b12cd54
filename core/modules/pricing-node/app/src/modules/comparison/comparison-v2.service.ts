import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';
import { calculatePercentageDifference, dopplerPhases } from '@app/helpers/common.helper';
import { BuffPredictService } from '../predict/buff-predict.service';

/**
 * -------------------------------------------------------
 * This service is used to compare the old and new predictions
 * and return the difference in percentages
 * Before it went live.
 * It can be removed after a while
 * -------------------------------------------------------
 */

@Injectable()
export class ComparisonV2Service {
    private liveEndpoint = 'https://csgoempire-pricig-node-prod.s3.us-east-2.amazonaws.com/csgo_predictionsv2_debug.json';

    constructor(
        private readonly httpService: HttpService,
        private readonly buffPredictService: BuffPredictService,
    ) { }

    async compare(csv = false) {

        const livePredictions = (await lastValueFrom(this.httpService.get(this.liveEndpoint))).data;

        const predictions = await this.buffPredictService.getPredictions(true);

        const prices = {};

        predictions.result.forEach((value) => {
            value.predictions.forEach((prediction) => {
                const marketName = this.getMarketName(value.market_name, prediction.phases[0]);
                prices[marketName] = {
                    new: prediction.prediction,
                };
            });
        });

        livePredictions.result.forEach((value) => {
            value.predictions.forEach((prediction) => {
                const marketName = this.getMarketName(value.market_name, prediction.phases[0]);

                if (!prices[marketName] || !prices[marketName].new) {
                    // console.log('no new price', market_name, prediction, prices[market_name]);
                    return;
                }

                const pred = prediction.prediction;

                const buffPrice = prediction.prices.find(p => p.marketplace_key === 'buff')?.price || 0;
                const buffBuyPrice = prediction.prices.find(p => p.marketplace_key === 'buff_buy')?.price || 0;

                prices[marketName] = {
                    ...prices[marketName],
                    old: pred, // 14% bonus, probably wrong calculation
                    difference: calculatePercentageDifference(pred, prices[marketName].new),
                    buff: buffPrice,
                    buffbuy: buffBuyPrice,
                    buff_difference: calculatePercentageDifference(pred, buffPrice),
                    buffbuy_difference: calculatePercentageDifference(pred, buffBuyPrice),
                };
            });
        });

        // order by difference
        const orderedPrices = Object.keys(prices).sort((a, b) => prices[b].difference - prices[a].difference);

        const data = orderedPrices.map((key) => ({
            name: key,
            ...prices[key],
        }));

        if (!csv) {
            return data;
        }

        // eslint-disable-line prefer-template
        return this.generateCSV(data);
    }

    // eslint-disable-next-line class-methods-use-this
    private generateCSV(data) {
        let csv = '';

        csv += `${Object.keys(data[0]).join(',')} \n`;
        csv += data.map((item) => [
            item.name,
            item.new,
            item.old,
            item.difference,
            item.buff,
            item.buffbuy,
            item.buff_difference,
            item.buffbuy_difference,
        ].join(',')).join('\n');

        return csv;
    }

    // eslint-disable-next-line class-methods-use-this
    private getMarketName(name: string, phase: number) {
        let marketName = name;
        const phaseText = dopplerPhases[phase];

        if (phaseText) {
            marketName = `${marketName} - ${phaseText}`;
        }

        return marketName;
    }
}
