import { Body, Controller, Get, Header, HttpException, ParseBoolPipe, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { toBoolean } from '@app/helpers/common.helper';
import { AuthGuard } from '../auth/auth.guard';
import { ApiService } from './api.service';
import { ComparisonService } from '../comparison/comparison.service';
import { ComparisonV2Service } from '../comparison/comparison-v2.service';

@Controller('/v1/')
@UseGuards(AuthGuard)
export class V1Controller {

    constructor(
        private readonly apiService: ApiService,
        private readonly comparisonService: ComparisonService,
        private readonly comparisonV2Service: ComparisonV2Service
    ) { }

    @Get('predictions')
    async getPredictions(@Query('debug') debug): Promise<object> {
        return this.apiService.getActivePredictService().getPredictions(toBoolean(debug));
    }

    @Get('predictions/cached')
    async getPredictionsCached(@Query('debug') debug): Promise<object> {
        return this.apiService.getActivePredictService().getPredictions(toBoolean(debug), true);
    }

    @Get('cookies')
    getCookies(
        @Query('type') type,
    ) {
        return this.apiService.getCookies(type);
    }

    @Post('cookies/add')
    async addCookies(
        @Body('type') type,
        @Body('value') value
    ) {
        return this.apiService.addCookie(type, value);
    }

    @Get('marketplaces')
    getMarketplaces() {
        return this.apiService.getMarketplaces();
    }

    @Patch('marketplaces/status')
    setMarketplaceStatus(
        @Body('marketplace') marketplace,
        @Body('status', ParseBoolPipe) status,
    ) {
        if (!marketplace) {
            throw new HttpException('Please provide a marketplace key.', 500);
        }

        if (!status) {
            throw new HttpException(`Please Provide a status`, 500);
        }

        return this.apiService.setMarketplaceStatus(marketplace, status);
    }

    @Get('comparison')
    getCompare() {
        return this.comparisonService.compare();
    }

    @Get('comparison/v2/csv')
    @Header('Content-Type', 'application/csv')
    @Header(
        `Content-Disposition`,
        `attachment; filename="compare-v2.csv`
    )
    async getCompareV2Csv() {
        return this.comparisonV2Service.compare(true);
    }

    @Get('comparison/v2/json')
    @Header('Content-Type', 'application/json')
    @Header(
        `Content-Disposition`,
        `attachment; filename="compare-v2.json`
    )
    async getCompareV2Json() {
        return this.comparisonV2Service.compare(true);
    }

}
