import { HttpException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { MoreThanOrEqual, Repository } from "typeorm";
import { PriceCalcNew } from "../database/views/price-calc-new.view";
import { Item } from "../database/entities/item/item.entity";
import { PriceHistory } from "../database/views/price-history.view";
import { MakretplaceItem } from "./public.models";

@Injectable()
export class PublicService {

    constructor(
        @InjectRepository(Item)
        private readonly itemRepository: Repository<Item>,
        @InjectRepository(PriceHistory)
        private readonly priceHistoryRepository: Repository<PriceHistory>,
        @InjectRepository(PriceCalcNew)
        private readonly priceCalcView: Repository<PriceCalcNew>,
    ) {
    }

    /**
     * Get Price history for an item by Market Hash Name
     * @param marketHashName string
     * @param days
     * @returns Object
     */
    async getHistory(marketHashName: string, days: number) {
        const item = await this.itemRepository.findOne({
            where: {
                market_hash_name: marketHashName
            }
        });

        if (!item) {
            throw new HttpException('Item Not found', 404);
        }

        const markets = await this.priceCalcView.find({
            select: [
                'current_price',
                'min_price',
                'max_price',
                'avg_price',
                'count',
                'marketplace_key',
            ],
            where: {
                market_hash_name: marketHashName
            }
        });

        let marketsByKey = markets.reduce((obj, market) => ({
            ...obj,
            [market.marketplace_key]: {
                ...market,
                history: [],
            },
        }), {});

        // return history only if days > 1
        if (days > 1) {
            const todayMinusN = new Date();
            todayMinusN.setDate(todayMinusN.getDate() - days);

            const history = await this.priceHistoryRepository
                .createQueryBuilder('price_history')
                .select([
                    'marketplace_key',
                    'close_price',
                    'avg_price',
                    'min_price',
                    'max_price',
                    'date',
                    'count'
                ])
                .where('item_id = :itemId', { itemId: item.id })
                .andWhere({
                    date: MoreThanOrEqual(todayMinusN)
                })
                .orderBy('date', 'DESC')
                .getRawMany();

            marketsByKey = history.reduce((obj, historyDataPoint) => {
                if (!obj[historyDataPoint.marketplace_key]) {
                    // sanity check, but should not happening
                    return obj;
                }

                const data = obj[historyDataPoint.marketplace_key];
                data.history.push({
                    ...historyDataPoint,
                    marketplace_key: undefined
                });

                return {
                    ...obj,
                    [historyDataPoint.marketplace_key]: data,
                };
            }, marketsByKey);
        }

        // cast to any[]
        // TODO: proper type
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let listings: any[] = Object.values(marketsByKey)
            .filter((market: MakretplaceItem) => !market.marketplace_key.includes('_'));

        if (days > 1) {
            // TODO: proper type
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            listings = listings.map((market: any) => market.history)
                .filter(i => i) // empty markets
                .flat();
        }

        const prices = listings.map(market => market.min_price);
        const avgPrices = listings.map(market => market.avg_price);

        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);

        const sum = avgPrices.reduce((total, price) => total + price, 0);
        const average = Math.round(sum / avgPrices.length);

        const counts = listings.map(market => market.count);
        const sumCount = counts.reduce((total, price) => total + price, 0);

        return {
            markets: Object.values(marketsByKey),
            total: {
                min_price: minPrice,
                max_price: maxPrice,
                avg_price: average,
                count: sumCount,
            }
        };
    }

    /**
     * Returns the current aggregated prices for marketplaces.
     * @param marketHashName item name
     */
    async getItemPrices(marketHashName: string) {
        return this.priceCalcView.find({
            select: [
                'current_price',
                'min_price',
                'max_price',
                'avg_price',
                'count',
                'marketplace_key'
            ],
            where: {
                market_hash_name: marketHashName
            }
        });
    }
}
