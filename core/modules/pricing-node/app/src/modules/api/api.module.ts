import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ItemPrice } from '../database/entities/item/item-price.entity';
import { PredictModule } from '../predict/predict.module';
import { ApiService } from './api.service';
import { V1Controller } from './v1.controller';
import { Marketplace } from '../database/entities/marketplace/marketplace.entity';
import { <PERSON><PERSON> } from '../database/entities/cookie/cookie.entity';
import { ComparisonModule } from '../comparison/comparison.module';
import { PublicController } from './public.controller';
import { PublicService } from './public.service';
import { PriceHistory } from '../database/views/price-history.view';
import { Item } from '../database/entities/item/item.entity';
import { PriceCalcNew } from '../database/views/price-calc-new.view';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            Item,
            ItemPrice,
            Marketplace,
            Cookie,
            PriceCalcNew,
            PriceHistory,
        ]),
        PredictModule,
        ComparisonModule,
    ],
    providers: [
        ApiService,
        PublicService,
    ],
    controllers: [
        V1Controller,
        PublicController,
    ]
})
export class ApiModule { }
