import { Controller, Get, Param } from '@nestjs/common';
import { PublicService } from './public.service';
import { GetPricesDto } from './dto/get-prices.dto';

@Controller('/v1/public/')
export class PublicController {

    constructor(
        private readonly publicService: PublicService,
    ) { }

    @Get([
        'prices/:market_hash_name/:days?'
    ])
    getPrices(
        @Param() params: GetPricesDto
    ) {
        const { market_hash_name: marketHashName, days } = params;
        return this.publicService.getHistory(marketHashName, days);
    }
}
