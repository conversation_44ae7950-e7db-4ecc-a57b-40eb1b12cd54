import { HttpException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Marketplace } from "../database/entities/marketplace/marketplace.entity";
import { Cookie, CookieType } from "../database/entities/cookie/cookie.entity";
import { BuffPredictService } from "../predict/buff-predict.service";
import { BuffPredictV2Service } from "../predict/buff-predict-v2.service";
import { PredictService } from "../predict/predict.service";

@Injectable()
export class ApiService {

    constructor(
        @InjectRepository(Marketplace)
        private readonly marketplaceRepository: Repository<Marketplace>,
        @InjectRepository(Cookie)
        private readonly cookieRepository: Repository<Cookie>,
        private readonly predictService: PredictService,
        private readonly buffPredictService: BuffPredictService,
        private readonly buffPredictV2Service: BuffPredictV2Service,
    ) { }

    public getActivePredictService(): PredictService | BuffPredictV2Service | BuffPredictService {
        switch (process.env.PREDICT_SERVICE) {
            case this.buffPredictV2Service.SERVICE_NAME:
                return this.buffPredictV2Service;
            case this.buffPredictService.SERVICE_NAME:
                return this.buffPredictService;
            default:
                return this.predictService;
        }
    }

    /**
     * Enable or disable marketplaces
     * @param key string
     * @param status boolean | string
     * @returns Entity Write Object
     */
    setMarketplaceStatus(
        key: string,
        status: boolean,
    ) {
        return this.marketplaceRepository.update({
            key,
        }, {
            status
        });
    }


    /**
     * Get all cookie by type
     * @returns Marketplace[]
     */
    getCookies(type: CookieType) {
        return this.cookieRepository.find({
            where: {
                type
            }
        });
    }

    /**
     * Add cookie to the database
     * @param type CookieType
     * @param value string
     * @returns Write Object Result
     */
    async addCookie(type: CookieType, value: string) {
        const cookie = await this.cookieRepository.findOne({
            where: {
                type,
                cookie: value,
            }
        });

        if (cookie) {
            throw new HttpException('The given Cookie already exists in the database.', 500);
        }

        return this.cookieRepository.save({
            cookie: value,
            type,
        });
    }

    /**
     * Get all marketplace Entity
     * @returns Marketplace[]
     */
    getMarketplaces() {
        return this.marketplaceRepository.find();
    }
}
