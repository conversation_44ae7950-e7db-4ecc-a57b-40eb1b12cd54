import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';


@Injectable()
export class AuthGuard implements CanActivate {

    // eslint-disable-next-line class-methods-use-this
    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        const { host } = request.headers;

        const internalHost = process.env.INTERNAL_HOSTNAME;
        if (host === internalHost) {
            // internal request, no need to check token
            return true;
        }

        const authHeader = request.headers.authorization;

        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.slice(7, authHeader.length);
            if (token === process.env.API_KEY) {
                return true;
            }
        }

        return false;
    }
}
