import { HttpModule } from '@nestjs/axios';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, OnApplicationBootstrap, Inject } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { mapLimit, sleep } from '@app/helpers/common.helper';
import { HttpInterceptModule } from '../http-intercept/http-intercept.module';
import { CurrenciesModule } from '../currencies/currencies.module';
import { Cookie } from '../database/entities/cookie/cookie.entity';
import { Game } from '../database/entities/game/game.entity';
import { ItemId } from '../database/entities/item/item-id.entity';
import { ItemPrice } from '../database/entities/item/item-price.entity';
import { Item } from '../database/entities/item/item.entity';
import { MarketplaceLog } from '../database/entities/marketplace/marketplace-log.entity';
import { Marketplace, Marketplaces, enabledMarketPlaces } from '../database/entities/marketplace/marketplace.entity';
import { BitskinsCsgoBulkParser } from './marketplaces/csgo/parsers/bitskins-csgo-bulk.parser';
import { BuffCsgoBulkParser } from './marketplaces/csgo/parsers/buff-csgo-bulk.parser';
import { BuffMarketCsgoBulkParser } from './marketplaces/csgo/parsers/buffmarket-csgo-bulk.parser';
import { BuffMarketCsgoBackupParser } from './marketplaces/csgo/parsers/buffmarket-csgo-backup.parser';
import { GamerpayListingCsgoParser } from './marketplaces/csgo/parsers/gamerpay-csgo-listing.parser';
import { MarketcsgoBuyorderCsgoParser } from './marketplaces/csgo/parsers/market-csgo-buyorder-csgo.parser';
import { MarketcsgoListingCsgoParser } from './marketplaces/csgo/parsers/market-csgo-listing-csgo.parser';
import { SkinportListingCsgoParser } from './marketplaces/csgo/parsers/skinport-listing-csgo.parser';
import { MarketplaceService } from '../marketplace/marketplace.service';
import { CsmoneyCsgoParser } from './marketplaces/csgo/parsers/csmoney-csgo-listing.parser';
import { ScmParser } from './marketplaces/csgo/parsers/scm-csgo.parser';
import { BuffCsgoBackupParser } from './marketplaces/csgo/parsers/buff-csgo-backup.parser';
import { CSGOfloatParser } from './marketplaces/csgo/parsers/csgofloat.parser';
import { BuffNewPhasesCsgoParser } from './marketplaces/csgo/parsers/buff-doppler-csgo.parser';
import { DmarketParser } from './marketplaces/csgo/parsers/dmarket.parser';
import { ScmBuyParser } from './marketplaces/csgo/parsers/scm-buy.parser';
import { CsgorollParser } from './marketplaces/csgo/parsers/csgoroll.parser';
import { WaxpeerCsgoParser } from './marketplaces/csgo/parsers/waxpeer-csgo-listing.parser';
import { Scraper } from './scraper.interface';

const logger = new Logger('PricingScraperModule');

const providers = {
    [Marketplaces.CSGORoll]: [CsgorollParser],
    [Marketplaces.Buff]: [BuffCsgoBulkParser, BuffCsgoBackupParser, BuffNewPhasesCsgoParser],
    [Marketplaces.BuffBuy]: [BuffCsgoBulkParser, BuffCsgoBackupParser, BuffNewPhasesCsgoParser],
    [Marketplaces.BuffMarket]: [BuffMarketCsgoBulkParser, BuffMarketCsgoBackupParser],
    [Marketplaces.BuffMarketBuy]: [BuffMarketCsgoBulkParser, BuffMarketCsgoBackupParser],
    [Marketplaces.Bitskins]: [],
    [Marketplaces.BitskinsSale]: [BitskinsCsgoBulkParser],
    [Marketplaces.CSFloat]: [CSGOfloatParser],
    [Marketplaces.CSMoney]: [CsmoneyCsgoParser],
    [Marketplaces.Dmarket]: [DmarketParser],
    [Marketplaces.DmarketBuy]: [DmarketParser],
    [Marketplaces.Gamerpay]: [GamerpayListingCsgoParser],
    [Marketplaces.MarketCSGO]: [MarketcsgoListingCsgoParser],
    [Marketplaces.MarketCSGOBuy]: [MarketcsgoBuyorderCsgoParser],
    [Marketplaces.SteamCommunityMarket]: [ScmParser],
    [Marketplaces.SteamCommunityMarketBuy]: [ScmBuyParser],
    [Marketplaces.Waxpeer]: [WaxpeerCsgoParser],
    [Marketplaces.Skinport]: [SkinportListingCsgoParser],
};

const providersList = Object.values(Object
    .entries(providers)
    .filter(([key]) => enabledMarketPlaces.includes(key))
    .map(([, value]) => value)
    .flat()
    .reduce((list, provider) => {
        // eslint-disable-next-line no-param-reassign
        list[provider.toString()] = provider;
        return list;
    }, {})) as [];

logger.debug(`Enabled marketplaces: ${enabledMarketPlaces.join(', ')} with ${providersList.length} scrapers`);

@Module({
    imports: [
        ScheduleModule.forRoot(),
        TypeOrmModule.forFeature([
            ItemPrice,
            Item,
            ItemId,
            Game,
            Marketplace,
            MarketplaceLog,
            Cookie,
        ]),
        HttpModule,
        CurrenciesModule,
        HttpInterceptModule,
    ],
    providers: [
        MarketplaceService,
        ...providersList,
        {
            provide: 'Scrapers',
            useFactory: (...scrapers: Scraper[]) => scrapers,
            inject: [...providersList],
        },
    ],
})
export class PriceScraperModule implements OnApplicationBootstrap {
    private readonly logger = new Logger('PriceScraperModule');

    constructor(@Inject('Scrapers') private scrapers: Scraper[]) {
    }

    async onApplicationBootstrap() {
        await mapLimit(this.scrapers, async (scraper: Scraper) => {
            this.logger.verbose(`Starting scraper ${scraper.constructor.name}`);
            await scraper.initialize();
            scraper.process(); // do not wait for this
            await sleep(5000);
        }, 1);
    }
}
