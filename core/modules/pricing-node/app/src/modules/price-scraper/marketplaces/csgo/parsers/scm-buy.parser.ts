import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { mapLimit, sleep, steamMarketHeaders } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import * as BeeQueue from 'bee-queue';
import { ScmBuyOrderResponse } from '../models/scm.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class ScmBuyParser implements Scraper {

    private readonly logger = new Logger(ScmBuyParser.name);

    private scmBuyorder: Marketplace;

    private readonly onSuccessDelay = 100;

    private readonly maxRetry = 3;

    private readonly endpoint = `https://steamcommunity.com/market/itemordershistogram?norender=1&country=US&language=english&currency=1&item_nameid={steamItemId}&two_factor=0`;

    private readonly scmBuyMeta = {
        key: Marketplaces.SteamCommunityMarketBuy,
        name: 'Steam Buy Order Prices',
        games: ['CS:GO'],
    };

    private queue = new BeeQueue('scm-buy-csgo', {
        redis: {
            url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}/`,
        },
        removeOnSuccess: true,
        removeOnFailure: true,
    });

    constructor(
        private httpService: HttpService,
        private marketplaceService: MarketplaceService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.scmBuyorder = await this.marketplaceService.getMarketplace(this.scmBuyMeta);

        await this.queue.ready();

        this.queue.process(parseInt(process.env.REQUEST_CONCURRENCY, 10), async (job) => {
            try {
                const data = await this.getData(job.data.steamItemId);
                await this.processBuyOrder(data, job.data.steamItemId);
            } catch (e) {
                // local server error? Db save error?
            }
            await sleep(this.onSuccessDelay);
            return null;
        });
    }

    /**
     * Init The Queue, fill the queue with page numbers
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void | null> {
        const counts = await this.queue.checkHealth();

        /**
         * Return if theres an active process
         */
        if (counts.waiting > 0) {
            return;
        }

        const itemIds = await this.marketplaceService.getItemIds('steamItemId');
        await mapLimit(itemIds, (steamItemId: string) => this.queue.createJob({ steamItemId }).save());
    }

    /**
     * Processing Scm response
     * @param buyOrder ScmBuyOrderResponse
     */
    private async processBuyOrder(buyOrder: ScmBuyOrderResponse | boolean, steamItemId: string) {
        if (typeof buyOrder === 'boolean') {
            return;
        }

        const item = await this.marketplaceService.getItemByItemId(
            'steamItemId',
            steamItemId,
        );

        if (!item) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.scmBuyorder,
            item,
            parseInt(buyOrder.highest_buy_order, 10),
            parseInt(buyOrder.buy_order_count, 10),
        );
    }

    /**
     * Getting Steam response
     * @param steamItemId string
     * @returns Steam response
     */
    private async getData(steamItemId: string, retry: number = 0): Promise<ScmBuyOrderResponse | boolean> {
        if (retry > this.maxRetry) {
            return false;
        }

        try {
            return (
                await lastValueFrom(
                    this.httpService.get(this.endpoint.replace('{steamItemId}', steamItemId), {
                        headers: steamMarketHeaders(),
                    }),
                )
            ).data;
        } catch (e) {
            this.logger.error(`Failed to fetch steam ${steamItemId} ${e.message}`);
            await sleep(5000);
            return this.getData(steamItemId, retry + 1);
        }
    }
}
