import { Game } from '@app/modules/database/entities/game/game.entity';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { MarketCSGOBuyorderResponse } from '../models/marketcsgo-buyorder.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class MarketcsgoBuyorderCsgoParser implements Scraper {

    private marketplace: Marketplace;

    private readonly appId = 730;

    private readonly endpoint =
        'https://market.csgo.com/api/v2/prices/class_instance/USD.json';

    private readonly meta = {
        key: Marketplaces.MarketCSGOBuy,
        name: 'Market.CSGO Buy Order Prices',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(
            this.meta,
        );
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {
        const gameEntity = this.marketplace.games.find(
            (game: Game) => game.app_id === this.appId && game.status,
        );

        // Game disabled in db
        if (!gameEntity) {
            return;
        }

        try {
            const buyorderPrices = {};
            const buyorders = (
                await lastValueFrom(this.httpService.get(this.endpoint, {
                    headers: {
                        'Accept': 'gzip, deflate, br'
                    }
                }))
            ).data as MarketCSGOBuyorderResponse;

            const classes = Object.keys(buyorders.items);
            classes.forEach((cls) => {
                const item = buyorders.items[cls];
                if (
                    item.buy_order * 100 < buyorderPrices[item.market_hash_name] ||
                    buyorderPrices[item.market_hash_name] === undefined
                ) {
                    buyorderPrices[item.market_hash_name] = item.buy_order;
                }
            });

            await mapLimit(Object.keys(buyorderPrices), itemName => this.processItem(itemName, buyorderPrices[itemName]));
        } catch (e) {
            await this.marketplaceService.error(
                e,
                MarketcsgoBuyorderCsgoParser.name
            );
        }
    }

    /**
     * Processing the item
     * @param itemName string
     * @param price number
     * @returns Promise void | null
     */
    private async processItem(itemName: string, price: number): Promise<void | null> {
        const dbItem = await this.marketplaceService.getItemByHashName(
            itemName,
            this.appId,
        );

        if (!dbItem) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            Math.round(
                (Math.ceil((price * 1000) / 5) * 5 * 100) / 1000, // to normalize price
            ),
        );
    }
}
