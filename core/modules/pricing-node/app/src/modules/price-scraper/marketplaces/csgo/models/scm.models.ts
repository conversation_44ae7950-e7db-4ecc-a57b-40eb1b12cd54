export interface ScmResponse {
    success: boolean;
    start: number;
    pagesize: number;
    total_count: number;
    searchdata: Searchdata;
    results: Result[];
}

export interface Searchdata {
    query: string;
    search_descriptions: boolean;
    total_count: number;
    pagesize: number;
    prefix: string;
    class_prefix: string;
}

export interface Result {
    name: string;
    hash_name: string;
    sell_listings: number;
    sell_price: number;
    sell_price_text: string;
    app_icon: string;
    app_name: string;
    asset_description: AssetDescription;
    sale_price_text: string;
}

export interface AssetDescription {
    appid: number;
    classid: string;
    instanceid: string;
    currency: number;
    background_color: string;
    icon_url: string;
    icon_url_large: string;
    descriptions: Description[];
    tradable: number;
    actions: Action[];
    name: string;
    name_color: string;
    type: string;
    market_name: string;
    market_hash_name: string;
    market_actions: MarketAction[];
    commodity: number;
    market_tradable_restriction: number;
    marketable: number;
    fraudwarnings?: string[];
    owner_descriptions?: OwnerDescription[];
}

export interface Description {
    type: string;
    value: string;
    color?: string;
}

export interface Action {
    link: string;
    name: string;
}

export interface MarketAction {
    link: string;
    name: string;
}

export interface OwnerDescription {
    type: string;
    value: string;
    color?: string;
}


export interface ScmBuyOrderResponse {
    sell_order_count: string;
    buy_order_count: string;
    highest_buy_order: string;
    lowest_sell_order: string;
}
