import { CurrenciesService } from '@app/modules/currencies/currencies.service';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { SkinportItem } from '../models/skinport.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class SkinportListingCsgoParser implements Scraper {
    private marketplace: Marketplace;

    private readonly appId = 730;

    private readonly endpoint = 'https://api.skinport.com/v1/items?app_id=730';

    private EUR = 1;

    private readonly meta = {
        key: Marketplaces.Skinport,
        name: 'Skinport Listing Prices',
        games: ['CSGO', 'DOTA2', 'TF2'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly currenciesService: CurrenciesService,
        private readonly httpService: HttpService,
    ) { }

    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(
            this.meta,
        );
    }

    /**
     * Loading EUR Currency
     */
    private async loadCurrency() {
        this.EUR = (await this.currenciesService.getCurrency('EUR'))?.rate;
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {
        if (!process.env.SKINPORT_CLIENTID) {
            await this.marketplaceService.error(
                new Error('SKINPORT_CLIENTID was not found in PROD.env/DEV.env file'),
                SkinportListingCsgoParser.name,
            );
        }
        if (!process.env.SKINPORT_SECRET) {
            await this.marketplaceService.error(
                new Error('SKINPORT_SECRET was not found in PROD.env/DEV.env file'),
                SkinportListingCsgoParser.name,
            );
        }

        await this.loadCurrency();

        const response = (
            await lastValueFrom(
                this.httpService.get(this.endpoint, {
                    headers: {
                        authorization: `Basic ${Buffer.from(
                            `${process.env.SKINPORT_CLIENTID}:${process.env.SKINPORT_SECRET}`,
                        ).toString('base64')}`,
                    },
                }),
            )
        ).data as SkinportItem[];

        await mapLimit(response, item => this.processItem(item));
    }

    /**
     * Processing item object
     * @param item SkinportItem object
     * @returns null | void
     */
    private async processItem(item: SkinportItem): Promise<void | null> {

        if (!item.min_price) {
            return;
        }

        const dbItem = await this.marketplaceService.getItemByHashName(
            item.market_hash_name,
            this.appId,
        );

        if (!dbItem) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            Math.round((item.min_price / this.EUR) * 100),
            item.quantity,
        );

    }
}
