export interface BuffBackupResponse {
    code: string
    data: Data

    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    msg: any
}

export interface Data {
    appid: number
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    asset_tags: any[]
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    asset_tags_history: any[]
    bookmarked: boolean
    buy_max_price: string
    buy_min_price_limit: string
    buy_num: number
    can_buy: boolean
    can_sort_by_heat: boolean
    container_type: string
    containers: string[]
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    description: any
    fade_choices: string[][]
    game: string
    goods_info: GoodsInfo
    has_buff_price_history: boolean
    has_fade_name: boolean
    has_paintwear_rank: boolean
    has_related: boolean
    has_rune: boolean
    id: number
    is_container: boolean
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    item_id: any
    market_hash_name: string
    market_min_price: string
    name: string
    paintseed_filters: PaintseedFilter[]
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    paintseed_filters_history: any[]
    paintwear_choices: string[][]
    paintwear_range: string[]
    quick_price: string
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    rank_types: any[]
    recent_sold_count: number
    relative_goods: RelativeGood[]
    sell_min_price: string
    sell_num: number
    sell_reference_price: string
    share_data: ShareData
    short_name: string
    show_game_cms_icon: boolean
    sort_by_fields: SortByFields
    steam_market_url: string
    super_short_name: string
    support_name_tag: boolean
    transacted_num: number
    user_show_count: number
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    wiki_link: any
}

export interface GoodsInfo {
    can_3d_inspect: boolean
    can_display_inspect: boolean
    can_inspect: boolean
    can_preview: boolean
    can_preview_upload: boolean
    can_search_by_patch: boolean
    can_search_by_sticker: boolean
    can_search_by_tournament: boolean
    can_specific_buy: boolean
    can_specific_paintwear_buy: boolean
    icon_url: string
    info: Info
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    item_id: any
    normal_icon_url: string
    original_icon_url: string
    // TODO: proper type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    specific: any[]
    specific_paintwear_buying_choices: string[][]
    steam_price: string
    steam_price_cny: string
}

export interface Info {
    tags: Tags
}

export interface Tags {
    exterior: Exterior
    quality: Quality
    rarity: Rarity
    type: Type
    weapon: Weapon
}

export interface Exterior {
    category: string
    id: number
    internal_name: string
    localized_name: string
}

export interface Quality {
    category: string
    id: number
    internal_name: string
    localized_name: string
}

export interface Rarity {
    category: string
    id: number
    internal_name: string
    localized_name: string
}

export interface Type {
    category: string
    id: number
    internal_name: string
    localized_name: string
}

export interface Weapon {
    category: string
    internal_name: string
    localized_name: string
}

export interface PaintseedFilter {
    name: string
    placeholder?: string
    search?: boolean
    type: string
    items?: Item[]
}

export interface Item {
    name: string
    value: number
}

export interface RelativeGood {
    goods_id: number
    is_change: boolean
    sell_min_price: string
    sell_num: number
    tag: string
    tag_name: string
}

export interface ShareData {
    content: string
    thumbnail: string
    title: string
    url: string
}

export interface SortByFields {
    list: List[]
    title: string
}

export interface List {
    attribute: string
    default_value: string
    list: List2[]
}

export interface List2 {
    title: string
    value: string
}
