import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import * as BeeQueue from 'bee-queue';
import { CurrenciesService } from '@app/modules/currencies/currencies.service';
import { sleep, mapLimit } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { BuffBackupResponse } from '../models/buff-backup.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class BuffCsgoBackupParser implements Scraper {

    protected logger = new Logger(BuffCsgoBackupParser.name);

    private buff: Marketplace;

    private buffBuyorder: Marketplace;

    private onSuccessDelay = 100;

    private appId = 730;

    private maxRetry = 5;

    private CNY = 1;

    protected endpoint = 'https://buff.163.com/api/market/goods/info?goods_id={goodsId}';

    protected apiHost = 'buff.163.com';

    protected buffItemIdKey: string = 'buffId';

    protected readonly buffMeta = {
        key: Marketplaces.Buff,
        name: 'Buff Listing Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    protected readonly buffBuyMeta = {
        key: Marketplaces.BuffBuy,
        name: 'Buff Buy Order Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    protected queue = new BeeQueue('buff-backup-csgo', {
        redis: {
            url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}/`,
        },
        removeOnSuccess: true,
        removeOnFailure: true,
    });

    constructor(
        protected httpService: HttpService,
        protected marketplaceService: MarketplaceService,
        private currenciesService: CurrenciesService,
    ) { }

    async initialize() {
        this.buff = await this.marketplaceService.getMarketplace(this.buffMeta);
        this.buffBuyorder = await this.marketplaceService.getMarketplace(this.buffBuyMeta);

        this.queue.process(parseInt(process.env.REQUEST_CONCURRENCY, 10), async (job) => {
            try {
                await this.processGoods(await this.getData(job.data.goodsId));
            } catch (e) {
                this.logger.error(`Failed process goods ${e.message}`);
            }
            await sleep(this.onSuccessDelay);
            return null;
        });

        await this.loadCurrency();
    }

    /**
     * Load CNY currency
     */
    protected async loadCurrency() {
        this.CNY = 1 / (await this.currenciesService.getCurrency('CNY')).rate;
    }

    /**
     * Init The Queue, fill the queue with page numbers
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void | null> {
        const counts = await this.queue.checkHealth();
        /**
         * Return if theres an active process
         */
        if (counts.waiting > 0) {
            return;
        }

        await this.loadCurrency();

        const goods = await this.marketplaceService.getItemIds(this.buffItemIdKey);

        await mapLimit(goods, goodsId => this.queue.createJob({ goodsId }).save());
    }

    /**
     * Processing Buff filter page
     * @param page Buff Page Response
     */
    private async processGoods(goods: BuffBackupResponse | boolean) {

        if (typeof goods === 'boolean') {
            return;
        }

        const item = await this.marketplaceService.getItemByHashName(
            goods.data.market_hash_name,
            this.appId,
        );

        await this.marketplaceService.storePrice(
            this.buff,
            item,
            Math.round(parseInt(goods.data.sell_min_price, 10) * this.CNY * 100),
            goods.data.sell_num,
        );
        await this.marketplaceService.storePrice(
            this.buffBuyorder,
            item,
            Math.round(parseInt(goods.data.buy_max_price, 10) * this.CNY * 100),
            goods.data.buy_num,
        );

    }

    /**
     * Getting Buff response for doppler item
     * @param buffId int
     * @returns Buff response
     */
    async getData(buffId, retry = 0): Promise<BuffBackupResponse | boolean> {
        if (retry > this.maxRetry) {
            this.logger.error(`Too many retries ${retry} > ${this.maxRetry}`);
            return false;
        }

        try {
            const url = this.endpoint.replace('{goodsId}', buffId);
            return (
                await lastValueFrom(
                    this.httpService.get(url, {
                        headers: this.getHeaders(),
                    }),
                )
            ).data;
        } catch (e) {
            this.logger.error(`Failed to fetch buff ${buffId} ${e.message}`);
            await sleep(5000);
            return this.getData(buffId, retry + 1);
        }
    }

    protected getHeaders(): { [key: string]: string } {
        return {
            Cookie: 'Locale-Supported=en',
            Origin: `https://${this.apiHost}`,
            Referer: `https://${this.apiHost}/`,
            'Access-Control-Request-Headers': 'x-csrftoken',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        };
    }
}
