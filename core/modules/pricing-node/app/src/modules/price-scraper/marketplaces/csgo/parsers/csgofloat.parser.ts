import { Game } from '@app/modules/database/entities/game/game.entity';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { CSGOFloatItem } from '../models/csgofloat.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class CSGOfloatParser implements Scraper {

    private marketplace: Marketplace;

    private readonly appId = 730;

    private readonly endpoint = `https://csfloat.com/api/v1/listings/price-list`;

    private readonly meta = {
        key: Marketplaces.CSFloat,
        name: 'CSGOFloat.com Listing Prices',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
    ) { }

    /**
     * Init service
     */
    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(this.meta);
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {
        const gameEntity = this.marketplace.games.find(
            (game: Game) => game.app_id === this.appId && game.status,
        );

        // Game disabled in db
        if (!gameEntity) {
            return;
        }

        try {
            const items = (await lastValueFrom(this.httpService.get(this.endpoint))).data as CSGOFloatItem[];

            await mapLimit(items, item => this.processItem(item));

        } catch (e) {
            await this.marketplaceService.error(
                e,
                CSGOfloatParser.name
            );
        }
    }

    /**
     * Processing item object
     * @param item CSGOFloatItem object
     * @returns null | void
     */
    private async processItem(item: CSGOFloatItem): Promise<void | null> {
        const dbItem = await this.marketplaceService.getItemByHashName(
            item.market_hash_name,
            this.appId,
        );
        if (!dbItem) {
            // TODO: Upgrade item loader from steamapis to csgo files
            return;
        }
        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            item.min_price,
            item.qty,
        );
    }
}
