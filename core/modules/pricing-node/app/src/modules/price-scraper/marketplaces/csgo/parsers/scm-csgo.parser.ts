import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import * as BeeQueue from 'bee-queue';
import { mapLimit, sleep, steamMarketHeaders } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { Result, ScmResponse } from '../models/scm.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class ScmParser implements Scraper {

    private scm: Marketplace;

    private readonly url = `https://steamcommunity.com/market/search/render/?norender=1&query=&count=100&sort_column=popular&sort_dir=desc&appid=730&start=[start]`;

    private readonly appId = 730;

    private readonly onErrorDelay = 5000;

    private readonly scmMeta = {
        key: Marketplaces.SteamCommunityMarket,
        name: 'Steam Listing Prices',
        games: ['CS:GO'],
    };

    private queue = new BeeQueue('scm-csgo', {
        redis: {
            url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}/`,
        },
        removeOnSuccess: true,
        removeOnFailure: true,
    });

    constructor(
        private httpService: HttpService,
        private marketplaceService: MarketplaceService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.scm = await this.marketplaceService.getMarketplace(
            this.scmMeta,
        );

        await this.queue.ready();

        this.queue.process(parseInt(process.env.REQUEST_CONCURRENCY, 10), async (job) => {
            try {
                const page = await this.getPage(job.data.page);
                if (page) {
                    await this.processPage(page.results);
                }
            } catch (e) {
                // local server error? Db save error?
            }
            return null;
        });
    }

    /**
     * Init The Queue, fill the queue with page numbers
     */
    @Cron(process.env.PRICING_CRON)
    public async process() {

        const counts = await this.queue.checkHealth();

        /**
         * Return if theres an active process
         */
        if (counts.waiting > 0) {
            return;
        }

        const response = (await this.getPage(1)) as ScmResponse;

        if (!response) {
            // SCM offline / bad response
            return;
        }

        await this.processPage(response.results);

        const pageCount = Math.ceil(response.total_count / 100);

        for (let page = 2; page !== pageCount + 1; page += 1) {
            const job = this.queue.createJob({
                page,
            });
            // eslint-disable-next-line no-await-in-loop
            await job.save();
        }
    }

    /**
     * Processing SCM Page Response
     * @param page SCM Page Response
     */
    private async processPage(results: Result[]): Promise<void> {

        if (!results || results.length === 0) {
            return;
        }

        await mapLimit(results, item => this.processItem(item));
    }

    /**
     * Processing SCM Item Prices & quantity
     * @param Result SCM item response
     */
    private async processItem(result: Result): Promise<void | null> {

        if (!result) {
            return;
        }

        const item = await this.marketplaceService.getItemByHashName(
            result.hash_name,
            this.appId,
        );

        await this.marketplaceService.storePrice(
            this.scm,
            item,
            result.sell_price,
            result.sell_listings,
        );

    }

    /**
     *
     * @param page Page number
     * @returns return SCM response
     */
    private async getPage(page): Promise<ScmResponse> {
        try {
            const response = (
                await lastValueFrom(
                    this.httpService.get(
                        this.url.replace('[start]', ((page * 100) - 100).toString()),
                        {
                            headers: steamMarketHeaders(),
                        },
                    ),
                )
            ).data;

            if (response) {
                return response;
            }
        } catch (e) {
            await sleep(this.onErrorDelay);
            return await this.getPage(page);
        }

        return null;
    }
}
