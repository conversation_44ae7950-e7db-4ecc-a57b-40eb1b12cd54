import { CurrenciesService } from '@app/modules/currencies/currencies.service';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { GamerpayItem } from '../models/gamerpay.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class GamerpayListingCsgoParser implements Scraper {

    private marketplace: Marketplace;

    private EUR = 1;

    private readonly appId = 730;

    private readonly endpoint = 'https://api.gamerpay.gg/prices';

    private readonly meta = {
        key: Marketplaces.Gamerpay,
        name: 'Gamerpay Listing Prices',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
        private readonly currenciesService: CurrenciesService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        await this.loadCurrency();
        this.marketplace = await this.marketplaceService.getMarketplace(
            this.meta,
        );
    }

    /**
     * Load EUR currency for later use
     */
    private async loadCurrency() {
        this.EUR = (await this.currenciesService.getCurrency('EUR')).rate;
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {

        await this.loadCurrency();

        const gameEntity = this.marketplace.games.find(
            (game) => game.app_id === this.appId && game.status,
        );

        // Game disabled in db
        if (!gameEntity) {
            return;
        }

        try {
            const items = (
                await lastValueFrom(
                    this.httpService.get(this.endpoint, {
                        headers: {
                            'content-type': 'application/json',
                        },
                    }),
                )
            ).data as GamerpayItem[];

            await mapLimit(items, item => this.processItem(item));
        } catch (e) {
            await this.marketplaceService.error(
                e,
                GamerpayListingCsgoParser.name
            );
        }
    }

    /**
     * Process item object
     * @param item GamerpayItem object
     * @returns null | void
     */
    private async processItem(item: GamerpayItem): Promise<null | void> {

        const dbItem = await this.marketplaceService.getItemByHashName(
            `${item.item}${item.phase ? ` - ${item.phase}` : ''}`,
            this.appId,
        );

        if (!dbItem) {
            // TODO: Upgrade item loader from steamapis to csgo files
            // console.log(`${item.item} Not found in the DB`);
            return;
        }

        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            Math.round((item.price * 100) / this.EUR), // to normalize prices
            item.count,
        );

    }
}
