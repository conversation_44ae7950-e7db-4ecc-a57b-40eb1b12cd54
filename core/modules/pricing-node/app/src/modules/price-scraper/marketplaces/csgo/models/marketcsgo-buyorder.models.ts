export interface MarketCSGOBuyorderItem {
    price: string;
    buy_order: number;
    avg_price: string;
    popularity_7d: string;
    market_hash_name: string;
    ru_name: string;
    ru_rarity: string;
    ru_quality: string;
    text_color: string;
    bg_color: string;
}

export interface Items {
    [key: string]: MarketCSGOBuyorderItem;
}

export interface MarketCSGOBuyorderResponse {
    success: boolean;
    time: number;
    currency: string;
    items: Items;
} 
