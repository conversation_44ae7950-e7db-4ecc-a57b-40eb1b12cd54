import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { mapLimit } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class CsgorollParser implements Scraper {

    private marketplace: Marketplace;

    private isRunning = false; // prevent multiple instances

    private appId = 730;

    private readonly endpoint = 'https://api.csgoroll.com/graphql';

    private readonly meta = {
        key: Marketplaces.CSGORoll,
        name: 'CSGOROll Prices',
        games: ['CS:GO'],
    };

    constructor(
        private httpService: HttpService,
        private marketplaceService: MarketplaceService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(this.meta);
    }

    /**
     * Init The Queue, fill the queue with page numbers
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {

        if (this.isRunning) {
            return;
        }

        this.isRunning = true;

        const items = {};
        let cursor = null;

        // eslint-disable-next-line no-constant-condition
        while (true) {
            try {
                // eslint-disable-next-line no-await-in-loop
                const { data } = await this.query({
                    after: cursor,
                });
                const { edges, pageInfo } = data.items;
                edges.forEach(edge => {
                    const item = edge.node;

                    if (item.category && item.category.name !== 'CSGO') {
                        // skip non csgo items (e.g. Dota 2)
                        return;
                    }

                    const price = this.convertPrice(item.value);

                    let itemName = item.brand;
                    if (item.brand === 'Sticker') {

                        if (item.name) {
                            if (
                                item.itemVariants[0] &&
                                item.itemVariants[0].color
                            ) {
                                items[`${itemName} | ${item.name.replace(' |', ` (${item.itemVariants[0].color}) |`)}`] = price;
                                items[`${itemName} (${item.itemVariants[0].color}) | ${item.name}`] = price;
                            } else {
                                items[`${itemName} | ${item.name}`] = price;
                            }
                        }
                    } else {
                        if (item.name) {
                            itemName += ` | ${item.name}`;
                        }
                        if (
                            item.itemVariants[0] &&
                            item.itemVariants[0].color
                        ) {
                            itemName += ` (${item.itemVariants[0].color})`;
                        }
                    }

                    itemName = this.fixDopplerNames(itemName);

                    items[itemName] = price;
                });

                if (!pageInfo.hasNextPage || !pageInfo.endCursor || pageInfo.endCursor === cursor) {
                    break;
                }

                cursor = pageInfo.endCursor;
            } catch (e) {
                // HTTP Errors by CSGORoll Graphql API
                // 500 Internal Server Error
                // 502 Bad Gateway
                // 503 Service Unavailable
            }
        }

        this.isRunning = false;

        await mapLimit(Object.keys(items).map(key => ({
            name: key,
            price: items[key],
        })), item => this.processItem(item));

    }

    // eslint-disable-next-line class-methods-use-this
    private fixDopplerNames(name: string): string {
        if (name.includes(' Ruby (')) {
            return `${name.replace(' Ruby', ' Doppler')} - Ruby`;
        }
        if (name.includes(' Sapphire (')) {
            return `${name.replace(' Sapphire', ' Doppler')} - Sapphire`;
        }
        if (name.includes(' Emerald (')) {
            return `${name.replace(' Emerald', ' Doppler')} - Emerald`;
        }
        if (name.includes(' Black Pearl (')) {
            return `${name.replace(' Black Pearl', ' Doppler')} - Black Pearl`;
        }
        if (name.includes(' Phase 1')) {
            return `${name.replace(' Phase 1', '')} - Phase 1`;
        }
        if (name.includes(' Phase 2')) {
            return `${name.replace(' Phase 2', '')} - Phase 2`;
        }
        if (name.includes(' Phase 3')) {
            return `${name.replace(' Phase 3', '')} - Phase 3`;
        }
        if (name.includes(' Phase 4')) {
            return `${name.replace(' Phase 4', '')} - Phase 4`;
        }
        return name;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private async processItem(item: any): Promise<void> {
        const { name, price } = item;

        const dbItem = await this.marketplaceService.getItemByHashName(
            name,
            this.appId,
        );

        if (!dbItem) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            price,
            1,
        );
    }

    // eslint-disable-next-line class-methods-use-this
    private convertPrice(price: number): number {
        return Math.round(price * 100 * 0.667);
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private async query(variables: any = {}): Promise<any> {
        const { data } = await lastValueFrom(this.httpService.post(this.endpoint, {
            query: `
				query ItemList($after: String, $categoryId: ID, $distinctValues: Boolean, $first: PaginationAmount, $marketId: ID, $maxValue: Float, $minValue: Float, $name: String, $obtainable: Boolean, $orderBy: [ItemOrderBy], $usable: Boolean, $withdrawable: Boolean, $minValueUpdatedAt: SequelizeDate, $maxValueUpdatedAt: SequelizeDate, $containedInAnyBox: Boolean) {
				items(after: $after, categoryId: $categoryId, distinctValues: $distinctValues, first: $first, marketId: $marketId, maxValue: $maxValue, minValue: $minValue, name: $name, obtainable: $obtainable, orderBy: $orderBy, usable: $usable, withdrawable: $withdrawable, minValueUpdatedAt: $minValueUpdatedAt, maxValueUpdatedAt: $maxValueUpdatedAt, containedInAnyBox: $containedInAnyBox) {
						pageInfo {
						hasNextPage
						endCursor
					}
						edges {
							node {
								...Item
							}
						}
					}
				}
				fragment Item on Item {
					name
					brand
					value
					itemVariants {
						name
						color
						value
					}
					category {
						name
					}
					createdAt
					releasedAt
					valueUpdatedAt
				}
`,
            variables: {
                first: 250, // max 250 limit
                distinctValues: false,
                orderBy: 'VALUE_DESC',
                after: null, // cursor
                ...variables,
            }
        }, {
            headers: {
                'authority': 'api.csgoroll.com',
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7,cy;q=0.6',
                'apollographql-client-name': 'csgoroll-www',
                'apollographql-client-version': 'v31',
                'cache-control': 'no-cache',
                'ngsw-bypass': 'true',
                'origin': 'https://www.csgoroll.com',
                'pragma': 'no-cache',
                'referer': 'https://www.csgoroll.com/',
                'sec-ch-ua': '"Chromium";v="116", "Not)A;Brand";v="24", "Google Chrome";v="116"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
                'Content-Type': 'application/json',
            },
        }));
        return data;
    }

}
