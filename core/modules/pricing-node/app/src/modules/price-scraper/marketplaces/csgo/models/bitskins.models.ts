export interface RecentSalesInfo {
    hours: string;
    average_price: string;
}

export interface BitskinsItem {
    market_hash_name: string;
    total_items: number;
    lowest_price: string;
    highest_price: string;
    cumulative_price: string;
    recent_sales_info: RecentSalesInfo;
    updated_at: number;
}

export interface Data {
    app_id: string;
    context_id: string;
    items: BitskinsItem[];
}

export interface BitskinsResponse {
    status: string;
    data: Data;
}
