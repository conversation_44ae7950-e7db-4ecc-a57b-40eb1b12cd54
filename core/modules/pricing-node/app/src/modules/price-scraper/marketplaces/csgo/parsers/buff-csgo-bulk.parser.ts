import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import * as BeeQueue from 'bee-queue';
import { CookieType } from '@app/modules/database/entities/cookie/cookie.entity';
import { CurrenciesService } from '@app/modules/currencies/currencies.service';
import { sleep, mapLimit } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { BuffItem, BuffPage } from '../models/buff.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class BuffCsgoBulkParser implements Scraper {

    private readonly logger = new Logger(BuffCsgoBulkParser.name);

    private buff: Marketplace;

    private buffBuyorder: Marketplace;

    private CNY = 1;

    private readonly jobConcurrency = 1;

    private readonly url = `https://buff.163.com/api/market/goods/all?game=[game]&page_num=[page]&sort_by=price.desc&page_size=80`;

    private readonly game = 'csgo';

    private readonly appId = 730;

    private readonly onErrorDelay = 3000;

    private readonly onSuccessDelay = 45000;

    private readonly buffMeta = {
        key: Marketplaces.Buff,
        name: 'Buff Listing Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    private readonly buffBuyMeta = {
        key: Marketplaces.BuffBuy,
        name: 'Buff Buy Order Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    private queue = new BeeQueue('buff-csgo', {
        redis: {
            url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}/`,
        },
        removeOnSuccess: true,
        removeOnFailure: true,
    });

    constructor(
        private httpService: HttpService,
        private marketplaceService: MarketplaceService,
        private currenciesService: CurrenciesService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.buff = await this.marketplaceService.getMarketplace(this.buffMeta);
        this.buffBuyorder = await this.marketplaceService.getMarketplace(this.buffBuyMeta);

        this.queue.process(this.jobConcurrency, async (job) => {
            try {
                const page = await this.getPage(job.data.page);
                if (page) {
                    await this.processPage(page);
                }
            } catch (e) {
                this.logger.error(`Error while processing buff page ${job.data.page}`, e);
            }
            await sleep(this.onSuccessDelay);
            return null;
        });

        await this.loadCurrency();
    }

    /**
     * Load CNY currency
     */
    private async loadCurrency() {
        this.CNY = 1 / (await this.currenciesService.getCurrency('CNY')).rate;
    }

    /**
     * Init The Queue, fill the queue with page numbers
     * @cron Running the once a day just to collect all buffId
     */
    @Cron('0 0 5 * * *')
    public async process(): Promise<void | null> {
        const counts = await this.queue.checkHealth();
        /**
         * Return if theres an active process
         */
        if (counts.waiting > 0) {
            return;
        }

        const response = (await this.getPage(1));

        if (!response) {
            // Buff error
            return;
        }

        await this.loadCurrency();

        await this.processPage(response);

        const pageCount = response.total_page;
        for (let page = 2; page !== pageCount + 1; page += 1) {
            const job = this.queue.createJob({
                page,
            });
            // eslint-disable-next-line no-await-in-loop
            await job.save();
        }
    }

    /**
     * Processing Buff filter page
     * @param page Buff Page Response
     */
    private async processPage(page: BuffPage) {
        if (!page || !page.items) {
            return;
        }

        await mapLimit(page.items, (item: BuffItem) => this.processItem(item));
    }

    /**
     * Processing Buff Item Prices & quantity
     * @param buffItem Buff item response
     */
    private async processItem(buffItem: BuffItem) {
        if (!buffItem) {
            return;
        }

        const item = await this.marketplaceService.getItemByHashName(
            buffItem.market_hash_name,
            this.appId,
        );

        if (!item) {
            // Item not found for some reason, sometimes buff have ghots items, like Major thropies
            return;
        }

        await this.marketplaceService.storePrice(
            this.buff,
            item,
            Math.round(buffItem.sell_min_price * this.CNY * 100),
            buffItem.sell_num,
        );
        await this.marketplaceService.storePrice(
            this.buffBuyorder,
            item,
            Math.round(buffItem.buy_max_price * this.CNY * 100),
            buffItem.buy_num,
        );
        await this.marketplaceService.saveCustomId(item, 'buffId', buffItem.id.toString());

    }

    /**
     *
     * @param page Page number
     * @returns return Buff Filter page response | false = no more cookies left - edge case
     */
    private async getPage(page): Promise<BuffPage | false> {
        try {
            const cookie = await this.marketplaceService.getCookie(
                CookieType.Buff163,
            );

            if (cookie === false) {
                this.queue.destroy();
                await this.marketplaceService.error(
                    new Error(
                        `No more alive cookies left for Buff.163.com, destroying queue.`,
                    ),
                    BuffCsgoBulkParser.name,
                );
                return false;
            }
            const response = (
                await lastValueFrom(
                    this.httpService.get(
                        this.url.replace('[game]', this.game).replace('[page]', page),
                        {
                            headers: {
                                Accept: '/',
                                Connection: 'keep-alive',
                                Cookie: cookie,
                            },
                        },
                    ),
                )
            ).data;
            if (response) {
                if (response.error) {
                    await this.marketplaceService.setCookieStatus(cookie);
                    return await this.getPage(page);
                }
                return response.data;

            }
        } catch (e) {
            await sleep(this.onErrorDelay);
            return await this.getPage(page);
        }

        return null;
    }
}
