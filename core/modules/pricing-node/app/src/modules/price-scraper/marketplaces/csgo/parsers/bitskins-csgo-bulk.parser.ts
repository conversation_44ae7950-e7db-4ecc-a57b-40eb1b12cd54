import { Injectable } from '@nestjs/common';
import { totp } from 'notp';
import { Cron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import * as base32 from 'thirty-two';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { BitskinsItem, BitskinsResponse } from '../models/bitskins.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class BitskinsCsgoBulkParser implements Scraper {

    private bitskins: Marketplace;

    private bitskinsLastsale: Marketplace;

    private readonly url = `https://bitskins.com/api/v1/get_price_data_for_items_on_sale/?api_key=${process.env.BITSKINS_APIKEY}&code={code}&app_id=730`;

    private readonly appId = 730;

    private readonly bitskinsMeta = {
        key: Marketplaces.Bitskins,
        name: 'Bitskins Listing Prices',
        games: ['CS:GO', 'DOTA2', 'TF2'],
    };

    private readonly bitskinsLastsaleMeta = {
        key: Marketplaces.BitskinsSale,
        name: 'Bitskins Last Sale Prices',
        games: ['CS:GO', 'DOTA2', 'TF2'],
    };

    constructor(
        private httpService: HttpService,
        private marketplaceService: MarketplaceService,
    ) { }

    async initialize() {
        this.bitskins = await this.marketplaceService.getMarketplace(
            this.bitskinsMeta,
        );
        this.bitskinsLastsale = await this.marketplaceService.getMarketplace(
            this.bitskinsLastsaleMeta,
        );
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process() {
        if (!process.env.BITSKINS_APIKEY) {
            await this.marketplaceService.error(
                new Error('BITSKINS_APIKEY not found in PROD.env/DEV.env file'),
                BitskinsCsgoBulkParser.name,
            );
        }
        if (!process.env.BITSKINS_SECRET) {
            await this.marketplaceService.error(
                new Error('BITSKINS_SECRET not found in PROD.env/DEV.env file'),
                BitskinsCsgoBulkParser.name,
            );
        }
        try {
            await mapLimit((await this.getItems()).data.items, (item: BitskinsItem) => this.processItem(item));
        } catch (e) {
            await this.marketplaceService.error(e, BitskinsCsgoBulkParser.name);
        }
    }

    /**
     * Processing item
     * @param item BitskinsItem object
     * @returns null | void
     */
    async processItem(item: BitskinsItem) {

        const dbItem = await this.marketplaceService.getItemByHashName(
            item.market_hash_name,
            this.appId,
        );

        if (!dbItem) {
            // TODO: Missing item? Upgrade item loader from steamapis to csgo files
            // console.log(`${item.item} Not found in the DB`);
            return;
        }

        await this.marketplaceService.storePrice(
            this.bitskins,
            dbItem,
            Math.round(parseFloat(item.lowest_price) * 100),
            item.total_items,
        );

        if (!item.recent_sales_info) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.bitskinsLastsale,
            dbItem,
            Math.round(parseFloat(item.recent_sales_info.average_price) * 100),
        );
    }

    /**
     * Loading Items from url
     * @returns items
     */
    async getItems(): Promise<BitskinsResponse> {
        return (
            await lastValueFrom(
                this.httpService.request({
                    method: 'POST',
                    url: this.url.replace(
                        '{code}',
                        totp.gen(base32.decode(process.env.BITSKINS_SECRET)),
                    ),
                }),
            )
        ).data;
    }
}
