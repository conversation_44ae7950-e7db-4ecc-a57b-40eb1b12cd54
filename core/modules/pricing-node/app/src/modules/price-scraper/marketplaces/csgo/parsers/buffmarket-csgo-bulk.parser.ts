import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import * as BeeQueue from 'bee-queue';
import { CookieType } from '@app/modules/database/entities/cookie/cookie.entity';
import { mapLimit, sleep } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { BuffItem, BuffPage } from '../models/buff.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class BuffMarketCsgoBulkParser implements Scraper {

    private buffMarket: Marketplace;

    private buffMarketBuyorder: Marketplace;

    private readonly url = `https://api.buff.market/api/market/goods?game=[game]&page_num=[page]&sort_by=price.desc&page_size=80`;

    private readonly game = 'csgo';

    private readonly appId = 730;

    private readonly onErrorDelay = 3000;

    private readonly buffMarketMeta = {
        key: Marketplaces.BuffMarket,
        name: 'Buff Market Listing Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    private readonly buffMarketBuyMeta = {
        key: Marketplaces.BuffMarketBuy,
        name: 'Buff Market Buy Order Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    private queue = new BeeQueue('buffmarket-csgo', {
        redis: {
            url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}/`,
        },
        removeOnSuccess: true,
        removeOnFailure: true,
    });

    constructor(
        private httpService: HttpService,
        private marketplaceService: MarketplaceService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.buffMarket = await this.marketplaceService.getMarketplace(
            this.buffMarketMeta,
        );
        this.buffMarketBuyorder = await this.marketplaceService.getMarketplace(
            this.buffMarketBuyMeta,
        );

        this.queue.process(parseInt(process.env.REQUEST_CONCURRENCY, 10), async (job) => {
            try {
                const page = await this.getPage(job.data.page);
                if (page) {
                    await this.processPage(page);
                }
            } catch (e) {
                // local server error? Db save error?
            }

            return null;
        });
    }

    /**
     * Init The Queue, fill the queue with page numbers
     */
    @Cron(process.env.PRICING_CRON)
    public async process() {

        const counts = await this.queue.checkHealth();

        /**
         * Return if theres an active process
         */
        if (counts.waiting > 0) {
            return;
        }

        const response = (await this.getPage(1)) as BuffPage;
        if (!response) {
            // Buff error
            return;
        }

        await this.processPage(response);

        const pageCount = response.total_page;
        for (let page = 2; page !== pageCount + 1; page += 1) {
            const job = this.queue.createJob({
                page,
            });
            // eslint-disable-next-line no-await-in-loop
            await job.save();
        }
    }

    /**
     * Processing Buff Market filter page
     * @param page Buff Market Page Response
     */
    private async processPage(page: BuffPage): Promise<void> {

        if (!page || !page.items) {
            return;
        }

        await mapLimit(page.items, item => this.processItem(item));
    }

    /**
     * Processing Buff Market Item Prices & quantity
     * @param buffItem Buff Market item response
     */
    private async processItem(buffItem: BuffItem): Promise<void | null> {

        if (!buffItem) {
            return;
        }

        const item = await this.marketplaceService.getItemByHashName(
            buffItem.market_hash_name,
            this.appId,
        );

        await this.marketplaceService.storePrice(
            this.buffMarket,
            item,
            Math.round(buffItem.sell_min_price * 100),
            buffItem.sell_num,
        );

        await this.marketplaceService.storePrice(
            this.buffMarketBuyorder,
            item,
            Math.round(buffItem.buy_max_price * 100),
            buffItem.buy_num,
        );

        await this.marketplaceService.saveCustomId(item, 'buffId', buffItem.id.toString());

    }

    /**
     *
     * @param page Page number
     * @returns return Buff Market Filter page response | false = no more cookies left - edge case
     */
    private async getPage(page): Promise<BuffPage | false> {
        try {
            const cookie = await this.marketplaceService.getCookie(
                CookieType.BuffMarket,
            );

            if (cookie === false) {
                this.queue.destroy();
                await this.marketplaceService.error(
                    new Error(
                        `No more alive cookies left for BuffMarket, destroying queue.`,
                    ),
                    BuffMarketCsgoBulkParser.name
                );
                return false;
            }
            const response = (
                await lastValueFrom(
                    this.httpService.get(
                        this.url.replace('[game]', this.game).replace('[page]', page),
                        {
                            headers: {
                                Accept: '/',
                                Connection: 'keep-alive',
                                Cookie: cookie,
                            },
                        },
                    ),
                )
            ).data;
            if (response) {
                if (response.error) {
                    await this.marketplaceService.setCookieStatus(cookie);
                    return await this.getPage(page);
                }
                return response.data;

            }
        } catch (e) {
            await sleep(this.onErrorDelay);
            return await this.getPage(page);
        }

        return null;
    }
}
