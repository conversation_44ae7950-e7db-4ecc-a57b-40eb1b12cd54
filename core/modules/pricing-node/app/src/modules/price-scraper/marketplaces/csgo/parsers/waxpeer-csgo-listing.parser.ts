import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { WaxpeerItem, WaxpeerResponse } from '../models/waxpeer.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class WaxpeerCsgoParser implements Scraper {

    private marketplace: Marketplace;

    private readonly appId = 730;

    private readonly endpoint = 'https://api.waxpeer.com/v1/prices/?all=1';

    private readonly meta = {
        key: Marketplaces.Waxpeer,
        name: 'Waxpeer',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(
            this.meta,
        );
    }


    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {

        const gameEntity = this.marketplace.games.find(
            (game) => game.app_id === this.appId && game.status,
        );

        // Game disabled in db
        if (!gameEntity) {
            return;
        }

        try {
            const items = (
                await lastValueFrom(
                    this.httpService.get(this.endpoint, {
                        headers: {
                            'content-type': 'application/json',
                        },
                    }),
                )
            ).data as WaxpeerResponse;

            await mapLimit(Object.keys(items), itemName => this.processItem(items[itemName]));
        } catch (e) {
            await this.marketplaceService.error(
                e,
                WaxpeerCsgoParser.name
            );
        }
    }

    /**
     * Process item object
     * @param item WaxpeerItem object
     * @returns null | void
     */
    private async processItem(item: WaxpeerItem): Promise<null | void> {

        const dbItem = await this.marketplaceService.getItemByHashName(
            item.name,
            this.appId,
        );
        if (!dbItem) {
            // TODO: Upgrade item loader from steamapis to csgo
            // https://moonrailteam.atlassian.net/browse/MP-1655
            // console.log(`${item.item} Not found in the DB`);
            return;
        }

        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            Math.round(item.min / 10), // cents
            item.count,
        );

    }
}
