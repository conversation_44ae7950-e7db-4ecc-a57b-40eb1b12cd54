import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { CSMoneyItem } from '../models/csmoney.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class CsmoneyCsgoParser implements Scraper {

    private marketplace: Marketplace;

    private readonly appId = 730;

    private readonly endpoint = 'https://cs.money/api/min_price/market/all';

    private readonly meta = {
        key: Marketplaces.CSMoney,
        name: 'CS.Money Market',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
    ) { }

    /**
     * Init Service
     */
    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(
            this.meta,
        );
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {

        const gameEntity = this.marketplace.games.find(
            (game) => game.app_id === this.appId && game.status,
        );

        // Game disabled in db
        if (!gameEntity) {
            return;
        }

        try {
            const items = (
                await lastValueFrom(
                    this.httpService.get(this.endpoint, {
                        headers: {
                            'content-type': 'application/json',
                        },
                    }),
                )
            ).data as { [key: string]: CSMoneyItem };

            await mapLimit(Object.keys(items), itemName => this.processItem(items[itemName]));
        } catch (e) {
            await this.marketplaceService.error(
                e,
                CsmoneyCsgoParser.name
            );
        }
    }

    /**
     * Process item object
     * @param item CSMoneyItem object
     * @returns null | void
     */
    private async processItem(item: CSMoneyItem): Promise<null | void> {

        let itemName = item.fullName;

        if (!itemName) {
            return;
        }

        if (itemName.includes(' Ruby (')) {
            itemName = `${itemName.replace(' Ruby', '')} - Ruby`;
        }
        if (itemName.includes(' Sapphire (')) {
            itemName = `${itemName.replace(' Sapphire', '')} - Sapphire`;
        }
        if (itemName.includes(' Emerald (')) {
            itemName = `${itemName.replace(' Emerald', '')} - Emerald`;
        }
        if (itemName.includes(' Black Pearl (')) {
            itemName = `${itemName.replace(' Black Pearl', '')} - Black Pearl`;
        }
        if (itemName.includes(' Phase 1')) {
            itemName = `${itemName.replace(' Phase 1', '')} - Phase 1`;
        }
        if (itemName.includes(' Phase 2')) {
            itemName = `${itemName.replace(' Phase 2', '')} - Phase 2`;
        }
        if (itemName.includes(' Phase 3')) {
            itemName = `${itemName.replace(' Phase 3', '')} - Phase 3`;
        }
        if (itemName.includes(' Phase 4')) {
            itemName = `${itemName.replace(' Phase 4', '')} - Phase 4`;
        }

        const dbItem = await this.marketplaceService.getItemByHashName(
            itemName,
            this.appId,
        );
        if (!dbItem) {
            // TODO: Upgrade item loader from steamapis to csgo files
            // console.log(`${item.item} Not found in the DB`);
            return;
        }

        const price = Math.round(item.price * 100);

        if (Number.isNaN(price)) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            price, // cents
            item.marketCount,
        );

    }
}
