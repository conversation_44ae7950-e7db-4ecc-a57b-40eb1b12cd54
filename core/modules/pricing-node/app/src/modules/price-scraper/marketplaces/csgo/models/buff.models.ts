export interface Exterior {
    category: string;
    id: number;
    internal_name: string;
    localized_name: string;
}

export interface Quality {
    category: string;
    id: number;
    internal_name: string;
    localized_name: string;
}

export interface Rarity {
    category: string;
    id: number;
    internal_name: string;
    localized_name: string;
}

export interface Type {
    category: string;
    id: number;
    internal_name: string;
    localized_name: string;
}

export interface Weapon {
    category: string;
    id: number;
    internal_name: string;
    localized_name: string;
}

export interface Tags {
    exterior: Exterior;
    quality: Quality;
    rarity: Rarity;
    type: Type;
    weapon: Weapon;
}

export interface Info {
    tags: Tags;
}

export interface GoodsInfo {
    icon_url: string;
    info: Info;
    item_id?: string;
    original_icon_url: string;
    steam_price: string;
    steam_price_cny: string;
}

export interface BuffItem {
    appid: number;
    bookmarked: boolean;
    buy_max_price: number;
    buy_num: number;
    can_bargain: boolean;
    can_search_by_tournament: boolean;
    description?: string;
    game: string;
    goods_info: GoodsInfo;
    has_buff_price_history: boolean;
    id: number;
    market_hash_name: string;
    market_min_price: string;
    name: string;
    quick_price: string;
    sell_min_price: number;
    sell_num: number;
    sell_reference_price: string;
    short_name: string;
    steam_market_url: string;
    transacted_num: number;
}

export interface BuffPage {
    items: BuffItem[];
    page_num: number;
    page_size: number;
    total_count: number;
    total_page: number;
}

export interface BuffResponse {
    code: string;
    data: BuffPage;
    msg?: string;
}
