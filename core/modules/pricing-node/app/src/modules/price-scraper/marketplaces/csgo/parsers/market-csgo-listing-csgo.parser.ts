import { Game } from '@app/modules/database/entities/game/game.entity';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { mapLimit } from '@app/helpers/common.helper';
import { MarketCSGOListingItem, MarketCSGOListingResponse } from '../models/market-csgo-listing.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class MarketcsgoListingCsgoParser implements Scraper {
    private marketplace: Marketplace;

    private readonly appId = 730;

    private readonly endpoint = 'https://market.csgo.com/api/v2/prices/USD.json';

    private readonly meta = {
        key: Marketplaces.MarketCSGO,
        name: 'Market.CSGO Listing Prices',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
    ) { }

    /**
     * Init service
     */
    async initialize() {
        this.marketplace = await this.marketplaceService.getMarketplace(
            this.meta,
        );
    }

    /**
     * Load Prices from the marketplace
     */
    @Cron(process.env.PRICING_CRON)
    public async process(): Promise<void> {
        const gameEntity = this.marketplace.games.find(
            (game: Game) => game.app_id === this.appId && game.status,
        );

        // Game disabled in db
        if (!gameEntity) {
            return;
        }

        try {
            const { items } = (await lastValueFrom(this.httpService.get(this.endpoint))).data as MarketCSGOListingResponse;

            await mapLimit(items, item => this.processItem(item));
        } catch (e) {
            await this.marketplaceService.error(
                e,
                MarketcsgoListingCsgoParser.name
            );
        }
    }

    /**
     * Processing item object
     * @param item MarketCSGOListingItem object
     * @returns null | void
     */
    private async processItem(item: MarketCSGOListingItem): Promise<void | null> {
        const dbItem = await this.marketplaceService.getItemByHashName(
            item.market_hash_name,
            this.appId,
        );
        if (!dbItem) {
            // TODO: Upgrade item loader from steamapis to csgo files
            return;
        }
        await this.marketplaceService.storePrice(
            this.marketplace,
            dbItem,
            Math.round((Math.ceil((parseFloat(item.price) * 1000) / 5) * 5 * 100) / 1000), // to normalize prices
            parseInt(item.volume, 10),
        );
    }
}
