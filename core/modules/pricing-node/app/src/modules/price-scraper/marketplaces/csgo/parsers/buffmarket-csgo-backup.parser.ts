import { Injectable, Logger } from '@nestjs/common';
import { Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import * as BeeQueue from 'bee-queue';
import { BuffCsgoBackupParser } from './buff-csgo-backup.parser';

@Injectable()
export class BuffMarketCsgoBackupParser extends BuffCsgoBackupParser {

    protected logger = new Logger(BuffMarketCsgoBackupParser.name);

    protected buffItemIdKey: string = 'buffMarketId';

    protected endpoint = 'https://api.buff.market/api/market/goods/info?game=csgo&goods_id={goodsId}';

    protected apiHost = 'buff.market';

    protected readonly buffMeta = {
        key: Marketplaces.BuffMarket,
        name: 'Buff Market Listing Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    protected readonly buffBuyMeta = {
        key: Marketplaces.BuffMarketBuy,
        name: 'Buff Market Buy Order Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    protected queue = new BeeQueue('buffmarket-backup-csgo', {
        redis: {
            url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}/`,
        },
        removeOnSuccess: true,
        removeOnFailure: true,
    });

    /**
     * Load CNY currency
     */
    // eslint-disable-next-line class-methods-use-this
    protected async loadCurrency() {
        // do not load as here we are using USD
    }
}
