import { ItemId } from '@app/modules/database/entities/item/item-id.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { CurrenciesService } from '@app/modules/currencies/currencies.service';
import { mapLimit, sleep } from '@app/helpers/common.helper';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class BuffNewPhasesCsgoParser implements Scraper {

    private readonly logger = new Logger(BuffNewPhasesCsgoParser.name);

    private readonly endpoint = 'https://buff.163.com/api/market/goods/info?goods_id={goodsId}';

    private readonly appId = 730;

    private buff: Marketplace;

    private readonly buffMeta = {
        key: Marketplaces.Buff,
        name: 'Buff Listing Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    private readonly buffBuyMeta = {
        key: Marketplaces.BuffBuy,
        name: 'Buff Buy Order Prices',
        games: ['CS:GO', 'DOTA2', 'TF2', 'RUST'],
    };

    private buffBuyorder: Marketplace;

    private CNY: number;

    constructor(
        private marketplaceService: MarketplaceService,
        private httpService: HttpService,
        private currenciesService: CurrenciesService,
    ) { }

    async initialize() {

        // delay the initialize by 5s to avoid issues with getMarketplace
        await sleep(5000);

        this.buff = await this.marketplaceService.getMarketplace(this.buffMeta);
        this.buffBuyorder = await this.marketplaceService.getMarketplace(this.buffBuyMeta);

        await this.loadCurrency();
    }

    /**
     * Starting the process
     */
    @Cron('0 */15 * * * *')
    async process() {
        await this.loadCurrency();
        const dopplers = await this.marketplaceService.getDopplers();
        await mapLimit(dopplers, async (item) => {
            if (item.market_hash_name.includes(' - ')) {
                return;
            }

            const id = item.ids.find((itemId: ItemId) => itemId.key === 'buffId');

            if (!id) {
                return;
            }

            await this.processDoppler(item.market_hash_name, id.value);
        }, 1);
    }

    /**
     * Load CNY currency
     */
    private async loadCurrency() {
        this.CNY = 1 / (await this.currenciesService.getCurrency('CNY')).rate;
    }

    /**
     * Processing dopplers
     * @param itemName Item market hash name
     * @param buffId Buff id (int)
     * @returns
     */
    private async processDoppler(itemName: string, buffId: string) {

        const itemInfo = await this.getData(buffId);

        if (
            !itemInfo ||
            !itemInfo.data ||
            !itemInfo.data.goods_info ||
            !itemInfo.data.goods_info.specific
        ) {
            return;
        }

        await Promise.all(
            itemInfo.data.goods_info.specific.map(
                async (specific) => {

                    if (!specific || !specific.items) {
                        return;
                    }

                    await specific.items.map(specificItem => this.saveSpecific(itemName, specificItem));
                }
            ).flat().filter(i => i) /** Remove nulls */
        );

    }

    /**
     * Saving doppler specific items
     * @param itemName Market Hash Name
     * @param specificItem object
     */
    private async saveSpecific(itemName, specificItem) {

        const extra = specificItem.name.replace('Phase', 'Phase ');

        const dopplerItem = await this.marketplaceService.getItemByHashName(
            `${itemName} - ${extra}`,
            this.appId,
        );

        await this.marketplaceService.storePrice(
            this.buff,
            dopplerItem,
            Math.round(specificItem.sell_min_price * this.CNY * 100)
        );

        await this.marketplaceService.storePrice(
            this.buffBuyorder,
            dopplerItem,
            Math.round(specificItem.buy_max_price * this.CNY * 100)
        );
    }

    /**
     * Getting Buff response for doppler item
     * @param buffId int
     * @returns Buff response
     */
    async getData(buffId) {
        try {
            return (
                await lastValueFrom(
                    this.httpService.get(this.endpoint.replace('{goodsId}', buffId), {
                        headers: {
                            Cookie: 'Locale-Supported=en',
                        },
                    }),
                )
            ).data;
        } catch (e) {
            this.logger.error(`Failed to fetch buff ${buffId} ${e.message}`);
            await sleep(2000);
            return this.getData(buffId);
        }
    }
}
