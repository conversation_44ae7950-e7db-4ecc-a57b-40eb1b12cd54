

import { Marketplace, Marketplaces } from '@app/modules/database/entities/marketplace/marketplace.entity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import * as nacl from 'tweetnacl';
import { mapLimit } from '@app/helpers/common.helper';
import { DmarketItem } from '../models/dmarket.models';
import { Scraper } from '../../../scraper.interface';

@Injectable()
export class DmarketParser implements Scraper {

    private dmarket: Marketplace;

    private dmarketBuy: Marketplace;

    private readonly appId = 730;

    private host = 'https://api.dmarket.com';

    private readonly dmarketMeta = {
        key: Marketplaces.Dmarket,
        name: 'Dmarket.com Listing Prices',
        games: ['CS:GO'],
    };

    private readonly dmarketBuyMeta = {
        key: Marketplaces.DmarketBuy,
        name: 'Dmarket.com Buy order Prices',
        games: ['CS:GO'],
    };

    constructor(
        private readonly marketplaceService: MarketplaceService,
        private readonly httpService: HttpService,
    ) { }

    /**
     * Init service
     */
    async initialize() {
        this.dmarket = await this.marketplaceService.getMarketplace(
            this.dmarketMeta,
        );
        this.dmarketBuy = await this.marketplaceService.getMarketplace(
            this.dmarketBuyMeta,
        );
    }

    /*
    * Starting the process
    */
    @Cron(process.env.PRICING_CRON)
    async process() {
        await this.processPage(1);
    }

    async processPage(page: number) {
        const prices = await this.getPage(page);

        if (prices.AggregatedTitles.length === 0) {
            return;
        }

        await mapLimit(prices.AggregatedTitles, item => this.processItem(item));
        await this.processPage(page + 1);
    }

    async processItem(item: DmarketItem) {

        if (item.Offers.Count === 0 || item.GameID !== 'a8db') {
            return;
        }

        const dbItem = await this.marketplaceService.getItemByHashName(
            item.MarketHashName,
            this.appId,
        );

        if (!dbItem) {
            // TODO: Upgrade item loader from steamapis to csgo files
            return;
        }

        await this.marketplaceService.storePrice(
            this.dmarket,
            dbItem,
            Math.round(item.Offers.BestPrice * 100),
            item.Offers.Count,
        );

        if (item.Orders.Count === 0) {
            return;
        }

        await this.marketplaceService.storePrice(
            this.dmarketBuy,
            dbItem,
            Math.round(item.Orders.BestPrice * 100),
            item.Orders.Count,
        );

    }

    async getPage(page: number) {
        const path = `/price-aggregator/v1/aggregated-prices?gameId=a8db&Limit=10000&Offset=${10000 * page - 10000}`;
        const headers = this.getHeaders('GET', path);
        return (
            await lastValueFrom(
                this.httpService.get(`${this.host}${path}`, {
                    headers,
                }),
            )
        ).data;
    }

    // eslint-disable-next-line class-methods-use-this
    private byteToHexString(uint8arr) {
        if (!uint8arr) {
            return '';
        }

        let hexStr = '';
        const radix = 16;
        const magicNumber = 0xff;
        for (let i = 0; i < uint8arr.length; i += 1) {
            // eslint-disable-next-line no-bitwise
            let hex = (uint8arr[i] & magicNumber).toString(radix);
            hex = hex.length === 1 ? `0${hex}` : hex;
            hexStr += hex;
        }

        return hexStr;
    }

    // eslint-disable-next-line class-methods-use-this
    private hexStringToByte(str) {
        if (typeof str !== 'string') {
            throw new TypeError(
                'Wrong data type passed to convertor. Hexadecimal string is expected',
            );
        }
        const twoNum = 2;
        const radix = 16;
        const uInt8arr = new Uint8Array(str.length / twoNum);
        for (let i = 0, j = 0; i < str.length; i += twoNum, j += 1) {
            uInt8arr[j] = parseInt(str.substr(i, twoNum), radix);
        }
        return uInt8arr;
    }

    private sign(string) {
        const signatureBytes = nacl.sign(
            new TextEncoder().encode(string),
            this.hexStringToByte(process.env.DMARKET_SECRET),
        );
        return this.byteToHexString(signatureBytes).substr(0, 128);
    }

    private getHeaders(method, path) {
        const timestamp = Math.floor(new Date().getTime() / 1000);
        const stringToSign = method + path + timestamp;
        const signature = this.sign(stringToSign);

        return {
            'X-Api-Key': process.env.DMARKET_PUB,
            'X-Request-Sign': `dmar ed25519 ${signature}`,
            'X-Sign-Date': timestamp,
            'Content-Type': 'application/json',
        };
    }
}
