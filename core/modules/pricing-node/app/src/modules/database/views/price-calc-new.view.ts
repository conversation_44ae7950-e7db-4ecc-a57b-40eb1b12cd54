import { ViewEntity, ViewColumn, Index } from 'typeorm';

const expression = `
SELECT DISTINCT ON (item_id, marketplace_key)
        count,
        item_id,
        marketplace_key,
        min_price,
        max_price,
        avg_price as avg_price,
        lag_price,
        price As current_price,
        item.market_hash_name,
        item.paint_id,
        item.weapon_id,
        created_at
    FROM (
        SELECT
            item_id,
            marketplace_key,
            price,
            count,
            created_at,
            MIN(price) OVER (PARTITION BY item_id, marketplace_key ORDER BY created_at) AS min_price,
            MAX(price) OVER (PARTITION BY item_id, marketplace_key ORDER BY created_at) AS max_price,
            AVG(price) OVER (PARTITION BY item_id, marketplace_key ORDER BY created_at)::integer AS avg_price,
            LAG(price) OVER (PARTITION BY item_id, marketplace_key ORDER BY created_at) AS lag_price
        FROM item_price
    ) AS t1
    LEFT JOIN
        item ON item.id = t1.item_id
    GROUP BY
        id, item_id, marketplace_key, price, count, created_at, lag_price, min_price, max_price, avg_price
    ORDER BY
        item_id, marketplace_key, created_at DESC
`;

// console.log('expression', expression);

@ViewEntity({
    synchronize: true,
    materialized: true,
    expression,
})
@Index(['item_id', 'marketplace_key'], { unique: true })
@Index(['created_at', 'market_hash_name'])
export class PriceCalcNew {

    @ViewColumn()
    current_price: number;

    @ViewColumn()
    min_price: number;

    @ViewColumn()
    max_price: number;

    @ViewColumn()
    avg_price: number;

    @ViewColumn()
    lag_price: number;

    @ViewColumn()
    count: number;

    @ViewColumn()
    item_id: number;

    @Index()
    @ViewColumn()
    marketplace_key: string;

    @ViewColumn()
    market_hash_name: string;

    @ViewColumn()
    paint_id: number;

    @ViewColumn()
    weapon_id: number;

    @ViewColumn()
    created_at: Date;
}
