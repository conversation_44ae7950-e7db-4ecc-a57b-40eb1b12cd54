import { ViewEntity, ViewColumn, Index } from 'typeorm';

@ViewEntity({
    synchronize: true,
    materialized: true,
    expression: `
        SELECT
            mp.marketplace_key,
            ip.item_id,
            DATE ( ip.created_at ) AS DATE,
            MIN ( ip.price ) AS min_price,
            MAX ( ip.price ) AS max_price,
            AVG ( ip.price )::integer AS avg_price,
            SUM ( ip.count)::integer As count,
            lp.close_price AS close_price 
        FROM
            (
            SELECT
                marketplace_key,
                item_id,
                DATE_TRUNC( 'day', created_at ) AS DATE,
                MAX ( created_at ) AS last_created_at 
            FROM
                item_price 
            WHERE
                created_at >= CURRENT_DATE - INTERVAL '30 days' 
            GROUP BY
                marketplace_key,
                item_id,
                DATE_TRUNC( 'day', created_at ) 
            ) AS mp
            JOIN item_price AS ip ON ip.marketplace_key = mp.marketplace_key 
            AND ip.item_id = mp.item_id 
            AND ip.created_at = mp.last_created_at
            LEFT JOIN LATERAL (
            SELECT
                price AS close_price 
            FROM
                item_price 
            WHERE
                marketplace_key = mp.marketplace_key 
                AND item_id = mp.item_id 
                AND DATE_TRUNC( 'day', created_at ) = mp.DATE 
            ORDER BY
                created_at DESC 
                LIMIT 1 
            ) AS lp ON TRUE 
        GROUP BY
            mp.marketplace_key,
            ip.item_id,
            DATE ( ip.created_at ),
            lp.close_price 
        ORDER BY
            mp.marketplace_key,
            ip.item_id,
            DATE ( ip.created_at );
    `,
})
@Index(['item_id', 'marketplace_key', 'date'], { unique: true })
export class PriceHistory {
    @ViewColumn()
    item_id: number;

    @ViewColumn()
    marketplace_key: string;

    @ViewColumn()
    close_price: number;

    @ViewColumn()
    avg_price: number;

    @ViewColumn()
    min_price: number;

    @ViewColumn()
    max_price: number;

    @ViewColumn()
    date: Date;

    @ViewColumn()
    count: number;
}
