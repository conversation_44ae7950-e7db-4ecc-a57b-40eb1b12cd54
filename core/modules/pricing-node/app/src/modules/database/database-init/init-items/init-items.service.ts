import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { lastValueFrom } from 'rxjs';
import { Repository } from 'typeorm';
import { MarketplaceService } from '@app/modules/marketplace/marketplace.service';
import { phases, mapLimit } from '@app/helpers/common.helper';
import { Cron } from '@nestjs/schedule';
import * as marketplaceIds from '@app/assets/marketplace_ids.json';
import { Item } from '../../entities/item/item.entity';
import { Game } from '../../entities/game/game.entity';

@Injectable()
export class InitItemsService {
	private readonly imageUrl = 'https://api.steamapis.com/image/items/730';

	private readonly metaUrl = 'https://csgofloat.com/api/v1/schema';

	private readonly appId = 730;

	constructor(
		@InjectRepository(Item) private itemRepository: Repository<Item>,
		@InjectRepository(Game) private gameRepository: Repository<Game>,
		private marketplaceService: MarketplaceService,
		private httpService: HttpService,
	) { }

	/**
	 * Saving Item to db
	 * @param marketHashName Item Name
	 * @param image Item Image
	 * @param game Game Object
	 */
	private async saveItem(
		item: {
			name: string,
			image: string,
			paintId: number,
			weaponId: number
		},
		game: Game
	): Promise<void> {
		if (!item || !item.image || !item.name) {
			return;
		}

		const already = await this.itemRepository.findOne({
			where: {
				paint_id: item.paintId,
				market_hash_name: item.name,
				game,
			}
		});

		if (already) {
			return;
		}

		await this.itemRepository.save(
			{
				market_hash_name: item.name,
				image_hash: item.image
					.replace(
						'https://community.cloudflare.steamstatic.com/economy/image/',
						'',
					)
					.replace(
						'https://steamcommunity-a.akamaihd.net/economy/image/',
						'',
					),
				game,
				paint_id: item.paintId,
				weapon_id: item.weaponId,
			}
		);
	}

	/**
	 * Initialize Items for selected Apps (mostly need market_hash_name & image)
	 * @returns void
	 * @cron runs every day at 00:00:00
	*/
	@Cron('0 0 0 * * *')
	public async initItems(): Promise<void> {
		const images = await (
			await lastValueFrom(
				this.httpService.get(this.imageUrl),
			)
		).data;

		const metaData = await (
			await lastValueFrom(
				this.httpService.get(this.metaUrl),
			)
		).data;

		const game = await this.gameRepository.findOne({
			where: {
				app_id: 730
			}
		});

		const items = [];
		/**
		 * Generating Weapon skin items
		 */
		Object.keys(metaData.weapons).forEach((weaponId: string) => {
			const weapon = metaData.weapons[weaponId];
			const star = ['Knives', 'Gloves'];

			let prefix = '';
			if (star.includes(weapon.type)) {
				prefix = '★ ';
			}
			Object.keys(weapon.paints).forEach(paintId => {
				const paint = weapon.paints[paintId];
				const wears = this.getWears(paint.min, paint.max);
				wears.forEach(wear => {

					const name = this.generateName(weapon.name, paint.name, wear);

					let prefixed = `${prefix}${name}`;

					if (paint.name.includes('Vanilla')) {
						if (wear === 'Factory New') {
							const vanillaName = `${prefix}${weapon.name}`;
							const stVanillaName = `${prefix}StatTrak™ ${weapon.name}`;
							items.push({
								name: vanillaName,
								image: images[vanillaName] || paint.image,
								paintId: parseInt(paintId, 10),
								weaponId: parseInt(weaponId, 10)
							});

							if (paint.stattrak) {
								items.push({
									name: stVanillaName,
									image: images[stVanillaName] || paint.image,
									paintId: parseInt(paintId, 10),
									weaponId: parseInt(weaponId, 10)
								});
							}
						}
						/**
						 * We should return since we dont want duplicated items in the array
						 */
						return;
					}

					items.push({
						name: prefixed,
						image: images[prefixed] || paint.image,
						paintId: parseInt(paintId, 10),
						weaponId: parseInt(weaponId, 10)
					});

					if (paint.stattrak) {
						prefixed = `${prefix}StatTrak™ ${name}`;
						items.push({
							name: `${prefix}StatTrak™ ${name}`,
							image: images[prefixed] || paint.image,
							paintId: parseInt(paintId, 10),
							weaponId: parseInt(weaponId, 10)
						});
					}
					if (paint.souvenir) {
						prefixed = `Souvenir ${name}`;
						items.push({
							name: prefixed,
							image: images[prefixed] || paint.image,
							paintId: parseInt(paintId, 10),
							weaponId: parseInt(weaponId, 10)
						});
					}
				});
			});

		});

		/**
		 * Adding Stickers
		 */
		items.push(...Object.values(metaData.stickers).map((metadata) => {
			const name = `Sticker | ${metadata}`;
			return {
				name,
				image: images[name],
				paintId: 0,
				weaponId: 0,
			};
		}));

		/**
		 * Adding leftovers (Patches, capsules, agents)
		 */
		items.push(...Object.keys(images).map((name) => {
			if (
				!items.find(item => item.name === name)
				// && name.includes('Doppler') // removed, we should save the Doppler itself since we need buffId's for them later
			) {
				return {
					name,
					image: images[name],
					paintId: 0,
					weaponId: 0,
				};
			}

			return null;
		}).filter(i => i) /* Remove nulls */);


		await mapLimit(items, (item: {
			name: string,
			image: string,
			paintId: number,
			weaponId: number
		}) => this.saveItem(item, game));

		await this.assignIds();
	}

	/**
	 * Assign initial ids to items
	 */
	private async assignIds() {
        await mapLimit(Object.entries(marketplaceIds), async ([itemName, items]) => {
            await Promise.all(items.map(({ name, id }) => this.saveCustomId(itemName, name, id)));
        });
	}

    private async saveCustomId(itemName: string, name: string, id: string) {
		await this.marketplaceService.saveCustomId(itemName, name, id, this.appId);
    }

	/**
	 * Generate Item name
	 * @param weaponName string
	 * @param paintName string
	 * @param wear string
	 * @returns string
	 */
	// eslint-disable-next-line class-methods-use-this
	private generateName(weaponName: string, paint: string, wear: string) {
		const phase = phases.find(p => paint.includes(`(${p})`));
		const paintName = phase ? paint.replace(` (${phase})`, '') : paint;

		return `${weaponName} | ${paintName} (${wear})${phase ? ` - ${phase}` : ''}`;
	}

	/**
	 * Getting wears by min & max float value
	 * @param floatMin float
	 * @param floatMax float
	 * @returns string[]
	 */
	// eslint-disable-next-line class-methods-use-this
	private getWears(floatMin: number, floatMax: number): string[] {
		const wears = [];
		if (floatMin <= 0.07) {
			wears.push('Factory New');
		}
		if (floatMin <= 0.15 && floatMax >= 0.07) {
			wears.push('Minimal Wear');
		}
		if (floatMin <= 0.399 && floatMax >= 0.15) {
			wears.push('Field-Tested');
		}
		if (floatMin <= 0.45 && floatMax >= 0.38) {
			wears.push('Well-Worn');
		}
		if (floatMin <= 0.45 && floatMax >= 0.45) {
			wears.push('Battle-Scarred');
		}
		return wears;
	}
}
