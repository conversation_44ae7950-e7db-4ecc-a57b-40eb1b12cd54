import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InitModifiersService } from './init-modifiers.service';
import { Modifier } from '../../entities/modifier/modifier.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([Modifier])
	],
	providers: [
		InitModifiersService
	],
	exports: [
		InitModifiersService
	],
})
export class InitModifiersModule { }
