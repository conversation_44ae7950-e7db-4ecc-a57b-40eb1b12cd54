import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpInterceptModule } from '@app/modules/http-intercept/http-intercept.module';
import { InitGamesService } from './init-games.service';
import { Game } from '../../entities/game/game.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([Game]),
		HttpInterceptModule
	],
	providers: [InitGamesService],
	exports: [InitGamesService],
})
export class InitGamesModule { }
