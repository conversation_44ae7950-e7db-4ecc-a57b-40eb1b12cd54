import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Game } from '../entities/game/game.entity';
import { Item } from '../entities/item/item.entity';
import { Marketplace } from '../entities/marketplace/marketplace.entity';
import { DatabaseInitService } from './database-init.service';
import { InitGamesModule } from './init-games/init-games.module';
import { InitItemsModule } from './init-items/init-items.module';
import { InitModifiersModule } from './init-modifiers/init-modifiers.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([Game, Item, Marketplace]),
		InitGamesModule,
		InitItemsModule,
		InitModifiersModule,
	],
	providers: [DatabaseInitService],
})
export class DatabaseInitModule { }
