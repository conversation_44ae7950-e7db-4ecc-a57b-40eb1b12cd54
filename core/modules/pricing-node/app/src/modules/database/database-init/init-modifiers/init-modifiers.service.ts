import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import { Modifier, ModifierType, ModifierDir } from '../../entities/modifier/modifier.entity';

@Injectable()
export class InitModifiersService implements OnApplicationBootstrap {

	private readonly modifiers = [
		{
			name: 'Steam -20%',
			type: ModifierType.marketplaceKey,
			dir: ModifierDir.Equal,
			rule: 'steam',
			modifier: 0.8,
		},
		{
			name: '0-0,50 Lowball',
			type: ModifierType.Price,
			dir: ModifierDir.Between,
			rule: '0',
			rule2: '50',
			modifier: 0.88,
		},
		{
			name: '0,50-1,00 Lowball',
			type: ModifierType.Price,
			dir: ModifierDir.Between,
			rule: '50',
			rule2: '100',
			modifier: 0.92,
		},
		{
			name: '1,00-10,00 Lowball',
			type: ModifierType.Price,
			dir: ModifierDir.Between,
			rule: '100',
			rule2: '1000',
			modifier: 0.93,
		},
		{
			name: '>10,00 Lowball',
			type: ModifierType.Price,
			dir: ModifierDir.GreaterThanOrEqual,
			rule: '1000',
			modifier: 0.95,
		},
	];

	constructor(
		@InjectRepository(Modifier) private modifierRepository: Repository<Modifier>,
	) { }


	async onApplicationBootstrap() {
		await this.modifierRepository.delete({
			name: Not(In(this.modifiers.map(({ name }) => name)))
		});

		await this.modifierRepository.upsert(this.modifiers, ['name']);
	}

}
