import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { InitGamesService } from './init-games/init-games.service';
import { InitItemsService } from './init-items/init-items.service';

@Injectable()
export class DatabaseInitService implements OnApplicationBootstrap {
	constructor(
		private readonly initGamesService: InitGamesService,
		private readonly initItemsService: InitItemsService,
	) { }

	/**
	 * Initialize Default DB values, games, items.
	 */
	async onApplicationBootstrap() {
		await this.initGamesService.initGames();
		await this.initItemsService.initItems();
	}
}
