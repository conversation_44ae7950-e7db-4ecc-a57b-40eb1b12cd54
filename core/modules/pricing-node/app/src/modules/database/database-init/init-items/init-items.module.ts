import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MarketplaceModule } from '@app/modules/marketplace/marketplace.module';
import { HttpInterceptModule } from '@app/modules/http-intercept/http-intercept.module';
import { Item } from '../../entities/item/item.entity';
import { InitItemsService } from './init-items.service';
import { Game } from '../../entities/game/game.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([Item, Game]),
		HttpInterceptModule,
		MarketplaceModule
	],
	providers: [
		InitItemsService,
	],
	exports: [
		InitItemsService,
	],
})
export class InitItemsModule { }
