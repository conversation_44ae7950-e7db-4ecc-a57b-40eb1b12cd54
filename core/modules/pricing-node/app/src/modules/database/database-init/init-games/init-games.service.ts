import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { mapLimit } from '@app/helpers/common.helper';
import { Game } from '../../entities/game/game.entity';

@Injectable()
export class InitGamesService {
	private readonly gameMetas = [
		{
			app_id: 730,
			short_name: 'CS:GO',
			long_name: 'Counter-Strike: Global Offensive',
			status: true,
		},
		/*
		{
			app_id: 440,
			short_name: 'TF2',
			long_name: 'Team Fortress 2',
			status: true,
		},
		{
			app_id: 570,
			short_name: 'DOTA2',
			long_name: 'DOTA 2',
			status: true,
		},
		{
			app_id: 252490,
			short_name: 'RUST',
			long_name: 'RUST',
			status: true,
		},
		*/
	];

	constructor(
		@InjectRepository(Game) private gameRepository: Repository<Game>,
	) { }

	/**
	 * Initialize games from .env file
	 */
	public async initGames(): Promise<void> {
        await mapLimit(this.gameMetas, gameMeta => this.gameRepository.save(gameMeta));
	}
}
