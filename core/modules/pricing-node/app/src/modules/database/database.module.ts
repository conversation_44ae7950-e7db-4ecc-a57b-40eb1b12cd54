import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, OnModuleInit, OnApplicationShutdown } from '@nestjs/common';
import { InjectDataSource, TypeOrmModule, } from '@nestjs/typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { <PERSON>ron } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { DatabaseInitModule } from './database-init/database-init.module';
import { Game } from './entities/game/game.entity';
import { ItemId } from './entities/item/item-id.entity';
import { ItemPrice, ACTIVE_ITEM_PRICE_TIMEFRAME_DAYS } from './entities/item/item-price.entity';
import { Item } from './entities/item/item.entity';
import { Marketplace } from './entities/marketplace/marketplace.entity';
import { MarketplaceLog } from './entities/marketplace/marketplace-log.entity';
import { Currency } from './entities/currency/currency.entity';
import { <PERSON>ie } from './entities/cookie/cookie.entity';
import { PriceHistory } from './views/price-history.view';
import { Modifier } from './entities/modifier/modifier.entity';
import { PriceCalcNew } from './views/price-calc-new.view';

const logger = new Logger('DatabaseModule');

@Module({
    imports: [
        TypeOrmModule.forRootAsync({
            // Use useFactory, useClass, or useExisting
            // to configure the DataSourceOptions.
            useFactory: () => ({
                type: 'postgres',
                synchronize: true,
                host: process.env.POSTGRES_HOST,
                port: parseInt(process.env.POSTGRES_PORT, 10),
                username: process.env.POSTGRES_USER,
                password: process.env.POSTGRES_PASSWORD,
                database: process.env.POSTGRES_DB,
                schema: 'public',
                poolErrorHandler: (err) => {
                    logger.error('DATABASE POOL ERROR', err);
                },
                poolSize: process.env.POSTGRES_POOL_SIZE ? parseInt(process.env.POSTGRES_POOL_SIZE, 10) : 150,
                extra: {
                    idleTimeoutMillis: 300,
                    allowExitOnIdle: false,
                    // initialConnections: 100,
                    // retryConnectionMaxRetries: 100,
                    // retryConnectionWaitMillis: 100,
                    // keepAlive: true,
                    // keepAliveInitialDelayMillis: 120000,
                    // connectionTimeoutMillis: 60000,
                    maxUses: 5000,
                    log: (l) => {
                        if (['no queued requests', 'pulse queue'].includes(l)) return;

                        const params = [`Postgres - ${l}`];

                        if (l === 'ending') {
                            params.push(new Error(l).stack);
                        }

                        // eslint-disable-next-line prefer-spread
                        logger.verbose.apply(logger, params);
                    },
                },
                entities: [
                    Game,
                    Marketplace,
                    MarketplaceLog,
                    Item,
                    ItemId,
                    ItemPrice,
                    Currency,
                    Cookie,
                    Modifier,

                    // Prices,
                    PriceHistory,
                    PriceCalcNew,

                    // __dirname + './entities/*.{entity|view}.{js,ts}',
                ],
                migrationsTableName: 'migration',
                migrations: ['src/migration/*.ts'],
                namingStrategy: new SnakeNamingStrategy(),
                logNotifications: true,
                maxQueryExecutionTime: 3000,
                logging: [
                    // "query",
                    "error",
                    "schema",
                    "warn",
                    "info",
                    "log",
                ],
            }),
            // dataSource receives the configured DataSourceOptions
            // and returns a Promise<DataSource>.
            dataSourceFactory: async (options) => {
                logger.verbose('CREATING DATASOURCE!');
                const dataSource = await new DataSource(options).initialize();
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const d: any = dataSource.manager.connection.driver;
                d.master.on('error', (error) => {
                    logger.error('DB POOL master error', error);
                });
                // d.master.on('acquire', (c) => {
                //     logger.verbose('DB POOL master acquire, pool size', c._poolUseCount, d.master.totalCount, d.master.idleCount, d.master.waitingCount)
                // });
                d.master.on('remove', (c) => {
                    // eslint-disable-next-line no-underscore-dangle
                    logger.verbose(`DB POOL master remove - use count: ${c._poolUseCount}`);
                });
                // d.master.on('release', (c) => {
                //     logger.verbose('DB POOL master release')
                // });
                d.master.on('connect', (/* c */) => {
                    logger.verbose('DB POOL master connect');
                    // c.on('error', (error) => {
                    //     logger.verbose('DB POOL CLIENT error', error);
                    // });
                });
                // console.log(d.master);

                // d.master.client.on('error', (err) => {
                //     console.error('DB POOL CLIENT something bad has happened!', err);
                // });

                // const pg = dataSource.manager.connection.driver.postgres;
                // pg.on('error', (error) => {
                //     console.log('postgres error', error)
                // });
                return dataSource;
            },
        }),
        DatabaseInitModule,
    ],
})
export class DatabaseModule implements OnModuleInit, OnApplicationShutdown {
    private readonly logger = logger;

    constructor(
        @InjectDataSource() private dataSource: DataSource
    ) {

    }

    onApplicationShutdown(signal: string) {
        this.logger.warn('SHUTDOWN SIGNAL', signal); // e.g. "SIGINT"
    }

    async onModuleInit() {
        this.logger.debug('Init database module');

        await this.pingDatasource();
        await this.updateMaterializedViews();
    }

    @Cron('* * * * *')
    async pingDatasource() {
        this.logger.debug('Ping datasource');
        if (!this.dataSource.manager.connection.isInitialized) {
            this.logger.warn('Re-Initializing database connection');
            await this.dataSource.manager.connection.initialize();
        }

        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await this.dataSource.query('SELECT 1');
        await queryRunner.release();
    }

    @Cron(process.env.MATERIALIZED_VIEW_CRON)
    async updateMaterializedViews() {
        this.cleanup();

        const startedAt = Date.now();
        this.logger.debug('Refresh views...');
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        // Refreshing prices
        await this.dataSource.query('REFRESH MATERIALIZED VIEW CONCURRENTLY "price_calc_new"');

        // disabled due to heavy query, needs to be optimized before enabling
        // TODO: https://moonrailteam.atlassian.net/browse/MP-2368
        // await this.dataSource.query('REFRESH MATERIALIZED VIEW CONCURRENTLY "price_history"');
        await queryRunner.release();

        this.logger.debug(`Views refreshed in ${Date.now() - startedAt} milliseconds`);
    }

    async cleanup() {
        const startedAt = Date.now();
        this.logger.debug('Cleanup database...');
        await this.dataSource
            .createQueryBuilder()
            .delete()
            .from(ItemPrice)
            .where(`created_at <=  NOW() - INTERVAL '${ACTIVE_ITEM_PRICE_TIMEFRAME_DAYS} days'`)
            .execute();
        this.logger.debug(`Database cleanup done in ${Date.now() - startedAt} milliseconds`);
    }
}
