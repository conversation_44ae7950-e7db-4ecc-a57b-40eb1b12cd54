import {
    Column,
    Entity,
    Index,
    PrimaryGeneratedColumn,
} from 'typeorm';


export enum ModifierType {
    Price = 'current_price',
    marketplaceKey = 'marketplace_key',
    Regex = 'regex',
    ListingCount = 'count',
    Liquidity = 'liquidity',
    RSI = 'rsi',
    Name = 'market_hash_name',
}

export enum ModifierDir {
    LowerThan = 'lower',
    LowerThanOrEqual = 'lowerOrEqual',
    Between = 'between',
    GreaterThan = 'greater',
    GreaterThanOrEqual = 'greaterOrEqual',
    Include = 'include',
    NotInclude = 'not-include',
    Equal = 'equal',
}

@Entity()
export class Modifier {
    @Index()
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        unique: true,
    })
    name: string;

    @Column()
    type: ModifierType;

    @Column()
    dir: ModifierDir;

    @Column()
    rule: string;

    @Column({
        nullable: true
    })
    rule2: string;

    @Column({
        nullable: true,
        type: 'float',
    })
    modifier: number;

    @Index()
    @Column({
        default: true
    })
    status: boolean;
}
