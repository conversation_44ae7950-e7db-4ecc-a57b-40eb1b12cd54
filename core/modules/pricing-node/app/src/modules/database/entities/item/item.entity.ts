import {
	Column,
	Entity,
	Index,
	ManyToOne,
	OneToMany,
	PrimaryGeneratedColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Game } from '../game/game.entity';
// eslint-disable-next-line import/no-cycle
import { ItemId } from './item-id.entity';
// eslint-disable-next-line import/no-cycle
import { ItemPrice } from './item-price.entity';

@Entity()
@Index(['market_hash_name', 'game', 'paint_id'], { unique: true })
export class Item {
	@PrimaryGeneratedColumn()
	id: number;

	@Index()
	@Column({
		default: true,
	})
	status: boolean;

	@Index()
	@Column({
		type: 'varchar',
		length: 100,
	})
	market_hash_name: string;

	@Column({
		type: 'varchar',
		length: 300,
	})
	image_hash: string;

	@Column({
		default: 0,
	})
	paint_id: number;

	@Column({
		default: 0,
	})
	weapon_id: number;

	@OneToMany(() => ItemId, (itemId) => itemId.item, {
		cascade: true,
		eager: true,
	})
	ids: ItemId[];

	@Index()
	@ManyToOne(() => Game, (game) => game.items)
	game: Game;

	@OneToMany(() => ItemPrice, (price) => price.item)
	prices: ItemPrice[];
}
