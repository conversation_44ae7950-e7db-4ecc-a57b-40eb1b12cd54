import {
	Column,
	Entity,
	Index,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn,
	Unique,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Item } from './item.entity';

@Entity()
@Unique('item_id_key', ['item', 'key'])
export class ItemId {
	@Index()
	@PrimaryGeneratedColumn()
	id: number;

	@Index()
	@Column()
	key: string;

	@Column()
	value: string;

	@ManyToOne(() => Item, (item) => item.ids)
	@JoinColumn()
	item: Item;
}
