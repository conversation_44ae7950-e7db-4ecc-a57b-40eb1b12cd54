import {
	Column,
	<PERSON>tity,
	JoinTable,
	ManyToMany,
	ManyToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Game } from '../game/game.entity';
import { Marketplace } from './marketplace.entity';

@Entity()
export class MarketplaceLog {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({
		default: true,
	})
	status: boolean;

	@ManyToMany(() => Game, (game) => game.marketplaces, { eager: true })
	@JoinTable()
	games: Game[];

	@ManyToOne(() => Marketplace, (marketplace) => marketplace.logs)
	marketplace: Marketplace;
}
