import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Item } from './item.entity';
// eslint-disable-next-line import/no-cycle
import { Marketplace } from '../marketplace/marketplace.entity';

export const ACTIVE_ITEM_PRICE_TIMEFRAME_DAYS: number = parseInt(process.env.ACTIVE_ITEM_PRICE_TIMEFRAME_DAYS || '1', 10);

@Entity()
@Index(['item.id', 'marketplace.key', 'created_at'])
@Index(['created_at', 'marketplace.key'])
export class ItemPrice {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    price: number;

    @Index()
    @Column()
    count: number;

    @Index()
    @ManyToOne(() => Item, (item) => item.prices, { eager: true })
    @JoinColumn({ name: 'item_id', referencedColumnName: 'id' })
    item: Item;

    @Index()
    @ManyToOne(() => Marketplace, (marketplace) => marketplace.prices, { eager: true })
    @JoinColumn({ name: 'marketplace_key', referencedColumnName: 'key' })
    marketplace: Marketplace;

    @Index()
    @CreateDateColumn({ type: 'timestamp' })
    created_at: Date;
}
