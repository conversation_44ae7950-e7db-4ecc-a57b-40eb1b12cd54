import {
	Column,
	CreateDateColumn,
	Entity,
	Index,
	PrimaryGeneratedColumn,
	UpdateDateColumn,
} from 'typeorm';

export enum CookieType {
	Buff163 = 'buff',
	BuffMarket = 'buffmarket',
}

@Entity()
export class Cookie {

	@Index()
	@PrimaryGeneratedColumn()
	id: number;

	@Column()
	cookie: string;

	@Index()
	@Column()
	type: CookieType;

	@Column({
		default: 0,
	})
	requests: number;

	@Index()
	@Column({
		default: true,
	})
	status: boolean;

	@Index()
	@CreateDateColumn({ type: 'timestamp' })
	created_at: Date;

	@Index()
	@UpdateDateColumn({ type: 'timestamp' })
	updated_at: Date;
}
