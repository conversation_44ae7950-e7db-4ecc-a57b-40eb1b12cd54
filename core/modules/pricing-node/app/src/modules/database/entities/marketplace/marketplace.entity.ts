import {
    Column,
    Entity,
    Index,
    JoinTable,
    ManyToMany,
    OneToMany,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Game } from '../game/game.entity';
// eslint-disable-next-line import/no-cycle
import { ItemPrice } from '../item/item-price.entity';
// eslint-disable-next-line import/no-cycle
import { MarketplaceLog } from './marketplace-log.entity';

export enum Marketplaces {
    CSGORoll = 'csgoroll',
    Buff = 'buff',
    BuffBuy = 'buff_buy',
    BuffMarket = 'buffmarket',
    BuffMarketBuy = 'buffmarket_buy',
    Bitskins = 'bitskins',
    BitskinsSale = 'bitskins_sale',
    CSFloat = 'csgofloat',
    CSMoney = 'csmoney',
    Dmarket = 'dmarket',
    DmarketBuy = 'dmarket_buy',
    Gamerpay = 'gamerpay',
    MarketCSGO = 'marketcsgo',
    MarketCSGOBuy = 'marketcsgo_buy',
    SteamCommunityMarket = 'steam',
    SteamCommunityMarketBuy = 'steam_buy',
    Waxpeer = 'waxpeer',
    Skinport = 'skinport',
};

export const enabledMarketPlaces: string[] = (process.env.ENABLED_MARKETPLACES || Object.values(Marketplaces).join(',')).split(',').map(s => s.trim());

export const primaryMarketPlaces: string[] = (process.env.PRIMARY_MARKETPLACES || Object.values(Marketplaces).join(',')).split(',').map(s => s.trim());

const list = Object.values(Marketplaces) as string[];

if (!enabledMarketPlaces.some(m => list.includes(m))) {
    throw new Error(`Enabled marketplaces contains unknown market: ${enabledMarketPlaces.join(', ')}`);
}

@Entity()
export class Marketplace {
    @Index()
    @Column({
        unique: true,
        primary: true,
    })
    key: string;

    @Column({
        unique: true,
    })
    name: string;

    @Index()
    @Column({
        default: true,
    })
    status: boolean;

    @Column({
        type: 'float',
        default: 0.1,
    })
    weight: number;

    @ManyToMany(() => Game, (game) => game.marketplaces, { eager: true })
    @JoinTable()
    games: Game[];

    @OneToMany(() => ItemPrice, (price) => price.marketplace)
    prices: ItemPrice[];

    @OneToMany(() => MarketplaceLog, (log) => log.marketplace)
    logs: MarketplaceLog[];
}
