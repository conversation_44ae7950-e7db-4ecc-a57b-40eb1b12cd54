import {
	Column,
	Entity,
	Index,
	ManyToMany,
	OneToMany,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Item } from '../item/item.entity';
// eslint-disable-next-line import/no-cycle
import { Marketplace } from '../marketplace/marketplace.entity';

@Entity()
export class Game {
	@Index()
	@Column({
		unique: true,
		primary: true,
	})
	app_id: number;

	@Index()
	@Column({
		default: true,
	})
	status: boolean;

	@Column({
		unique: true,
	})
	short_name: string;

	@Column({
		unique: true,
	})
	long_name: string;

	@ManyToMany(() => Marketplace, (marketplace) => marketplace.games)
	marketplaces: Marketplace[];

	@OneToMany(() => Item, (item) => item.game)
	items: Item[];
}
