# Get Unbuntu image
FROM ubuntu:22.04 AS base
ENV DEBIAN_FRONTEND=noninteractive

USER root

# Upgrade repos and install packages
RUN apt-get -qq -o=Dpkg::Use-Pty=0 update && apt-get -qq -o=Dpkg::Use-Pty=0 -y upgrade \
    && apt-get -qq -o=Dpkg::Use-Pty=0 --assume-yes install \
    bash\
    build-essential \
    curl \   
    gnupg \
    ca-certificates

# Install nodejs20
RUN mkdir -p /etc/apt/keyrings/ && \
	curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg
	
ARG NODE_MAJOR=20

RUN echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list \
	&&apt-get update \
	&&apt-get -qq -o=Dpkg::Use-Pty=0 --assume-yes install nodejs -y

# Install yarn since this project uses package-lock for it
RUN npm install -g yarn

# Service stage
FROM base AS pricing-node

# Create non-root user
RUN adduser appuser --disabled-login --gecos "" --home "/app" --no-create-home \
    && addgroup appgroup && adduser appuser appgroup

# Give non-user ownership of folders
RUN mkdir /app && chown appuser:appgroup /app

USER appuser

# Install NPM packages in a separate layer
COPY /app/package.json /tmp/package.json
COPY /app/yarn.lock /tmp/yarn.lock
RUN cd /tmp && yarn install

# Set the directory we want to run the next commands for
WORKDIR /app

# Copy our source code into the container
COPY --chown=appuser:appgroup ./app /app
RUN cp -a /tmp/node_modules /app
COPY --chown=appuser:appgroup ./scripts ./scripts

RUN yarn build

EXPOSE 3000

CMD [ "node", "dist/main.js" ]
