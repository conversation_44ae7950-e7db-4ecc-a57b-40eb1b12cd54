# defaults to csgo<PERSON><PERSON>, override with COMPOSE_PROJECT_NAME
name: csgoempire

networks:
    app:
        name: ${CSGOEMPIRE_DOCKER_NETWORK_NAME:-${APP_NAME:-csgoempire}}

services:
    provably-fair-service:
        container_name: ${APP_NAME:-csgoempire}-provably-fair-service
        image: ${APP_NAME:-csgoempire}/provably-fair-service
        build: .
        entrypoint: ["/bin/bash", "./docker-entrypoint.dev.sh"]
        volumes:
            - ./src:/app/src:cached
            - ./prisma:/app/prisma:cached
            - ./package.json:/app/package.json:cached
            - ./package-lock.json:/app/package-lock.json:cached
            - ./tsconfig.json:/app/tsconfig.json:cached
            - ./docker-entrypoint.dev.sh:/app/docker-entrypoint.dev.sh:cached
            - ./.env:/app/.env:cached
            - ./dev.env:/app/dev.env:cached
        ports:
            - "${EXPOSED_PORT_PROVABLY_FAIR_SERVICE:-6282}:3000"
        environment:
            APP_NAME: "${APP_NAME:-csgoempire}"
            NODE_ENV: "development"
        env_file:
            - dev.env
            - .env
        networks:
            app:
                aliases:
                    - provably-fair-service
        # Keep the container running even if nodemon crashes
        restart: unless-stopped
        # Add healthcheck
        healthcheck:
            test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 10s
