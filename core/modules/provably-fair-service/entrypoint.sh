#!/bin/bash

# Function to parse the DATABASE_URL and extract the necessary information
parse_database_url() {
  local url=$1
  export DATABASE_USER=$(echo $url | sed -n 's|.*://\([^:]*\):.*|\1|p')
  export DATABASE_PASSWORD=$(echo $url | sed -n 's|.*://[^:]*:\([^@]*\)@.*|\1|p')
  export DATABASE_HOST=$(echo $url | sed -n 's|.*://[^@]*@\([^:]*\):.*|\1|p')
  export DATABASE_PORT=$(echo $url | sed -n 's|.*://[^@]*@[^:]*:\([^/]*\)/.*|\1|p')
  export DATABASE_NAME=$(echo $url | sed -n 's|.*/\([^?]*\).*|\1|p')
}

# Parse the DATABASE_URL
parse_database_url "$DATABASE_URL"

# Function to check if the database exists
check_database_exists() {
  mysql -h "$DATABASE_HOST" -P "$DATABASE_PORT" -u "$DATABASE_USER" -p"$DATABASE_PASSWORD" -e "USE $DATABASE_NAME" > /dev/null 2>&1
}

# Check if the database exists
if check_database_exists; then
  echo "Database exists. Skipping creation."
else
  echo "Database does not exist. Creating database and pushing schema..."
  # Push the Prisma schema to the database
  npx prisma db push
fi

# Start the application
exec node build/provably-fair-service.js
