generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-arm64-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
  output   = "./generated/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model GameSeed {
  id              Int         @id @default(autoincrement())
  game            Game
  gameRoundId     Int
  userId          Int?
  serverSeedHash  String      @db.Char(64)
  encryptedSeed   Bytes
  drandRoundId    Int
  drandRandomness String?     @db.Char(64)
  serverSeed      String?     @db.Char(32)
  currentNonce    Int         @default(0)
  createdAt       DateTime    @default(now()) @db.Timestamp(6)
  updatedAt       DateTime?   @db.Timestamp(6)
  results         GameResult[]

  @@unique([game, gameRoundId])
  @@index([drandRoundId])
  @@index([game, userId, serverSeed])
}

model GameResult {
  id          Int       @id @default(autoincrement())
  gameSeedId  Int
  nonce       Int       @default(0)
  result      String    @db.VarChar(255)
  createdAt   DateTime  @default(now()) @db.Timestamp(6)
  gameSeed    GameSeed  @relation(fields: [gameSeedId], references: [id])

  @@unique([gameSeedId, nonce])
  @@index([gameSeedId, createdAt(sort: Desc)])
}

enum Game {
  ROULETTE
  CASE_BATTLE
  DICE
}
