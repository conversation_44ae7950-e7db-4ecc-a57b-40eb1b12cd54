export default class BaseError extends Error {
    statusCode: number;

    constructor(message, statusCode = 400) {
        super(message);

        this.name = this.constructor.name;
        this.statusCode = statusCode;
        if (typeof Error.captureStackTrace === 'function') {
            Error.captureStackTrace(this, this.constructor);
        } else {
            this.stack = (new Error(message)).stack;
        }
    }
}
