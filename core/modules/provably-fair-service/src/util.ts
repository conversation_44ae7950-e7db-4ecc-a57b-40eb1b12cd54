export function shuffle<T>(arr: Array<T>): Array<T> {
    const input = arr.slice();
    const output = [];
    while (input.length > 0) {
        const idx = Math.floor(Math.random() * input.length);
        output.push(input.splice(idx, 1)[0]);
    }
    return output;
}

export function stringifyError(ex: Error): string {
    let message = ex.message;
    if (ex instanceof AggregateError) {
        message += ' ' + ex.errors.map(e => `(${e})`).join(' ');
    }
    return message;
}
