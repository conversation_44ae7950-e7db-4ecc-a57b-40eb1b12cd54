export class Config {
    private static toBoolean(val: string): boolean {
        const falsy = /^(?:f(?:alse)?|no?|0+)$/i;
        return !falsy.test(val) && !!val;
    }

    public static get(key: string): string {
        const value = process.env[key];
        if (value === undefined) {
            throw new Error(`Environment variable ${key} is not set`);
        }
        return value;
    }

    public static getBool(key: string): boolean {
        const value = this.get(key);
        return this.toBoolean(value);
    }

    public static getOptional(key: string, defaultValue: string = ''): string {
        return process.env[key] ?? defaultValue;
    }

    public static getOptionalBool(key: string, defaultValue: boolean = false): boolean {
        const value = process.env[key];
        return value === undefined ? defaultValue : this.toBoolean(value);
    }
}
