import { Game } from '../../prisma/generated/client';

export interface GameConfig {
    /**
     * Whether the game requires a user ID for seed generation.
     * If true, user ID is required. If false, user ID is not allowed.
     */
    isPerUser: boolean;

    /**
     * Whether the game allows reusing seeds with different nonces.
     * If true, the same seed can be used multiple times with different nonces.
     * If false, each seed can only be used once.
     */
    allowSeedReuse: boolean;

    /**
     * Minimum delay in milliseconds before the drand round can be picked up.
     * This ensures fairness by preventing last-moment seed generation.
     * Default is 3000ms (3 seconds).
     */
    minDrandDelay: number;

    /**
     * Threshold in milliseconds for rounding up to the next drand round.
     * If the time to next round is less than this threshold, we'll use the round after.
     * Default is 500ms.
     */
    drandRoundThreshold: number;
}

export class GameConfigService {
    private static readonly DEFAULT_MIN_DRAND_DELAY = 3000; // 3 seconds
    private static readonly DEFAULT_DRAND_ROUND_THRESHOLD = 500; // 500ms

    private static readonly configs: Record<Game, GameConfig> = {
        [Game.ROULETTE]: {
            isPerUser: false,
            allowSeedReuse: false,
            minDrandDelay: GameConfigService.DEFAULT_MIN_DRAND_DELAY,
            drandRoundThreshold: GameConfigService.DEFAULT_DRAND_ROUND_THRESHOLD,
        },
        [Game.CASE_BATTLE]: {
            isPerUser: true,
            allowSeedReuse: false,
            minDrandDelay: GameConfigService.DEFAULT_MIN_DRAND_DELAY,
            drandRoundThreshold: GameConfigService.DEFAULT_DRAND_ROUND_THRESHOLD,
        },
        [Game.DICE]: {
            isPerUser: true,
            allowSeedReuse: true,
            minDrandDelay: GameConfigService.DEFAULT_MIN_DRAND_DELAY,
            drandRoundThreshold: GameConfigService.DEFAULT_DRAND_ROUND_THRESHOLD,
        },
    };

    public static getConfig(game: Game): GameConfig {
        return this.configs[game];
    }

    public static validateSeedGeneration(game: Game, userId: number | null): void {
        const config = this.getConfig(game);

        if (config.isPerUser && userId === null) {
            throw new Error(`Game ${game} requires a user ID for seed generation`);
        }

        if (!config.isPerUser && userId !== null) {
            throw new Error(`Game ${game} does not accept a user ID for seed generation`);
        }
    }

    public static validateResultGeneration(game: Game, nonce: number | undefined): void {
        const config = this.getConfig(game);

        if (!config.allowSeedReuse && nonce !== undefined) {
            throw new Error(`Game ${game} does not support seed reuse with nonces`);
        }

        if (config.allowSeedReuse && nonce === undefined) {
            throw new Error(`Game ${game} requires a nonce for result generation`);
        }
    }

    /**
     * Calculate the target drand round based on the current round and game config.
     * Takes into account the minimum delay and round threshold.
     */
    public static calculateTargetDrandRound(
        game: Game,
        currentRound: number,
        chainPeriod: number,
        genesisTime: number,
        now: number = Date.now()
    ): {
        targetRound: number;
        millisecondsToRound: number;
    } {
        const config = this.getConfig(game);
        const minDelayRounds = Math.ceil(config.minDrandDelay / (chainPeriod * 1000));

        // Calculate minimum target round based on minDrandDelay
        // Always add at least 2 rounds to ensure minimum 3s delay
        let targetRound = currentRound + Math.max(2, minDelayRounds);

        // Calculate time to the target round
        const targetRoundTime = genesisTime + ((targetRound - 1) * chainPeriod);
        const timeToTargetRound = (targetRoundTime * 1000) - now;

        // If we're too close to the target round or the delay would be less than minDrandDelay,
        // move to the next round
        if (timeToTargetRound < config.drandRoundThreshold || timeToTargetRound < config.minDrandDelay) {
            targetRound++;
        }

        const finalRoundTime = genesisTime + ((targetRound - 1) * chainPeriod);
        const millisecondsToRound = Math.max(0, (finalRoundTime * 1000) - now);

        // Double check that we have at least minDrandDelay
        // If not, take one more round
        if (millisecondsToRound < config.minDrandDelay) {
            targetRound++;
            const adjustedRoundTime = genesisTime + ((targetRound - 1) * chainPeriod);
            return {
                targetRound,
                millisecondsToRound: Math.max(0, (adjustedRoundTime * 1000) - now),
            };
        }

        return {
            targetRound,
            millisecondsToRound,
        };
    }
}
