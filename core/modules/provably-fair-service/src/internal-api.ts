import axios from 'axios';
import debugLogger from 'debug';
import {Agent as HttpsAgent} from 'node:https';
import {ClientError} from './errors/client';
import {ServerError} from './errors/server';

const debug = debugLogger('provably-fair-service:internal-api');

export interface RequestOptions {
    retries?: number;
    retryCount?: number;
    method?: string;
    timeout?: number;
    startedAt?: number;
    headers?: Record<string, string>;
}

export async function internalApiRequest(url: string, data: {[name: string]: string|number}, options: RequestOptions = { }) {
    const opts: RequestOptions = {
        retries: 5,
        retryCount: 0,
        method: 'POST',
        timeout: 15000,
        ...options,
    };

    if (!opts.startedAt) {
        opts.startedAt = Date.now();
    }

    const baseURL = process.env.INTERNAL_API_URL || `http://${process.env.APP_NAME || 'csgoempire'}.backend/api/v2/socket`;

    debug('Send internal api request', baseURL, url);

    try {
        const result = await axios({
            method: opts.method,
            baseURL,
            url,
            headers: {
                ...opts.headers,
                'x-empire-socket-user-id': 'general',
                'x-empire-socket-api-token': process.env.INTERNAL_API_TOKEN,
                'x-empire-server-timestamp': opts.startedAt,
                'Content-Type': 'application/json',
            },
            data,
            validateStatus: status => status >= 200 && status <= 299,
            httpsAgent: new HttpsAgent({keepAlive: true}),
        });

        debug('Internal api request handled in', Date.now() - opts.startedAt, 'milliseconds with', opts.retryCount, 'retries');

        return result;
    } catch (error) {
        debug('Internal api request failed', error);

        if (error.response?.statusCode && error.response.statusCode < 500) {
            let message = null;
            if (error.response?.data?.error?.message) {
                message = error.response.data.error.message;
            }

            throw new ClientError(message, error.response.statusCode);
        }

        if (opts.retryCount < opts.retries) {
            return internalApiRequest(url, data,  { ...opts, retryCount: opts.retryCount + 1 });
        }

        if (error.statusCode && error.statusCode >= 500) {
            let message = null;
            if (error.response?.data?.error?.message) {
                message = error.response.data.error.message;
            }

            throw new ServerError(message, error.statusCode);
        }

        throw error;
    }
}
