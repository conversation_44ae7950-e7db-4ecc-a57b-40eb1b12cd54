import { Response } from 'express';
import DrandService, { DrandTimeoutError } from '../drand/drand-service';
import { GameSeedService, GameSeedStatus, GameSeedResponse } from './game-seed-service';

export class GameSeedStreamHelper {
    constructor(
        private drandService: DrandService,
        private gameSeedService: GameSeedService
    ) {}

    public async handleStreamResponse(
        res: Response,
        seed: GameSeedResponse
    ): Promise<void> {
        // Set up SSE headers for streaming
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        // Send initial result immediately
        this.writeStatus(res, seed.status, seed);

        // Check initial status in case the round is already available
        const initialStatus = await this.gameSeedService.getSeedStatus(seed.seedId);
        if (initialStatus.status === GameSeedStatus._READY) {
            this.writeStatus(res, initialStatus.status, initialStatus);
            res.end();
            return;
        }

        // Wait for the drand round with timeout
        try {
            let timeoutId: NodeJS.Timeout;
            const timeoutPromise = new Promise<never>((_, reject) => {
                timeoutId = setTimeout(() => {
                    reject(new DrandTimeoutError(`Timeout ${seed.millisecondsToRound}ms waiting for round ${seed.drandRoundId}`));
                }, seed.millisecondsToRound + 1500); // Add 1.5s buffer
            });

            const drandPromise = this.drandService.waitForRound(seed.drandRoundId);
            await Promise.race([drandPromise, timeoutPromise]).finally(() => clearTimeout(timeoutId));

            const finalStatus = await this.gameSeedService.getSeedStatus(seed.seedId);
            this.writeStatus(res, finalStatus.status, finalStatus);
        } catch (error) {
            this.writeError(res, error);
        } finally {
            res.end();
        }
    }

    private writeStatus(res: Response, status: GameSeedStatus, response: GameSeedResponse): void {
        res.write(`${status}==${JSON.stringify(response)}==${status}`);
    }

    private writeError(res: Response, error: unknown): void {
        if (error instanceof DrandTimeoutError) {
            res.write(JSON.stringify({
                error: error.message,
                code: 'DRAND_TIMEOUT',
                status: 408
            }));
        } else {
            res.write(JSON.stringify({
                error: error instanceof Error ? error.message : 'Unknown error',
                code: 'DRAND_ERROR',
                status: 500
            }));
        }
    }
}
