import crypto from 'crypto';
import { Prisma, PrismaClient, Game } from '../../prisma/generated/client';
import { KmsService } from './kms-service';
import DrandService from '../drand/drand-service';
import { currentChainRoundNumber, chainRoundNumberTimestamp } from '../drand/drand-helpers';
import { GameResult } from './game-result-service';
import { GameConfigService } from '../config/game-config';
import { createClient, RedisClientType } from 'redis';
import debugLogger from 'debug';

const debug = debugLogger('provably-fair-service:game-seed-service');

const GAME_SEED_SELECT = {
    id: true,
    game: true,
    drandRoundId: true,
    serverSeedHash: true,
    drandRandomness: true,
    serverSeed: true,
    currentNonce: true,
    gameRoundId: true,
    userId: true,
    createdAt: true,
    updatedAt: true,
} as const;

const GAME_SEED_WITH_ENCRYPTED_SELECT = {
    ...GAME_SEED_SELECT,
    encryptedSeed: true,
} as const;

const GAME_RESULT_SELECT = {
    id: true,
    result: true,
    nonce: true,
    createdAt: true,
} as const;

export enum GameSeedStatus {
    _PENDING = 'PENDING',
    _READY = 'READY',
    _CLOSED = 'CLOSED',
}

export interface GameSeedResponse {
    drandRoundId: number;
    drandRandomness: string | null;
    hashedServerSeed: string;
    serverSeed: string | null;
    createdAt: Date;
    updatedAt: Date | null;
    result: string | null;
    resultId: number | null;
    resultCreatedAt: Date | null;
    currentNonce: number;
    nonce: number;
    seedId: number;
    status: GameSeedStatus;
    game: Game;
    userId: number | null;
    gameRoundId: number;
    millisecondsToRound?: number;
}

type GameSeedSelect = {
    id: number;
    game: Game;
    drandRoundId: number;
    serverSeedHash: string;
    drandRandomness: string | null;
    encryptedSeed?: Buffer;
    serverSeed: string | null;
    currentNonce: number;
    gameRoundId: number;
    userId: number | null;
    createdAt: Date;
    updatedAt: Date | null;
};

type GameSeedUpdate = {
    drandRandomness?: string;
    updatedAt: Date;
    currentNonce: number;
    serverSeed?: string;
};

export class GameSeedService {
    private prisma: PrismaClient;
    private kmsService: KmsService;
    private drandService: DrandService;
    private redisClient: RedisClientType;

    constructor(drandService: DrandService) {
        this.prisma = new PrismaClient();
        this.kmsService = new KmsService();
        this.drandService = drandService;
        this.redisClient = createClient({ url: process.env.REDIS_URL });
        this.redisClient.connect().catch((err) => {
            debug(`Redis connection error: ${err}`);
        });
    }

    private async acquireLock(game: Game, userId: number | null): Promise<boolean> {
        const lockKey = `lock:game_seed:${game}${userId ? `:${userId}` : ''}`;
        const lockValue = crypto.randomBytes(16).toString('hex');

        // Try to set the lock with a 10 second expiry
        const result = await this.redisClient.set(lockKey, lockValue, {
            NX: true, // Only set if key doesn't exist
            EX: 10 // 10 second expiry
        });

        return result === 'OK';
    }

    private async releaseLock(game: Game, userId: number | null): Promise<void> {
        const lockKey = `lock:game_seed:${game}${userId ? `:${userId}` : ''}`;
        await this.redisClient.del(lockKey);
    }

    private async getSeedLatestResult(seedId: number): Promise<{ id: number; result: string; nonce: number; createdAt: Date; } | null> {
        return this.prisma.gameResult.findFirst({
            where: { gameSeedId: seedId },
            orderBy: { createdAt: 'desc' },
            select: GAME_RESULT_SELECT,
        });
    }

    private async buildGameSeedResponse(
        seed: GameSeedSelect,
        result: { id: number; result: string; createdAt: Date } | null = null,
        nonce: number = 0,
        millisecondsToRound?: number
    ): Promise<GameSeedResponse> {
        // If seed is pending, calculate millisecondsToRound
        let timeToRound = millisecondsToRound;
        if (!seed.drandRandomness) {
            const chainInfo = await this.drandService.getChainInfo();
            const currentRound = currentChainRoundNumber(chainInfo.period, chainInfo.genesis_time);

            // Use GameConfigService to calculate proper time to round with minimum delay
            const { millisecondsToRound: calculatedTime } = GameConfigService.calculateTargetDrandRound(
                seed.game,
                currentRound,
                chainInfo.period,
                chainInfo.genesis_time
            );

            timeToRound = calculatedTime;
        }

        let status = GameSeedStatus._PENDING;
        if (seed.serverSeed) {
            status = GameSeedStatus._CLOSED;
        } else if (seed.drandRandomness) {
            status = GameSeedStatus._READY;
        }

        return {
            drandRoundId: seed.drandRoundId,
            drandRandomness: seed.drandRandomness,
            hashedServerSeed: seed.serverSeedHash,
            serverSeed: seed.serverSeed,
            createdAt: seed.createdAt,
            updatedAt: seed.updatedAt,
            result: result?.result ?? null,
            resultId: result?.id ?? null,
            resultCreatedAt: result?.createdAt ?? null,
            currentNonce: seed.currentNonce,
            nonce,
            seedId: seed.id,
            status,
            game: seed.game,
            userId: seed.userId,
            gameRoundId: seed.gameRoundId,
            millisecondsToRound: timeToRound,
        };
    }

    public async generateGameSeed(
        game: Game,
        gameRoundId: number,
        userId?: number | null,
        lockRequired: boolean = true
    ): Promise<GameSeedResponse> {
        const config = GameConfigService.getConfig(game);

        // Validate user ID requirements based on game config
        if (config.isPerUser && !userId) {
            throw new Error(`Game ${game} requires a user ID for seed generation`);
        }

        if (!config.isPerUser && userId) {
            throw new Error(`Game ${game} does not accept a user ID for seed generation`);
        }

        const hasLock = lockRequired ? await this.acquireLock(game, userId) : true;
        if (!hasLock) {
            throw new Error('Another operation is in progress for this game/user combination');
        }

        try {
            // Check for existing non-closed seeds
            const existingSeed = await this.prisma.gameSeed.findFirst({
                where: {
                    game,
                    userId: config.isPerUser ? userId : null,
                    serverSeed: null, // not published/closed
                },
            });

            if (existingSeed) {
                throw new Error('There is already an active seed for this game/user combination');
            }

            // Generate random server seed
            const serverSeed = crypto.randomBytes(16);
            const serverSeedHash = crypto.createHash('sha256').update(serverSeed).digest('hex');

            // Double hash for return value
            const hashedServerSeed = crypto.createHash('sha256').update(serverSeedHash).digest('hex');

            // Encrypt server seed with KMS
            const encryptedSeed = await this.kmsService.encrypt(serverSeed);

            // Try to get cached drand info first
            const cachedDrand = await this.drandService.getLatestCachedDrand();
            const chainInfo = await this.drandService.getChainInfo();

            // Calculate current round based on cached value if available
            let currentRound: number;
            if (cachedDrand) {
                // Use cached round if it's still current
                const cachedRoundTime = chainRoundNumberTimestamp(cachedDrand.round, chainInfo.period, chainInfo.genesis_time);
                const now = Math.floor(Date.now() / 1000);
                if (now < cachedRoundTime + chainInfo.period) {
                    currentRound = cachedDrand.round;
                } else {
                    currentRound = currentChainRoundNumber(chainInfo.period, chainInfo.genesis_time);
                }
            } else {
                currentRound = currentChainRoundNumber(chainInfo.period, chainInfo.genesis_time);
            }

            // Use GameConfigService to calculate target round and milliseconds to round
            const { targetRound, millisecondsToRound } = GameConfigService.calculateTargetDrandRound(
                game,
                currentRound,
                chainInfo.period,
                chainInfo.genesis_time
            );

            const gameSeed = await this.prisma.gameSeed.create({
                data: {
                    game,
                    gameRoundId,
                    userId,
                    serverSeedHash,
                    encryptedSeed,
                    drandRoundId: targetRound,
                    currentNonce: config.allowSeedReuse ? 1 : 0,
                    createdAt: new Date(),
                },
                select: GAME_SEED_SELECT,
            });

            const seedResponse = await this.buildGameSeedResponse(gameSeed as GameSeedSelect, null, 0, millisecondsToRound);
            seedResponse.hashedServerSeed = hashedServerSeed; // Ensure we use the double-hashed value

            return seedResponse;
        } finally {
            // Always release the lock if we acquired it
            lockRequired && await this.releaseLock(game, userId);
        }
    }

    public async getSeedStatus(seedId: number): Promise<GameSeedResponse> {
        const seed = await this.prisma.gameSeed.findUnique({
            where: { id: seedId },
            select: GAME_SEED_WITH_ENCRYPTED_SELECT,
        });

        if (!seed) {
            throw new Error('Seed not found');
        }

        // If we already have the randomness in the database, return it
        if (seed.drandRandomness) {
            const latestResult = await this.getSeedLatestResult(seed.id);
            return this.buildGameSeedResponse(
                seed as GameSeedSelect,
                latestResult,
                latestResult?.nonce ?? 0
            );
        }

        const chainInfo = await this.drandService.getChainInfo();
        const currentRound = currentChainRoundNumber(chainInfo.period, chainInfo.genesis_time);

        // If the target round hasn't been reached yet
        if (currentRound < seed.drandRoundId) {
            return this.buildGameSeedResponse(seed as GameSeedSelect);
        }

        // Try to get randomness from cache first
        let drandRandomness: string | null = null;

        // Check local cache
        const cachedDrand = await this.drandService.getLatestCachedDrand();
        if (cachedDrand && cachedDrand.round === seed.drandRoundId) {
            drandRandomness = cachedDrand.randomness;
        }

        // If not in local cache, get from drand service (which will check Redis before network)
        if (!drandRandomness) {
            drandRandomness = await this.drandService.getRound(seed.drandRoundId);
        }

        // Update the database with the randomness
        const updatedSeed = await this.prisma.gameSeed.update({
            where: { id: seed.id },
            data: {
                drandRandomness,
                updatedAt: new Date(),
            } as GameSeedUpdate,
            select: GAME_SEED_WITH_ENCRYPTED_SELECT,
        });

        return this.buildGameSeedResponse(updatedSeed as GameSeedSelect);
    }

    public async generateResult(seedId: number, nonce: number): Promise<GameSeedResponse> {
        // Fetch the seed from the database
        const seed = await this.prisma.gameSeed.findUnique({
            where: { id: seedId },
            select: GAME_SEED_WITH_ENCRYPTED_SELECT,
        });

        // Check if seed exists before accessing its properties
        if (!seed) {
            throw new Error('Seed not found');
        }

        // Check if drand randomness is available
        if (!seed.drandRandomness) {
            throw new Error('Drand randomness not yet available');
        }

        // Check if encrypted seed is available
        if (!seed.encryptedSeed) {
            throw new Error('Encrypted seed not available');
        }

        const config = GameConfigService.getConfig(seed.game);

        // Check if a result already exists for this nonce
        const existingResult = await this.prisma.gameResult.findUnique({
            where: { gameSeedId_nonce: { gameSeedId: seed.id, nonce } },
            select: GAME_RESULT_SELECT,
        });
        if (existingResult) return this.buildGameSeedResponse(seed as GameSeedSelect, existingResult, nonce);

        // Validate the nonce
        if (config.allowSeedReuse) {
            if (nonce !== seed.currentNonce) {
                throw new Error('Invalid nonce: does not match current nonce for reusable seed');
            }
        } else {
            // For non-reusable seeds, nonce should match currentNonce (which is initialized to 1)
            if (nonce !== seed.currentNonce) {
                throw new Error('Invalid nonce: non-reusable seeds require nonce = 1');
            }
            if (seed.serverSeed) {
                throw new Error('Seed is already closed');
            }
        }

        // Acquire a lock to prevent concurrent operations
        if (!await this.acquireLock(seed.game, seed.userId)) {
            throw new Error('Another operation is in progress for this game/user combination');
        }

        try {
            // Decrypt the server seed
            const serverSeedBuffer = await this.kmsService.decrypt(Buffer.from(seed.encryptedSeed));
            if (!serverSeedBuffer) {
                throw new Error('Failed to decrypt server seed');
            }

            const serverSeed = serverSeedBuffer.toString('hex');
            const updatedAt = new Date();

            // Perform result generation and seed update in a transaction
            const gameResult = await this.prisma.$transaction(async (tx: Prisma.TransactionClient) => {
                // Generate the result
                const resultValue = GameResult.generate(seed.game, serverSeedBuffer, seed.drandRandomness, nonce);

                const result = await tx.gameResult.create({
                    data: {
                        gameSeedId: seed.id,
                        nonce,
                        result: resultValue,
                    },
                    select: GAME_RESULT_SELECT,
                });

                const update: Prisma.GameSeedUpdateInput = {
                    updatedAt,
                    ...(config.allowSeedReuse
                        ? { currentNonce: seed.currentNonce + 1 }
                        : { serverSeed }),
                };

                await tx.gameSeed.update({ where: { id: seed.id }, data: update });

                // Update local object for response
                seed.updatedAt = updatedAt;
                if (config.allowSeedReuse) {
                    seed.currentNonce += 1;
                } else {
                    seed.serverSeed = serverSeed;
                }

                return result;
            });

            // Build and return the response
            return this.buildGameSeedResponse(seed as GameSeedSelect, gameResult, nonce);
        } finally {
            // Release the lock
            await this.releaseLock(seed.game, seed.userId);
        }
    }

    public async closeSeed(seedId: number, lockRequired: boolean = true): Promise<void> {
        const seed = await this.prisma.gameSeed.findUnique({
            where: { id: seedId },
            select: {
                id: true,
                game: true,
                encryptedSeed: true,
                serverSeed: true,
                userId: true,
            },
        });

        if (!seed) {
            throw new Error('Seed not found');
        }

        // Acquire lock
        const hasLock = lockRequired ? await this.acquireLock(seed.game, seed.userId) : true;
        if (!hasLock) {
            throw new Error('Another operation is in progress for this game/user combination');
        }

        try {
            if (seed.serverSeed) {
                throw new Error('Seed is already closed');
            }

            const serverSeedBuffer = await this.kmsService.decrypt(Buffer.from(seed.encryptedSeed));
            if (!serverSeedBuffer) {
                throw new Error('Failed to decrypt server seed');
            }
            const serverSeed = Buffer.from(serverSeedBuffer).toString('hex');

            await this.prisma.gameSeed.update({
                where: { id: seedId },
                data: {
                    serverSeed,
                    updatedAt: new Date(),
                },
            });
        } finally {
            // Always release the lock
            lockRequired && await this.releaseLock(seed.game, seed.userId);
        }
    }

    public async regenerateSeed(
        seedId: number,
        newGameRoundId: number
    ): Promise<GameSeedResponse> {
        const seed = await this.prisma.gameSeed.findUnique({
            where: { id: seedId },
            select: GAME_SEED_SELECT,
        });

        if (!seed) {
            throw new Error('Seed not found');
        }

        // Acquire lock
        const hasLock = await this.acquireLock(seed.game, seed.userId);
        if (!hasLock) {
            throw new Error('Another operation is in progress for this game/user combination');
        }

        try {
            await this.closeSeed(seedId, false);

            return this.generateGameSeed(
                seed.game,
                newGameRoundId,
                seed.userId ?? undefined,
                false // Don't acquire lock in generateGameSeed since we already have it
            );
        } finally {
            // Always release the lock
            await this.releaseLock(seed.game, seed.userId);
        }
    }

    public async getLatestSeed(game: Game, userId?: number): Promise<GameSeedResponse | null> {
        const config = GameConfigService.getConfig(game);

        // Validate user ID requirements based on game config
        if (config.isPerUser && !userId) {
            throw new Error(`Game ${game} requires a user ID for seed lookup`);
        }

        if (!config.isPerUser && userId) {
            throw new Error(`Game ${game} does not accept a user ID for seed lookup`);
        }

        const latestSeed = await this.prisma.gameSeed.findFirst({
            where: {
                game,
                userId: config.isPerUser ? userId : null,
            },
            orderBy: {
                createdAt: 'desc'
            },
            select: GAME_SEED_SELECT,
        });

        if (!latestSeed) {
            return null;
        }

        const latestResult = await this.getSeedLatestResult(latestSeed.id);

        return this.buildGameSeedResponse(
            latestSeed as GameSeedSelect,
            latestResult,
            latestResult?.nonce ?? 0
        );
    }

    public async getSeedByGameAndRoundId(game: Game, gameRoundId: number): Promise<GameSeedResponse | null> {
        const seed = await this.prisma.gameSeed.findFirst({
            where: {
                game,
                gameRoundId,
            },
            select: GAME_SEED_SELECT,
        });

        if (!seed) {
            return null;
        }

        const latestResult = await this.getSeedLatestResult(seed.id);

        return this.buildGameSeedResponse(
            seed as GameSeedSelect,
            latestResult,
            latestResult?.nonce ?? 0
        );
    }
}
