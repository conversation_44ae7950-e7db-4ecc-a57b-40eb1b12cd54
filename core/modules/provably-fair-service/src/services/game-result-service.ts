import crypto from 'crypto';
import { Game } from '../../prisma/generated/client';

export class GameResult {
    public static generate(game: Game, serverSeed: Buffer, drandRandomness: string, nonce?: number): string {
        // Create HMAC using server seed as the key
        const hmac = crypto.createHmac('sha256', serverSeed);

        // Update with drand randomness
        hmac.update(Buffer.from(drandRandomness, 'hex'));

        // If nonce is provided, append it to the message
        if (nonce > 0) {
            hmac.update(Buffer.from(nonce.toString()));
        }

        const hash = hmac.digest('hex');
        return this.generateResultForGame(game, hash);
    }

    private static generateResultForGame(game: Game, hash: string): string {
        switch (game) {
            case Game.ROULETTE:
                return this.generateRouletteResult(hash);
            case Game.CASE_BATTLE:
                return this.generateCaseBattleResult(hash);
            case Game.DICE:
                return this.generateDiceResult(hash);
            default:
                throw new Error(`Unsupported game type: ${game}`);
        }
    }

    private static generateRouletteResult(hash: string): string {
        // Use first 4 bytes of hash for entropy
        const value = parseInt(hash.slice(0, 8), 16);
        // Generate number between 0-14
        return (value % 15).toString();
    }

    private static generateCaseBattleResult(hash: string): string {
        // Use first 4 bytes of hash for entropy
        const value = parseInt(hash.slice(0, 8), 16);
        // Generate float between 0-1
        return (value / 0xffffffff).toString();
    }

    private static generateDiceResult(hash: string): string {
        // Use first 4 bytes of hash for entropy
        const value = parseInt(hash.slice(0, 8), 16);
        // Generate number between 0-10000 (matching Laravel's getMinRoll and getMaxRoll)
        return (value % 10001).toString();
    }
}
