import { KMSClient, EncryptCommand, DecryptCommand } from '@aws-sdk/client-kms';
import { Config } from '../config';

export class KmsService {
    private client: KMSClient;
    private keyId: string;

    constructor() {
        const useLocalStack = Config.getOptionalBool('AWS_USE_LOCALSTACK', false) || !Config.getOptional('AWS_KMS_KEY_ID');
        const credentials = useLocalStack ? {
            accessKeyId: Config.getOptional('AWS_ACCESS_KEY_ID', 'test'),
            secretAccessKey: Config.getOptional('AWS_SECRET_ACCESS_KEY', 'test'),
        } : undefined;

        this.client = new KMSClient({
            endpoint: useLocalStack ? Config.getOptional('LOCALSTACK_ENDPOINT', 'http://localstack:4566') : undefined,
            region: Config.getOptional('AWS_REGION', 'us-east-2'),
            credentials: credentials,
        });

        this.keyId = Config.getOptional('AWS_KMS_KEY_ID', '1234abcd-12ab-34cd-56ef-1234567890ab');
    }

    public async encrypt(data: Buffer): Promise<Buffer> {
        const command = new EncryptCommand({
            KeyId: this.keyId,
            Plaintext: data,
        });

        const response = await this.client.send(command);
        if (!response.CiphertextBlob) {
            throw new Error('Failed to encrypt data with KMS');
        }

        return Buffer.from(response.CiphertextBlob);
    }

    public async decrypt(data: Buffer): Promise<Buffer> {
        const command = new DecryptCommand({
            KeyId: this.keyId,
            CiphertextBlob: data,
        });

        const response = await this.client.send(command);
        if (!response.Plaintext) {
            throw new Error('Failed to decrypt data');
        }

        return Buffer.from(response.Plaintext);
    }
}
