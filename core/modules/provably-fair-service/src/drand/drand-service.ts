import debugLogger from 'debug';
import {createClient, RedisClientType} from 'redis';
import type {ChainInfo} from 'drand-client';

import DrandClient from './drand-client';
import {chainRoundNumberTimestamp} from './drand-helpers';
import {stringifyError} from '../util';

const debug = debugLogger('provably-fair-service:drand-service');

interface CachedDrandValue {
    round: number;
    randomness: string;
    time: number;
}

type DrandCallback = (randomness: string) => void;

export class DrandTimeoutError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'DrandTimeoutError';
    }
}

export default class DrandService {
    private drandClient: DrandClient;
    private redisClient: RedisClientType;
    private redisPublisherClient: RedisClientType;
    private localCache: Map<number, CachedDrandValue>;
    private latestLocalCache: CachedDrandValue | null;
    private pendingRounds: Map<number, DrandCallback[]>;

    constructor() {
        this.localCache = new Map();
        this.latestLocalCache = null;
        this.pendingRounds = new Map();
    }

    public waitForRound(round: number): Promise<string> {
        // Check if we already have this round
        const cached = this.localCache.get(round);
        if (cached) {
            return Promise.resolve(cached.randomness);
        }

        return new Promise<string>((resolve) => {
            const callbacks = this.pendingRounds.get(round) || [];
            callbacks.push((randomness: string) => resolve(randomness));
            this.pendingRounds.set(round, callbacks);
        });
    }

    public async getChainInfo(): Promise<ChainInfo> {
        return this.drandClient.getChainInfo();
    }

    public async getLatestCachedDrand(): Promise<CachedDrandValue | null> {
        // Check local cache first
        if (this.latestLocalCache) {
            return this.latestLocalCache;
        }

        // Fallback to Redis
        if (!this.redisClient?.isReady) {
            return null;
        }

        const cachedValue = await this.redisClient.get('drand:latest');
        if (!cachedValue) {
            return null;
        }

        const parsed = JSON.parse(cachedValue) as CachedDrandValue;
        // Update local cache
        this.latestLocalCache = parsed;
        this.localCache.set(parsed.round, parsed);
        return parsed;
    }

    public async getRound(round: number): Promise<string> {
        // Check local cache first
        const localCached = this.localCache.get(round);
        if (localCached) {
            return localCached.randomness;
        }

        // Try to get from Redis cache
        if (this.redisClient?.isReady) {
            const cachedValue = await this.redisClient.get(`drand:round:${round}`);
            if (cachedValue) {
                const parsed = JSON.parse(cachedValue) as CachedDrandValue;
                // Update local cache
                this.localCache.set(round, parsed);
                return parsed.randomness;
            }
        }

        // Fallback to direct drand network fetch
        const randomness = await this.drandClient.getRound(round);
        // Cache the fetched value locally
        const value: CachedDrandValue = {
            round,
            randomness,
            time: Math.floor(Date.now() / 1000)
        };
        this.localCache.set(round, value);
        return randomness;
    }

    private cleanupLocalCache(currentRound: number): void {
        // Keep only the last 10 rounds in local cache
        for (const round of this.localCache.keys()) {
            if (round < currentRound - 10) {
                this.localCache.delete(round);
            }
        }
    }

    public isInLocalCache(round: number): boolean {
        return this.localCache.has(round);
    }

    public async start(): Promise<void> {
        this.drandClient = new DrandClient(
            process.env.DRAND_CHAIN_HASH || '52db9ba70e0cc0f6eaf7803dd07447a1f5477735fd3f661792ba94600c84e971',
            process.env.DRAND_CHAIN_PUBLIC_KEY || '83cf0f2896adee7eb8b5f01fcad3912212c437e0073e911fb90022d3e760183c8c4b450b6a0a6c3ac6a5776a2d1064510d1fec758c921cc22b0e17e63aaf4bcb5ed66304de9cf809bd274ca73bab4af5a6e9c76a4bc09e76eae8991ef5ece45a',
        );

        this.redisClient = createClient({
            url: process.env.REDIS_URL,
        });

        this.redisPublisherClient = this.redisClient.duplicate();

        ['redisClient', 'redisPublisherClient'].forEach((clientType) => {
            const client = this[clientType];

            client.on('ready', () => {
                debug(`${clientType} ready`);
            });

            client.on('error', (err) => {
                debug(`${clientType} error: ${stringifyError(err)}`);
                // The redis module automatically manages reconnecting, so we don't need to explicitly reconnect here
            });
        });

        await this.redisClient.connect();
        await this.redisPublisherClient.connect();
        await this.drandClient.startWatching();

        const drandChainInfo = await this.drandClient.getChainInfo();

        this.drandClient.on('round', async (round: number, randomness: string) => {
            const delay = Date.now() - chainRoundNumberTimestamp(round, drandChainInfo.period, drandChainInfo.genesis_time) * 1000;
            debug(`Received round ${round} = ${randomness} (${delay}ms latency)`);

            const value: CachedDrandValue = {
                round,
                randomness,
                time: Math.floor(Date.now() / 1000)
            };

            // Update local cache
            this.latestLocalCache = value;
            this.localCache.set(round, value);
            this.cleanupLocalCache(round);

            // Handle any pending callbacks for this round
            const callbacks = this.pendingRounds.get(round);
            if (callbacks) {
                callbacks.forEach(callback => callback(randomness));
                this.pendingRounds.delete(round);
            }

            if (!this.redisClient?.isReady || !this.redisPublisherClient?.isReady) {
                debug(`Cannot put round ${round} in redis because ${!this.redisClient ? 'redisClient' : 'redisPublisherClient'} is not ready`);
                return;
            }

            const redisValue = JSON.stringify(value);
            await Promise.all([
                this.redisClient.setEx('drand:latest', drandChainInfo.period * 2, redisValue),
                this.redisClient.setEx(`drand:round:${round}`, 60 * 60, redisValue), // cache for 1 hour
                this.redisPublisherClient.publish('drand:latest', redisValue),
            ]);
        });
    }
}
