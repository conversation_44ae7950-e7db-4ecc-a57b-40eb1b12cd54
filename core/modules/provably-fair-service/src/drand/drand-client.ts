import debugLogger from 'debug';
import {HttpCaching<PERSON>hain, HttpChainClient} from 'drand-client';
import {TypedEmitter} from 'tiny-typed-emitter';

import type {ChainInfo} from 'drand-client';

import {shuffle, stringifyError} from '../util';
import {chainRoundNumberTimestamp, currentChainRoundNumber, nodeUrls} from './drand-helpers';

const debug = debugLogger('provably-fair-service:drand-client');

interface DrandClientEvents {
    round: (round: number, randomness: string) => void;
}

export default class DrandClient extends TypedEmitter<DrandClientEvents> {
    private clients: HttpChainClient[] = [];
    private chainInfo: ChainInfo | null = null;
    private watching: boolean = false;
    private watcherPreviousRound: number = 0;
    private watcherTickTimeout: NodeJS.Timeout|null = null;

    constructor(chainHash: string, chainPublicKey: string) {
        super();

        const options = {
            // if disableBeaconVerification is true, then randomness values will not have their signatures validated
            disableBeaconVerification: false,
            noCache: false,
            chainVerificationParams: {chainHash, publicKey: chainPublicKey},
        };

        nodeUrls().forEach((url) => {
            const chain = new HttpCachingChain(`${url}/${chainHash}`, options);
            const client = new HttpChainClient(chain, options);
            this.clients.push(client);
        });
    }

    public async startWatching(): Promise<void> {
        // yes, drand-client has a watch function, but it's bad (errors out with 404 very frequently)
        if (this.watching) {
            throw new Error('Already watching on this DrandClient instance');
        }

        debug('Starting watcher');
        this.watching = true;

        if (!this.chainInfo) {
            this.chainInfo = await this.getChainInfo();
        }

        // noinspection ES6MissingAwait
        this.watcherTick();
    }

    public stopWatching(): void {
        this.watching = false;
    }

    public async getChainInfo(): Promise<ChainInfo> {
        if (this.chainInfo) {
            return this.chainInfo;
        }

        const clients = this.chooseClients();
        debug(`Fetching chain info from ${getClientDomains(clients).join(', ')}`);

        return Promise.any(clients.map(client => client.chain().info()));
    }

    public async getRound(round: number): Promise<string> {
        const clients = this.chooseClients();
        return (await Promise.any(clients.map(client => client.get(round)))).randomness;
    }

    /**
     * The idea here is to request data from multiple clients simultaneously and return the first response.
     * This is to ensure that we get data as quickly as possible.
     *
     * @private
     */
    private chooseClients(): HttpChainClient[] {
        // Choose at most 3 clients
        const clientCount = Math.min(3, this.clients.length);
        return shuffle(this.clients).slice(0, clientCount);
    }

    private async watcherTick(): Promise<void> {
        if (!this.watching) {
            return;
        }

        if (!this.chainInfo) {
            throw new Error('this.chainInfo is null (should never happen!)');
        }

        const currentRoundNumber = currentChainRoundNumber(this.chainInfo.period, this.chainInfo.genesis_time);
        if (currentRoundNumber == this.watcherPreviousRound) {
            // We're too early for the next round
            this.scheduleWatcherTick(50);
            return;
        }

        debug(`watcherTick(): Fetching round ${currentRoundNumber}`);

        try {
            const randomness = await this.getRound(currentRoundNumber);
            this.watcherPreviousRound = currentRoundNumber;

            const nextRoundTimestamp = chainRoundNumberTimestamp(currentRoundNumber + 1, this.chainInfo.period, this.chainInfo.genesis_time);
            const delay = (nextRoundTimestamp * 1000) - Date.now();
            this.scheduleWatcherTick(delay);

            debug(`watcherTick(): Got randomness ${randomness} for round ${currentRoundNumber}. Requesting next round in ${delay} ms`);
            this.emit('round', currentRoundNumber, randomness);
        } catch (ex) {
            debug(`Error fetching round ${currentRoundNumber}: ${stringifyError(ex)}`);
            this.scheduleWatcherTick(100);
        }
    }

    private scheduleWatcherTick(timeout: number): void {
        if (this.watcherTickTimeout) {
            clearTimeout(this.watcherTickTimeout);
        }

        this.watcherTickTimeout = setTimeout(() => this.watcherTick(), timeout);
    }
}

function getClientDomains(clients: HttpChainClient[]): string[] {
    return clients.map(client => client.chain().baseUrl.match(/https?:\/\/([^/]+)/)![1]);
}
