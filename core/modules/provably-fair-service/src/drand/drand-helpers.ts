export function currentChainRoundNumber(period: number, genesisTime: number): number {
    // round numbers are 1-indexed
    const now = Math.floor(Date.now() / 1000);
    return Math.floor((now - genesisTime) / period) + 1;
}

export function chainRoundNumberTimestamp(roundNumber: number, period: number, genesisTime: number): number {
    return genesisTime + ((roundNumber - 1) * period);
}

export function nodeUrls(): string[] {
    return [
        'https://api.drand.sh',
        'https://api2.drand.sh',
        'https://api3.drand.sh',
        'https://drand.cloudflare.com',
        'https://api.drand.secureweb3.com:6875',
    ];
}
