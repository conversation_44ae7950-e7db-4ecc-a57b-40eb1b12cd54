require('newrelic');
import {configDotenv} from 'dotenv';
configDotenv();

import express, { Express, Request, Response } from 'express';
import { z } from 'zod';
import { Game } from '../prisma/generated/client';
import { createClient } from 'redis';
import DrandService from './drand/drand-service';
import { currentChainRoundNumber, chainRoundNumberTimestamp } from './drand/drand-helpers';
import { GameSeedService } from './services/game-seed-service';
import { GameSeedStreamHelper } from './services/game-seed-stream-helper';
import { GameConfigService } from './config/game-config';

const app: Express = express();
const port: number = parseInt(process.env.PORT || '3000', 10);
let drandService: DrandService;
let gameSeedService: GameSeedService;
let gameSeedStreamHelper: GameSeedStreamHelper;
const redisSubscriber = createClient({ url: process.env.REDIS_URL });

// Development mode indicator
const isDev = process.env.NODE_ENV !== 'production';

// Connect Redis subscriber
redisSubscriber.connect().catch(console.error);

// Middleware
app.use(express.json());

// Authentication middleware
const authenticateApiKey = (req: Request, res: Response, next: () => void): void => {
    // Skip authentication for health check endpoint
    if (req.path === '/health') {
        return next();
    }

    const authHeader = req.headers.authorization;
    const apiKey = process.env.PF_API_KEY;

    if (!apiKey) {
        console.error('PF_API_KEY environment variable is not set');
        res.status(500).json({
            error: 'Server configuration error',
            code: 'SERVER_ERROR',
            status: 500
        });
        return;
    }

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
            error: 'Missing or invalid authorization header',
            code: 'UNAUTHORIZED',
            status: 401
        });
        return;
    }

    const token = authHeader.split(' ')[1];

    if (token !== apiKey) {
        res.status(401).json({
            error: 'Invalid API key',
            code: 'UNAUTHORIZED',
            status: 401
        });
        return;
    }

    next();
};

// Apply authentication middleware
app.use(authenticateApiKey);

// Request validation schemas
const generateSeedRequestSchema = z.object({
    game: z.nativeEnum(Game),
    gameRoundId: z.number().int().positive(),
    userId: z.number().int().positive().nullable().optional(),
    stream: z.boolean().optional().default(false),
});

const regenerateSeedRequestSchema = z.object({
    gameRoundId: z.number().int().positive(),
    stream: z.boolean().optional().default(false),
});

// Health check endpoint
app.get('/health', (_req: Request, res: Response): void => {
    res.json({ ok: true });
});

// Game seed generation endpoint
app.post('/game-seeds', async (req: Request, res: Response): Promise<void> => {
    try {
        const validatedData = generateSeedRequestSchema.parse(req.body);

        const result = await gameSeedService.generateGameSeed(
            validatedData.game,
            validatedData.gameRoundId,
            validatedData.userId
        );

        // If streaming is not requested, return the result immediately
        if (!validatedData.stream) {
            res.json(result);
            return;
        }

        await gameSeedStreamHelper.handleStreamResponse(res, result);
    } catch (error) {
        console.error('Error in game-seeds endpoint:', error);

        if (error instanceof z.ZodError) {
            res.status(400).json({
                error: 'Invalid request data',
                code: 'VALIDATION_ERROR',
                details: error.errors,
                status: 400
            });
            return;
        }

        // Handle specific game seed errors
        if (error instanceof Error) {
            const errorMap: Record<string, number> = {
                'Game ROULETTE requires a user ID for seed generation': 400,
                'Game CASE_BATTLE requires a user ID for seed generation': 400,
                'Game DICE requires a user ID for seed generation': 400,
                'Game ROULETTE does not accept a user ID for seed generation': 400,
                'Game CASE_BATTLE does not accept a user ID for seed generation': 400,
                'Game DICE does not accept a user ID for seed generation': 400,
                'There is already an active seed for this game/user combination': 409,
                'Seed not found': 404,
                'Drand randomness not yet available': 409,
                'Seed is already closed': 409,
            };

            const statusCode = errorMap[error.message] || 500;
            res.status(statusCode).json({
                error: error.message,
                code: error.name,
                status: statusCode
            });
            return;
        }

        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Get latest seed by game endpoint
app.get('/game-seeds/games/:game', async (req: Request, res: Response): Promise<void> => {
    try {
        const game = req.params.game as Game;
        const userId = req.query.userId ? parseInt(req.query.userId as string, 10) : undefined;

        if (!Object.values(Game).includes(game)) {
            res.status(400).json({
                error: 'Invalid game type',
                code: 'INVALID_GAME',
                status: 400
            });
            return;
        }

        const config = GameConfigService.getConfig(game);

        if (config.isPerUser && !userId) {
            res.status(400).json({
                error: `Game ${game} requires a user ID`,
                code: 'USER_ID_REQUIRED',
                status: 400
            });
            return;
        }

        if (!config.isPerUser && userId !== undefined) {
            res.status(400).json({
                error: `Game ${game} does not accept a user ID`,
                code: 'USER_ID_NOT_ALLOWED',
                status: 400
            });
            return;
        }

        if (userId !== undefined && isNaN(userId)) {
            res.status(400).json({
                error: 'Invalid user ID',
                code: 'INVALID_USER_ID',
                status: 400
            });
            return;
        }

        const result = await gameSeedService.getLatestSeed(game, userId);

        if (!result) {
            res.status(404).json({
                error: 'No seed found',
                code: 'SEED_NOT_FOUND',
                status: 404
            });
            return;
        }

        res.json(result);
    } catch (error) {
        console.error('Error in get latest seed endpoint:', error);

        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Get seed by game and round ID endpoint
app.get('/game-seeds/games/:game/:gameRoundId', async (req: Request, res: Response): Promise<void> => {
    try {
        const game = req.params.game as Game;
        const gameRoundId = parseInt(req.params.gameRoundId, 10);

        if (!Object.values(Game).includes(game)) {
            res.status(400).json({
                error: 'Invalid game type',
                code: 'INVALID_GAME',
                status: 400
            });
            return;
        }

        if (isNaN(gameRoundId) || gameRoundId <= 0) {
            res.status(400).json({
                error: 'Invalid game round ID',
                code: 'INVALID_GAME_ROUND_ID',
                status: 400
            });
            return;
        }

        const result = await gameSeedService.getSeedByGameAndRoundId(game, gameRoundId);

        if (!result) {
            res.status(404).json({
                error: 'No seed found',
                code: 'SEED_NOT_FOUND',
                status: 404
            });
            return;
        }

        res.json(result);
    } catch (error) {
        console.error('Error in get seed by game and round ID endpoint:', error);

        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Get seed status endpoint
app.get('/game-seeds/:id', async (req: Request, res: Response): Promise<void> => {
    try {
        const seedId = parseInt(req.params.id, 10);

        if (isNaN(seedId) || seedId <= 0) {
            res.status(400).json({
                error: 'Invalid seed ID',
                code: 'INVALID_SEED_ID',
                status: 400
            });
            return;
        }

        const result = await gameSeedService.getSeedStatus(seedId);
        res.json(result);
    } catch (error) {
        console.error('Error in get seed status endpoint:', error);

        if (error instanceof Error) {
            const errorMap: Record<string, number> = {
                'Seed not found': 404
            };

            const statusCode = errorMap[error.message] || 500;
            res.status(statusCode).json({
                error: error.message,
                code: error.name,
                status: statusCode
            });
            return;
        }

        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Get seed result endpoint
app.get('/game-seeds/:id/result/:nonce', async (req: Request, res: Response): Promise<void> => {
    try {
        const seedId = parseInt(req.params.id, 10);
        const nonce = parseInt(req.params.nonce, 10);

        // Validate seedId
        if (isNaN(seedId) || seedId <= 0) {
            res.status(400).json({
                error: 'Invalid seed ID',
                code: 'INVALID_SEED_ID',
                status: 400
            });
            return;
        }

        // Validate nonce
        if (isNaN(nonce) || nonce <= 0) {
            res.status(400).json({
                error: 'Invalid nonce',
                code: 'INVALID_NONCE',
                status: 400
            });
            return;
        }

        // Call generateResult with nonce
        const result = await gameSeedService.generateResult(seedId, nonce);
        res.json(result);
    } catch (error) {
        console.error('Error in get seed result endpoint:', error);
        if (error instanceof Error) {
            const errorMap: Record<string, number> = {
                'Seed not found': 404,
                'Drand randomness not yet available': 409,
                'Encrypted seed not available': 500,
                'Failed to decrypt server seed': 500,
                'Invalid nonce: does not match current nonce for reusable seed': 400,
                'Invalid nonce: non-reusable seeds require nonce = 1': 400,
                'Seed is already closed': 409,
                'Another operation is in progress for this game/user combination': 409
            };
            const statusCode = errorMap[error.message] || 500;
            res.status(statusCode).json({
                error: error.message,
                code: error.name,
                status: statusCode
            });
            return;
        }
        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Close seed endpoint
app.get('/game-seeds/:id/close', async (req: Request, res: Response): Promise<void> => {
    try {
        const seedId = parseInt(req.params.id, 10);

        if (isNaN(seedId) || seedId <= 0) {
            res.status(400).json({
                error: 'Invalid seed ID',
                code: 'INVALID_SEED_ID',
                status: 400
            });
            return;
        }

        await gameSeedService.closeSeed(seedId);
        res.status(200).json({ success: true });
    } catch (error) {
        console.error('Error in close seed endpoint:', error);

        if (error instanceof Error) {
            const errorMap: Record<string, number> = {
                'Seed not found': 404,
                'Seed is already closed': 409
            };

            const statusCode = errorMap[error.message] || 500;
            res.status(statusCode).json({
                error: error.message,
                code: error.name,
                status: statusCode
            });
            return;
        }

        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Regenerate seed endpoint
app.post('/game-seeds/:id/regenerate', async (req: Request, res: Response): Promise<void> => {
    try {
        const seedId = parseInt(req.params.id, 10);

        if (isNaN(seedId) || seedId <= 0) {
            res.status(400).json({
                error: 'Invalid seed ID',
                code: 'INVALID_SEED_ID',
                status: 400
            });
            return;
        }

        const validatedData = regenerateSeedRequestSchema.parse(req.body);

        const result = await gameSeedService.regenerateSeed(seedId, validatedData.gameRoundId);

        // If streaming is not requested, return the result immediately
        if (!validatedData.stream) {
            res.json(result);
            return;
        }

        await gameSeedStreamHelper.handleStreamResponse(res, result);
    } catch (error) {
        console.error('Error in regenerate seed endpoint:', error);

        if (error instanceof z.ZodError) {
            res.status(400).json({
                error: 'Invalid request data',
                code: 'VALIDATION_ERROR',
                details: error.errors,
                status: 400
            });
            return;
        }

        if (error instanceof Error) {
            const errorMap: Record<string, number> = {
                'Seed not found': 404,
                'Seed is already closed': 409
            };

            const statusCode = errorMap[error.message] || 500;
            res.status(statusCode).json({
                error: error.message,
                code: error.name,
                status: statusCode
            });
            return;
        }

        res.status(500).json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 500
        });
    }
});

// Drand information endpoint
app.get('/drand/current', async (_req: Request, res: Response): Promise<void> => {
    try {
        const chainInfo = await drandService.getChainInfo();
        const currentRound = currentChainRoundNumber(chainInfo.period, chainInfo.genesis_time);
        const nextRoundTime = chainRoundNumberTimestamp(currentRound + 1, chainInfo.period, chainInfo.genesis_time);
        const timeToNextRound = (nextRoundTime * 1000) - Date.now();

        // Try to get the latest cached value first
        const cachedDrand = await drandService.getLatestCachedDrand();
        let currentRandomness: string;
        let cacheSource: 'local' | 'redis' | 'network' = 'network';

        if (cachedDrand && cachedDrand.round === currentRound) {
            // Use cached value if it matches the current round
            currentRandomness = cachedDrand.randomness;
            cacheSource = 'local';
        } else {
            // Fallback to fetching from drand network or Redis cache
            currentRandomness = await drandService.getRound(currentRound);
            // Check if the value is now in local cache after fetching
            cacheSource = drandService.isInLocalCache(currentRound) ? 'local' : 'redis';
        }

        res.json({
            current_round: currentRound,
            randomness: currentRandomness,
            next_round_in_ms: timeToNextRound,
            next_round_timestamp: nextRoundTime,
            cache_source: cacheSource
        });
    } catch (error) {
        console.error('Error fetching drand information:', error);
        res.status(500).json({ error: 'Failed to fetch drand information' });
    }
});

async function startServer(): Promise<void> {
    try {
        app.listen(port, () => {
            console.log(`Server is running on port ${port} in ${isDev ? 'development' : 'production'} mode`);
            if (isDev) {
                console.log('Watching for file changes...');
            }
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

async function main(): Promise<void> {
    drandService = new DrandService();
    gameSeedService = new GameSeedService(drandService);
    gameSeedStreamHelper = new GameSeedStreamHelper(drandService, gameSeedService);
    await drandService.start();
    await startServer();
}

main().catch((error: Error) => {
    console.error('Application failed to start:', error);
    process.exit(1);
});
