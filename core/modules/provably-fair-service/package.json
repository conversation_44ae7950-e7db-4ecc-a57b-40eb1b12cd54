{"name": "provably-fair-service", "version": "1.0.0", "description": "", "scripts": {"lint": "eslint --ext .ts src", "dev": "npm run dev", "build": "node scripts/delete-build.js && tsc --outDir build", "start": "node build/provably-fair-service.js", "build-and-start": "npm run build && npm run start"}, "private": true, "engines": {"node": ">=22.0.0"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "22.13.17", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "eslint": "^8.57.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "dependencies": {"@aws-sdk/client-kms": "^3.0.0", "@noble/curves": "^1.8.1", "@prisma/client": "^6.0.0", "axios": "^1.8.4", "crypto": "^1.0.1", "debug": "^4.4.0", "dotenv": "^16.4.7", "drand-client": "^1.2.6", "express": "^5.0.0", "newrelic": "^12.18.0", "prisma": "^6.0.0", "redis": "^4.7.0", "tiny-typed-emitter": "^2.1.0", "zod": "^3.24.2"}}