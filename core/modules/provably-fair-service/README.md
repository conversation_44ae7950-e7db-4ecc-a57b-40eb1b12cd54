# Provably Fair Service

This microservice handles backend work for provably fair games. It fetches drand values in real-time and provides APIs for game seed generation and management.

## Configuration

### Environment Variables

- `DRAND_CHAIN_HASH` - The hash of the drand chain (defaults to quicknet)
- `DRAND_CHAIN_PUBLIC_KEY` - The public key of the drand chain
- `REDIS_URL` - Redis connection URL (`redis[s]://[[username][:password]@][host][:port][/db-number]`)
- `PORT` - Server port (defaults to 3000)
- `PF_API_KEY` - API key for authentication (required)

### Authentication

All endpoints (except `/health`) require authentication using a Bearer token. The token must match the `PF_API_KEY` environment variable.

**Request Headers**
```http
Authorization: Bearer <your-api-key>
```

**Error Responses**

Missing or invalid authorization header:
```json
{
    "error": "Missing or invalid authorization header",
    "code": "UNAUTHORIZED",
    "status": 401
}
```

Invalid API key:
```json
{
    "error": "Invalid API key",
    "code": "UNAUTHORIZED",
    "status": 401
}
```

## REST API Endpoints

### Health Check
```http
GET /health
```
Returns the service health status.

**Response**
```json
{
  "ok": true
}
```

### Game Seeds

#### Generate Game Seed
```http
POST /game-seeds
```
Creates a new game seed for a specific game round.

**Request Body**
```json
{
  "game": "ROULETTE|CASE_BATTLE|DICE",
  "gameRoundId": number,
  "userId": number|null (optional),
  "stream": boolean (optional, defaults to false)
}
```

**Response**
```json
{
  "drandRoundId": number,
  "drandRandomness": null,
  "hashedServerSeed": string,
  "serverSeed": null,
  "createdAt": string,
  "updatedAt": null,
  "result": null,
  "resultId": null,
  "resultCreatedAt": null,
  "currentNonce": number,
  "nonce": number,
  "seedId": number,
  "status": "PENDING|READY|CLOSED",
  "game": string,
  "userId": number|null,
  "gameRoundId": number,
  "millisecondsToRound": number
}
```

If `stream: true`, the response will be a Server-Sent Events (SSE) stream with the following events:
- `seed`: Initial seed data
- `update`: Updates about the seed status
- `ready`: When the seed is ready with drand randomness
- `error`: Any errors that occur during the stream

**Status Codes**
- `200`: Success
- `400`: Invalid request data or validation error
- `409`: Conflict (e.g., active seed exists, drand not available)
- `500`: Server error

#### Get Latest Seed
```http
GET /game-seeds/latest
```
Retrieves the latest seed for a specific game and optionally a user.

**Query Parameters**
```
game: "ROULETTE|CASE_BATTLE|DICE" (required)
userId: number (optional, required for some games)
```

**Response**
```json
{
  "drandRoundId": number,
  "drandRandomness": string|null,
  "hashedServerSeed": string,
  "serverSeed": string|null,
  "createdAt": string,
  "updatedAt": string|null,
  "result": string|null,
  "resultId": number|null,
  "resultCreatedAt": string|null,
  "currentNonce": number,
  "nonce": number,
  "seedId": number,
  "status": "PENDING|READY|CLOSED",
  "game": string,
  "userId": number|null,
  "gameRoundId": number,
  "millisecondsToRound": number|null
}
```

**Status Codes**
- `200`: Success
- `400`: Invalid request parameters
- `404`: No seed found
- `500`: Server error

#### Get Seeds By Game
```http
GET /game-seeds/games/:game
```
Retrieves the latest seed for a specific game.

**URL Parameters**
```
game: "ROULETTE|CASE_BATTLE|DICE" (required)
```

**Query Parameters**
```
userId: number (optional, required for games that require user-specific seeds)
```

**Response**
Same as Get Latest Seed response

**Status Codes**
- `200`: Success
- `400`: Invalid game parameter or missing required userId
- `404`: No seed found
- `500`: Server error

#### Get Seed By Game and Round ID
```http
GET /game-seeds/games/:game/:gameRoundId
```
Retrieves a seed for a specific game and round ID.

**URL Parameters**
```
game: "ROULETTE|CASE_BATTLE|DICE" (required)
gameRoundId: number (required)
```

**Response**
Same as Get Latest Seed response

**Status Codes**
- `200`: Success
- `400`: Invalid URL parameters
- `404`: No seed found
- `500`: Server error

#### Get Seed Status
```http
GET /game-seeds/:id
```
Retrieves the current status of a game seed.

**Response**
Same as Get Latest Seed response

**Status Codes**
- `200`: Success
- `400`: Invalid seed ID
- `404`: Seed not found
- `500`: Server error

#### Generate Result
```http
GET /game-seeds/:id/result
```
Generates a game result using the seed.

**Query Parameters**
```
nonce: number (optional, for games that support seed reuse)
```

**Response**
Same as Get Latest Seed response, with the latest result included

**Status Codes**
- `200`: Success
- `400`: Invalid seed ID or game doesn't support seed reuse
- `404`: Seed not found
- `409`: Drand randomness not available or seed closed
- `500`: Server error

#### Close Seed
```http
GET /game-seeds/:id/close
```
Closes a seed by revealing its server seed.

**Response**
```json
{
  "success": true
}
```

**Status Codes**
- `200`: Success
- `400`: Invalid seed ID
- `404`: Seed not found
- `409`: Seed is already closed
- `500`: Server error

#### Regenerate Seed
```http
POST /game-seeds/:id/regenerate
```
Closes the current seed and generates a new one.

**Request Body**
```json
{
  "gameRoundId": number,
  "stream": boolean (optional, defaults to false)
}
```

**Response**
Same as Generate Game Seed response

If `stream: true`, provides SSE stream as described in the Generate Game Seed endpoint.

**Status Codes**
- `200`: Success
- `400`: Invalid request data or seed ID
- `404`: Seed not found
- `409`: Seed is already closed
- `500`: Server error

### Drand Information
```http
GET /drand/current
```
Returns current drand chain information and randomness.

**Response**
```json
{
  "current_round": number,
  "randomness": string,
  "next_round_in_ms": number,
  "next_round_timestamp": number,
  "cache_source": "local|redis|network"
}
```

**Status Codes**
- `200`: Success
- `500`: Server error

## Game Configuration

Games are configured in `src/config/game-config.ts`. Each game requires specific configuration to determine its behavior in the provably fair system.

### Adding a New Game

1. Add the game to the `Game` enum in the Prisma schema
2. Add the game configuration in `src/config/game-config.ts`

### Game Configuration Options

```typescript
interface GameConfig {
    // Whether the game requires user-specific seeds
    isPerUser: boolean;
    
    // Whether the game allows reusing seeds with different nonces
    allowSeedReuse: boolean;
    
    // Minimum delay in milliseconds before drand round can be used
    // This ensures sufficient randomness entropy
    minDrandDelay: number;
}
```

### Example Configuration

```typescript
const gameConfig: Record<Game, GameConfig> = {
    ROULETTE: {
        isPerUser: false,
        allowSeedReuse: false,
        minDrandDelay: 5000, // 5 seconds
    },
    CASE_BATTLE: {
        isPerUser: true,
        allowSeedReuse: true,
        minDrandDelay: 3000, // 3 seconds
    },
};
```

### Configuration Options Explained

#### isPerUser
- `true`: Each user gets their own seed (e.g., case battles where each user needs their own seed)
- `false`: One seed is shared across all users (e.g., roulette where everyone uses the same seed)

#### allowSeedReuse
- `true`: The same seed can be used multiple times with different nonces (e.g., case battles where multiple cases are opened)
- `false`: Each seed can only be used once (e.g., roulette where each round needs a new seed)

#### minDrandDelay
- Minimum time in milliseconds that must pass before a drand round can be used
- Higher values provide more entropy and security but increase waiting time
- Should be set based on the game's requirements for randomness and user experience

### Game Result Generation

Each game needs to implement its own result generation logic in `src/services/game-result-service.ts`. This service is responsible for generating deterministic results using a hash derived from the server seed, drand randomness, and nonce.

#### Adding Result Generation for a New Game

1. Add a new case in the `generateResultForGame` method:

```typescript
private static generateResultForGame(game: Game, hash: string): string {
    switch (game) {
        case Game.ROULETTE:
            return this.generateRouletteResult(hash);
        case Game.CASE_BATTLE:
            return this.generateCaseBattleResult(hash);
        // Add your new game here
        case Game.YOUR_GAME:
            return this.generateYourGameResult(hash);
        default:
            throw new Error(`Unsupported game type: ${game}`);
    }
}
```

2. Implement the result generation method for your game:

```typescript
private static generateYourGameResult(hash: string): string {
    // Use first 4 bytes of hash for entropy
    const value = parseInt(hash.slice(0, 8), 16);
    
    // Generate your game result
    // Example: Generate number between 0-999
    return (value % 1000).toString();
}
```

#### Best Practices for Result Generation

1. **Using the Hash**
   - Each result function receives a hex string hash as input
   - Typically use first 4 bytes (8 characters) of the hash for entropy
   - Convert hex to integer using `parseInt(hash.slice(0, 8), 16)`

2. **Fairness**
   - Ensure uniform distribution using modulo operations
   - Consider the range of your modulo to avoid bias
   - Document the range and distribution of possible results

3. **Result Format**
   - Return results as strings for consistency
   - Use JSON.stringify for complex results
   - Document the result format for frontend integration

#### Example Implementations

```typescript
// Example for a roulette game (0-14)
private static generateRouletteResult(hash: string): string {
    // Use first 4 bytes of hash for entropy
    const value = parseInt(hash.slice(0, 8), 16);
    // Generate number between 0-14
    return (value % 15).toString();
}

// Example for a case battle (float between 0-1)
private static generateCaseBattleResult(hash: string): string {
    // Use first 4 bytes of hash for entropy
    const value = parseInt(hash.slice(0, 8), 16);
    // Generate float between 0-1
    return (value / 0xffffffff).toString();
}

// Example for a dice game (1-100)
private static generateDiceResult(hash: string): string {
    // Use first 4 bytes of hash for entropy
    const value = parseInt(hash.slice(0, 8), 16);
    // Generate number between 1-100
    return ((value % 100) + 1).toString();
}
```

## Streaming Support

Several endpoints support Server-Sent Events (SSE) streaming through the `stream: true` parameter. When streaming is enabled, the server maintains an open connection and sends updates about the seed status in real-time.

### Stream Event Types

- `seed`: Initial seed data when the stream starts
- `update`: Status updates about the seed
- `ready`: Sent when the seed becomes ready with drand randomness
- `error`: Any errors that occur during the stream

### Example Usage with Streaming

```javascript
const eventSource = new EventSource('/game-seeds?stream=true');

eventSource.addEventListener('seed', (event) => {
  const seedData = JSON.parse(event.data);
  console.log('Initial seed data:', seedData);
});

eventSource.addEventListener('update', (event) => {
  const update = JSON.parse(event.data);
  console.log('Seed update:', update);
});

eventSource.addEventListener('ready', (event) => {
  const finalData = JSON.parse(event.data);
  console.log('Seed is ready:', finalData);
});

eventSource.addEventListener('error', (event) => {
  console.error('Error:', event.data);
  eventSource.close();
});
```

## Error Handling

The service uses consistent error responses across all endpoints:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "status": number,
  "details": {} // Optional additional error details
}
```

Common error codes:
- `VALIDATION_ERROR`: Invalid request data
- `SEED_NOT_FOUND`: Requested seed doesn't exist
- `INTERNAL_ERROR`: Server-side error
- `INVALID_SEED_ID`: Invalid seed ID format
