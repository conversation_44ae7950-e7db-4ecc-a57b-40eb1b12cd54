# Base image
FROM ubuntu:24.04 AS base
ENV DEBIAN_FRONTEND=noninteractive

# Upgrade repos and install packages
RUN apt-get -o=Dpkg::Use-Pty=0 update \
    && apt-get -o=Dpkg::Use-Pty=0 --assume-yes install \
    bash \
    git  \
    dos2unix \
    software-properties-common

# Add service specific packages
FROM base AS packages
USER root

# Upgrade repos and install packages
RUN apt-get -o=Dpkg::Use-Pty=0 update && apt-get -o=Dpkg::Use-Pty=0 -y upgrade \
    && apt-get -o=Dpkg::Use-Pty=0 --assume-yes install \
    npm \
    curl \
    dirmngr \
    apt-transport-https \
    lsb-release \
    ca-certificates \
    jq \
    gettext-base \
    gnupg \
    mysql-client

# Install nodejs20
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Service image
FROM packages AS csgoempire-provably-fair-service

# Create non-root user
RUN adduser appuser --disabled-login --gecos ""  --home "/app"  --no-create-home \
    && addgroup appgroup && adduser appuser appgroup \
    && mkdir /app && chown appuser:appgroup /app

# Change to working dir
WORKDIR /app
USER appuser

# Copy package files code
COPY --chown=appuser:appgroup package.json .
COPY --chown=appuser:appgroup package-lock.json .

# install packages
RUN npm ci --no-color --quiet

# Copy source code
COPY --chown=appuser:appgroup . .

# Generate Prisma client
RUN npx prisma generate

# build the application
RUN npm run build

RUN find . -type f -not -path "./node_modules/*" -print0 | xargs -0 dos2unix

ENTRYPOINT ["/bin/bash", "/app/entrypoint.sh"]
