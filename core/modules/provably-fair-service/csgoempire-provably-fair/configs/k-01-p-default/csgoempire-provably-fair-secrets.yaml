apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-provably-fair-secrets
    namespace: default
type: Opaque
stringData:
    DRAND_CHAIN_HASH: ENC[AES256_GCM,data:SArGCusUyB6Wh3GnWX5Vy9ae1HWRZSoCd2Y9UFpGeBM53FeKzhgOnwNvKgbK/uC08jHMQlZULAlzveizZNC97A==,iv:L8h0rOgpn0o8v5VXn9vGXk4guLHkppzSa4ADz/ccPWQ=,tag:CBHVbyOcWyWmARh5oNJByQ==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:Ws2lG5SGOp1/Wpc6PNi6gI83m3oTW5xM9tiVACS3ngXaxcXxY+TM/EfwxHk9XQSP5wYuDrM/JIlfqA+ef57psovvoiOf8cjsZncqp5WFuw==,iv:xi/wBwE+jPafEn66d9TwiTRGaPDuUGUeazEkgZL8H3Q=,tag:EnUHYeaBPFqQPNiHtB/qsw==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/6d139d49-31a8-4df6-af2d-911ba3806029
          created_at: "2024-10-04T15:23:16Z"
          enc: AQICAHjeW8/jhqmxODoAy9IWX/R+cwYAT2/DbsAVhbPusZX8NAHznzjhoIUFpU0f0bCcFsrPAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMfccP0ez7u6fiH4B3AgEQgDsVBwsiOSWCiH7ytqTEX8Wh0WF8Snf9ccmJIlJUehciYW5Bqbr9+5PN9lWwKI5qQ7BX1fHLSrGknKvzaQ==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-04T15:23:17Z"
    mac: ENC[AES256_GCM,data:BWDwQ6qzTqaXjWTcKtZ7ArDEAXKcX7crxGv+ACpanZhVojtbm3iAFft2hjlbS2OfRJ4VW1nv21O/ppBOJobjAk8xYsnxonDe996biLVvkn2iAaBP7PcLjpNqu/6+iqjDdyS5hTg4Xc4DhK2EL2lxLlWAnmj9sTIt0NJEz+MC4IM=,iv:A6ASlaxvJefVgyIT5mmLCbH7HwlRFJ3IzNCQT6s4wjc=,tag:ntMN8oGhNrCpMZDPVnLzgg==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
