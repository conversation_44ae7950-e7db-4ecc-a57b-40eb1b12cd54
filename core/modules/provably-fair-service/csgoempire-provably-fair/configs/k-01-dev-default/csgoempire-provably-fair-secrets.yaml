apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-provably-fair-secrets
    namespace: default
type: Opaque
stringData:
    DRAND_CHAIN_HASH: ENC[AES256_GCM,data:Jm9hpMuEuzWHILCfbedu+fv61t3KTVnDlefU7fyNLJ7rTzVvoJM97/G48lrSrVTpuX+J+CBFx6jg/DXW4Fjtsg==,iv:wRCsxbau8QEBWlVTkuz1YWE9bitCKc4dUYjTsKYGQhY=,tag:4MPcQvKPpq4uf3wXAKjwnw==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:8Nb38ehqgA3pbtYfH7uavjO0TAa8j+FxWV+8ghCR508+9sWAqgua5Lhiih6JYy6ff2YFDK/CQIBsF19KHjq1CFfVt43IL1hr826GPgaIAQ==,iv:mBcDvFslHdQJYH3mPAtubnD78I3PaDwpMhBMpUIt1Wc=,tag:sXpYJ8pkVF8l3HfXAv7O3w==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/042a6a43-65a2-44a3-abe7-9f17ff1b7677
          created_at: "2024-10-04T15:21:52Z"
          enc: AQICAHhCoM3M4b9kgj3WXOlXYGSpssDPTNBbrRl46kn/pVYd9wFs6GRQFwoGkbjrKR8d3CC8AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMewijWwck2TBiC9NuAgEQgDvD82m5V3icjuSrF1QpwtGQBQrmfnMWHgbZGSlmCG0jo4S0VVemrRtDVkVtAE2m/mP6ZMRuhZ2iqXJzlw==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-04T15:21:53Z"
    mac: ENC[AES256_GCM,data:dnfbsoxEFVQLebzypsb/djt9GN7cmhO4uSu94dAeUXWMSTMbjbfEASjj5CbCeubiq8kdElkWtejdyolvHOWccS6HvzhuaYvY8ESw6iBdd00uvcFB/vLLxWa4lNJ5DH2m7O+VPIOOe2ew+X2393JzLMklIESMy2A2M4CMoqbJer0=,iv:Ks0V3ZyiA7aSpzIbsxHyWA2T/YYYlcXny903HpNVhsA=,tag:w4eyPhU8Km4RhkN6jY4TVA==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
