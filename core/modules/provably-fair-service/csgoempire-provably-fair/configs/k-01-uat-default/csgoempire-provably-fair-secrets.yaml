apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-provably-fair-secrets
    namespace: default
type: Opaque
stringData:
    DRAND_CHAIN_HASH: ENC[AES256_GCM,data:kd7ZSTUS5RabF/ad69gsVM0IvQIzx+21hFpuYwecy1URp7jnIGXq2qeJrRJrzDxCs6vjPheIy/CaIqO2vgBb8g==,iv:fQJSbn0Tu07gZnucS1z8NltXXywI0S5ygoDWNMcqHfE=,tag:RM2eTi5i+7meVHNVYvtXEQ==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:rSQYJP1pbr40VQdElBJTHpcVdEVPwrqt1u83HwGQIsu6disHtPftmoHfjkA33XeHUs40dbkdkbif7mfIGUBQq16ifG9by96n3LKs0vuBWw==,iv:18CqUFQl7z8jOq3wZ6ICLJZZ2Omc9Go33ISQgUEZxXY=,tag:Qf6sP21eejjo8+5KA16XpA==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/106cc3d0-52ce-4fff-8ce2-d5de6b4b495b
          created_at: "2024-10-04T15:23:41Z"
          enc: AQICAHgSoJfeQ7p7KnFbDOWNBH4uL/4umzn0G6+2tUTrQDfSXgGdm+6abkpIpO9kZcULX050AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM31xDI+NXtGb4aLW+AgEQgDtspnowKZfqXftRi5EoZ5Oa6XmqfCT0V6YX6qNDUzB9LsJq7+tADXferMPFQx8FawXqvUcUPuXnsuDbOQ==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-04T15:23:41Z"
    mac: ENC[AES256_GCM,data:EvgPv8+vXysjzxJi0f9FpFDMitsL2xrLPIOP92leBA3r7yVVblHhfsLlwteNWngzCdcc+QO1BdbmcXG8X6UrCD/OxRwseX0skG6nhqLCCHGaad+1V9f2oIdNTHqDAswLJCqGEbzmuS7wvNL+qpeveZvd6CX3gJjCR5GCQXmUbZg=,iv:htpxQW6c+1hntUX+T/nx8GmzTGwApQXv/XE971BQLk8=,tag:rXF5QYZSiZrykzSwVgF9HA==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
