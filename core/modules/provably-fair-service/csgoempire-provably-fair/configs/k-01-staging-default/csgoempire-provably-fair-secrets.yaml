apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-provably-fair-secrets
    namespace: default
type: Opaque
stringData:
    DRAND_CHAIN_HASH: ENC[AES256_GCM,data:VcG9kXacUHGM7yrU6QbRhabj/e9Xs/MwgieuCJyERfeDDR9kqfZiULFGJAzJMtlFaCFQVBbzEv+98xddFEowYw==,iv:IMYhOKiAEvQs5S0xyjyhf5WOjCp5zn4aMfE73RPwfF0=,tag:yA1flH3Lu5zAq36Q50ELng==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:45T9n7MYG9duWorHc+xkzGyrO/DZ0SYdZOAQ3V8w3GgB5YsS67EUPAg/IflJb6eED+UdcnWCrTFedj4MQ4EzLv25KcTfYKqSweVEqElhjw==,iv:pdMTdeCiRmYQh7duWlLVCD651di4FYy2F5Lt8z0g5oU=,tag:Ivuxh52LjOL8Vy9jGU1XzA==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:404599132715:key/03266ac7-13ee-424f-96b8-dd4cbe41d2f8
          created_at: "2024-10-04T15:23:28Z"
          enc: AQICAHgmVjCyicfe8YqMEaVgyxFqQVY/nh5ybHaA2JtK4enosAF9T+ONS1Cs6K6q7bTkX+SFAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMxTlwn3Tx10RtVfbuAgEQgDulrs2kQhr7wgV1ESwNRygiPEYQmpdN2StJilp65SKZwIgSQwBhQV1MMF39HXCP3BBCr7js27up/Zm6nA==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-04T15:23:29Z"
    mac: ENC[AES256_GCM,data:w/BGLOclmKiNZ7TnprdzXofPUkU5qESMyDNjmpf5G+ihNaGEwtom002wQoPTFPT8g4FylKDKDnO6MOuzCQwCJ6UeY3Z9MQgIiQ0q6eYs4S1zeMRqenL7ENoNQ2IyyQFi/PBSV/ExohC+T6mLA5TNDsmDeU/7F7sSxjgiSo4L5aw=,iv:FzoIpPS8IRO5OvWRTxDGBk+W6vmynsv3JDIoV3IHwnI=,tag:k7yVG9yH0RC5ytoHYKfebw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
