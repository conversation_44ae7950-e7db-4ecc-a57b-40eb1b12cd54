apiVersion: v1
kind: Secret
metadata:
    name: csgoempire-provably-fair-secrets
    namespace: default
type: Opaque
stringData:
    DRAND_CHAIN_HASH: ENC[AES256_GCM,data:mVFaYrIFJY0KEfAglVMbeK/uNgGr2dJTbKLHbSIdL/kFVAiVmh1/0zTyOmn7+vimqEcPoDvknQUzPOVrrPjh/A==,iv:713wwh8lUXUl5KSKh16GzO59MPxH2CW6LaXJ5yNM3O4=,tag:v4L+2J9C6T+PcOi0HbwQEA==,type:str]
    INTERNAL_API_TOKEN: ENC[AES256_GCM,data:yy7c7g6wkCMwl4ShwjbBQNj7hMwJV/NgkA4pEw3jgwiuJZFnH1dyT7OApL4/peBN3PJcfY5xxVs+pOVT4Yf935zUQzOhy9kJq4CAfxKSSA==,iv:J0HbG4+XHhXsuYNiS56fsZBKeSdCydANj5be0TpviA0=,tag:NMpMQrkSFCVunp3Hr+XEjw==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:eu-west-1:404599132715:key/mrk-9da20166b9a84c89a71a54c6b2fb88e1
          created_at: "2024-10-04T15:22:30Z"
          enc: AQICAHjArCQ/tgR4OJG2xPIW0+RrVQrqLl49P0G22ozQZIDx7gGEXfXV9WbHrGhRzpdJlRb8AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM6X5OhuMdeEWdjTgcAgEQgDsZH2nZtnyAq/NdKqrmrGuG3+LNblV5waSWBDJHcNmqqntG4+oO5DQOzZjpDTLU8Mq4T8CJjc4pjLPpYg==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-10-04T15:22:30Z"
    mac: ENC[AES256_GCM,data:9yJE3x1z+4iEroI9qAtUkCsOSvF61Ub331IYLpcIuzf885VrEx7wvAg9uuF0OuW9/Q2cZg7nP/6zczgOUCCPQaLOg+rIpXq49sdFlvedjEvZDNnvkM0X85i4SwQm0F2zHRzSsotIIrivFUNr4dpSQ6kcJ5iZhmYnwXxVawmD8wE=,iv:P4RAbEb/cIVaEYYIuCxIGTsqEY1TCrPR1j39T2w/A4g=,tag:QYXZTsQbgnA1uynHTblMeg==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.7.1
