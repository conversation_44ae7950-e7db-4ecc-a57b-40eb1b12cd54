apiVersion: v1
kind: ConfigMap
metadata:
  name: csgoempire-provably-fair-configs
  namespace: default
data:
  DRAND_CHAIN_PUBLIC_KEY: '83cf0f2896adee7eb8b5f01fcad3912212c437e0073e911fb90022d3e760183c8c4b450b6a0a6c3ac6a5776a2d1064510d1fec758c921cc22b0e17e63aaf4bcb5ed66304de9cf809bd274ca73bab4af5a6e9c76a4bc09e76eae8991ef5ece45a'
  REDIS_URL: 'redis://{{ required "Value is missing" .Values.CentralizedConfigs.REDIS_HOST }}'
  DEBUG: provably-fair-service:*
  INTERNAL_API_URL: 'http://csgoempire-roulette-backend/api/v2/socket'
  AWS_KMS_KEY_ID: arn:aws:kms:us-east-2:404599132715:key/mrk-babbbd124ab741f2bfaf2328d5f434d6
  NEW_RELIC_ENABLED: 'true'
  NEW_RELIC_LOG_LEVEL: "warn"
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED : 'false'
