DEBUG=provably-fair-service:*

REDIS_URL=redis://${APP_NAME:-csgoempire}-redis

INTERNAL_API_URL=http://${APP_NAME:-csgoempire}.backend/api/v2/socket
INTERNAL_API_TOKEN=random-token-for-internal-socket-api-requests

DATABASE_URL=mysql://${APP_NAME:-csgoempire}_provably_fair:${APP_NAME:-csgoempire}@${APP_NAME:-csgoempire}-database:3306/${APP_NAME:-csgoempire}_provably_fair
SHADOW_DATABASE_URL=mysql://${APP_NAME:-csgoempire}_provably_fair:${APP_NAME:-csgoempire}@${APP_NAME:-csgoempire}-database:3306/${APP_NAME:-csgoempire}_provably_fair_shadow

# Controls which drand chain we use. The default is "quicknet", which outputs a round every 3 seconds.
DRAND_CHAIN_HASH=52db9ba70e0cc0f6eaf7803dd07447a1f5477735fd3f661792ba94600c84e971
DRAND_CHAIN_PUBLIC_KEY=83cf0f2896adee7eb8b5f01fcad3912212c437e0073e911fb90022d3e760183c8c4b450b6a0a6c3ac6a5776a2d1064510d1fec758c921cc22b0e17e63aaf4bcb5ed66304de9cf809bd274ca73bab4af5a6e9c76a4bc09e76eae8991ef5ece45a

PF_API_KEY=random-token-for-provably-fair-api-requests
