module.exports = {
    env: {
        commonjs: true,
        es2021: true,
        node: true,
    },
    extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended'],
    parser: '@typescript-eslint/parser',
    plugins: ['@typescript-eslint'],
    root: true,
    parserOptions: {
        ecmaVersion: 12,
        sourceType: 'module',
    },
    ignorePatterns: [
        'dist/*',
        'dev/*',
        'scripts/*',
    ],
    rules: {
        // 4 space indents
        'indent': ['error', 4, { 'SwitchCase': 1 }],
        'max-len': [
            'error', {
                'code': 150,
                'ignoreStrings': true,
                'ignoreTemplateLiterals': true,
                'ignoreComments': true,
            }],
        'linebreak-style': 'off',
        // No parens for arrow functions when not needed
        'arrow-parens': ['error', 'as-needed', { 'requireForBlockBody': true }],
        // Quote strings with single quotes
        'quotes': ['error', 'single'],
        // Always require semicolons
        '@typescript-eslint/semi': ['error', 'always'],
        // Only quote object property names when needed
        'quote-props': ['error', 'as-needed'],
        // No trailing spaces at the end of lines
        'no-trailing-spaces': 'error',
        // Require spaces between keywords and parens e.g. `if ()`
        'keyword-spacing': 'error',
        // Don't allow unused variables
        'no-unused-vars': ['error', {vars: 'local', args: 'none', varsIgnorePattern: '^_'}],
        // Always require objects to be accessed with dot notation when possible, e.g. bans obj['foo']
        'dot-notation': 'error',
        // Allow explicitly defining variable types when type can be inferred
        '@typescript-eslint/no-inferrable-types': 'off',
        // Allow non-null assertion, e.g. this.foo!.bar
        '@typescript-eslint/no-non-null-assertion': 'off',
    },
};
