# Production Environment Configuration for staging.skinsarena.gg
# This file contains all environment variables needed for production deployment

# Application Configuration
APP_NAME=duel
COMPOSE_PROJECT_NAME=duel-production
COMPOSE_PROFILES=duel
NODE_ENV=production
APP_ENV=production
APP_DEBUG=false

# Domain Configuration (Cloudflare Zero Trust handles SSL)
DOMAIN=staging.skinsarena.gg
APP_URL=http://staging.skinsarena.gg

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=duel-database
DB_PORT=3306
DB_DATABASE=duel
DB_USERNAME=duel
DB_PASSWORD=duel

# Trading Database Configuration
DB_TRADING_CONNECTION=mysql
DB_TRADING_HOST=duel-database-trading
DB_TRADING_PORT=3306
DB_TRADING_DATABASE=duel_trading
DB_TRADING_USERNAME=duel
DB_TRADING_PASSWORD=duel

# Redis Configuration
REDIS_HOST=duel-redis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_SOCKET_HOST=duel-redis-socket
REDIS_SOCKET_PORT=6379

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.skinsarena.gg
SESSION_SECURE_COOKIE=false

# Cache Configuration
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail Configuration (configure with your SMTP settings)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Skins Arena"

# API Configuration
API_URL=https://staging.skinsarena.gg/api
INTERNAL_API_URL=http://duel-backend/api/v2/socket
INTERNAL_API_TOKEN=random-token-for-internal-socket-api-requests

# WebSocket Configuration (Cloudflare will upgrade to WSS)
SOCKET_SERVER_ADDRESS=http://staging.skinsarena.gg
TRADE_SERVER_ADDRESS=http://staging.skinsarena.gg
VITE_SOCKET_SERVER_ADDRESS=http://staging.skinsarena.gg
VITE_TRADE_SERVER_ADDRESS=http://staging.skinsarena.gg
VITE_API_URL=http://staging.skinsarena.gg/api

# Roulette Server Configuration
ROULETTE_ENABLED=true
COINFLIP_ENABLED=true
CHAT_ENABLED=true
MATCHBETTING_ENABLED=false
CASEBATTLE_ENABLED=false
TRADE_ENABLED=false

# Provably Fair Service Configuration
PF_API_KEY=random-token-for-provably-fair-api-requests
DRAND_CHAIN_HASH=52db9ba70e0cc0f6eaf7803dd07447a1f5477735fd3f661792ba94600c84e971
DRAND_CHAIN_PUBLIC_KEY=83cf0f2896adee7eb8b5f01fcad3912212c437e0073e911fb90022d3e760183c8c4b450b6a0a6c3ac6a5776a2d1064510d1fec758c921cc22b0e17e63aaf4bcb5ed66304de9cf809bd274ca73bab4af5a6e9c76a4bc09e76eae8991ef5ece45a

# Steam API Configuration (get your own keys from https://steamcommunity.com/dev/apikey)
STEAM_API_KEY=your-steam-api-key-here
STEAM_WEB_API_KEY=your-steam-web-api-key-here

# Security Configuration
APP_KEY=base64:your-32-character-secret-key-here
JWT_SECRET=your-jwt-secret-key-here
BCRYPT_ROUNDS=12

# Logging Configuration
LOG_CHANNEL=stack
LOG_LEVEL=info
LOG_DEPRECATIONS_CHANNEL=null
LOG_STACK=single

# AWS Configuration (if using AWS services)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-s3-bucket-name

# LocalStack Configuration (for local AWS simulation)
LOCALSTACK_ENDPOINT=http://duel-localstack:4566
AWS_ENDPOINT_URL=http://duel-localstack:4566

# Performance Configuration
OPCACHE_ENABLE=1
OPCACHE_MEMORY_CONSUMPTION=256
OPCACHE_MAX_ACCELERATED_FILES=20000
OPCACHE_VALIDATE_TIMESTAMPS=0

# Frontend Build Configuration
VITE_APP_NAME=duel
VITE_APP_ENV=production
VITE_APP_DEBUG=false

# Admin Panel Configuration
VUE_APP_API_URL=https://staging.skinsarena.gg/api
VUE_APP_SOCKET_URL=https://staging.skinsarena.gg

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_ATTEMPTS=60
RATE_LIMIT_DECAY_MINUTES=1

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://staging.skinsarena.gg
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Timezone Configuration
APP_TIMEZONE=UTC
DB_TIMEZONE=+00:00

# File Upload Configuration
MAX_UPLOAD_SIZE=10M
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
