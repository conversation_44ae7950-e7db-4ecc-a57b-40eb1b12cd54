# Production Docker Compose Configuration
# Optimized for staging.skinsarena.gg deployment

name: duel-production

networks:
  app:
    name: duel-production
    driver: bridge

volumes:
  dbdata:
  dbtradingdata:
  redis:
  sess:
  localstack-data:

services:
  localstack:
    image: localstack/localstack
    container_name: duel-localstack
    environment:
      - SERVICES=kms
      - DEBUG=0
      - PERSISTENCE=1
    volumes:
      - ./local-server/scripts/localstack.sh:/etc/localstack/init/ready.d/init-localstack.sh
      - localstack-data:/var/lib/localstack
    networks:
      - app
    restart: unless-stopped

  redis:
    image: valkey/valkey:7.2.7
    container_name: duel-redis
    sysctls:
      net.core.somaxconn: 1024
    command: ["redis-server", "/redis.conf"]
    volumes:
      - ./local-server/configs/redis.conf:/redis.conf:ro
      - redis:/data
    networks:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-socket:
    image: valkey/valkey:7.2.7
    container_name: duel-redis-socket
    sysctls:
      net.core.somaxconn: 1024
    command: ["redis-server", "/redis.conf"]
    volumes:
      - ./local-server/configs/redis-socket.conf:/redis.conf:ro
    networks:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  database:
    container_name: duel-database
    image: duel/database
    build:
      context: ./local-server
      dockerfile: ./database.dockerfile
    command: --general-log=0 --slow-query-log=1 --slow-query-log-file=/var/log/mysql/mysql-slow-query-log.log --long-query-time=2 --log-bin-trust-function-creators=1 --default-authentication-plugin=mysql_native_password --max-allowed_packet=10485760 --innodb-buffer-pool-size=512M
    env_file:
      - ./local-server/dev.env
    environment:
      APP_NAME: duel
    volumes:
      - ./modules/backend/laravel/tests/database-init/csgoempire.sql:/docker-entrypoint-initdb.d/csgoempire.sql:ro
      - dbdata:/var/lib/mysql
    networks:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  database-trading:
    container_name: duel-database-trading
    image: duel/database
    build:
      context: ./local-server
      dockerfile: ./database.dockerfile
    command: --general-log=0 --slow-query-log=1 --slow-query-log-file=/var/log/mysql/mysql-slow-query-log.log --long-query-time=2 --innodb-ft-min-token-size=2 --innodb-ft-enable-stopword=OFF --log-bin-trust-function-creators=1 --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=256M
    environment:
      APP_NAME: duel
      MYSQL_DATABASE: duel_trading
    env_file:
      - ./local-server/dev.env
    volumes:
      - ./modules/backend/laravel/tests/database-init/csgoempire_trading.sql:/docker-entrypoint-initdb.d/csgoempire_trading.sql:ro
      - dbtradingdata:/var/lib/mysql
    networks:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    container_name: duel-backend
    image: duel/backend
    build:
      context: ./modules/backend
      dockerfile: ./local-server/backend.dockerfile
      args:
        - COMPOSER_IGNORE_PLATFORM_REQ=ext-newrelic
    working_dir: /var/csgoempire
    environment:
      APP_NAME: duel
      DEFAULT_SERVICE: duel
      LOG_FILE_NAME: duel.laravel
      COMPOSER_IGNORE_PLATFORM_REQ: ext-newrelic
      APP_ENV: production
      APP_DEBUG: false
    env_file:
      - ./local-server/dev.env
      - ./modules/backend/laravel/.env.duel
    volumes:
      - ./modules/backend/laravel:/var/csgoempire/laravel:cached
      - sess:/var/csgoempire/sess
      - /var/csgoempire/laravel/vendor
      - /var/csgoempire/vendor
      - ./modules/backend/phpstan.neon:/var/csgoempire/phpstan.neon:ro
      - ./modules/backend/phpstan-baseline.neon:/var/csgoempire/phpstan-baseline.neon:ro
      - ./modules/backend/phpcs.xml:/var/csgoempire/phpcs.xml:ro
      - ./modules/backend/composer.json:/var/csgoempire/composer.json:ro
      - ./modules/backend/composer.lock:/var/csgoempire/composer.lock:ro
      - ./modules/backend/load-aws-secrets.php:/var/csgoempire/load-aws-secrets.php:ro
      - ./modules/backend/local-server/configs/vhosts.conf:/etc/nginx/conf.d/default.conf:ro
    ports:
      - "8080:80"
    depends_on:
      database:
        condition: service_healthy
      database-trading:
        condition: service_healthy
      redis:
        condition: service_healthy
      redis-socket:
        condition: service_healthy
    networks:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    container_name: duel-frontend
    image: duel/frontend
    build:
      context: ./modules/frontend
      dockerfile: Dockerfile
      args:
        IS_LOCAL: "0"
        APP: duel
    working_dir: /var/www/frontend
    environment:
      APP_NAME: duel
      NODE_ENV: production
      VITE_SOCKET_SERVER_ADDRESS: https://staging.skinsarena.gg
      VITE_TRADE_SERVER_ADDRESS: https://staging.skinsarena.gg
      VITE_API_URL: https://staging.skinsarena.gg/api
    env_file:
      - ./modules/frontend/dev.env
      - ./modules/frontend/.env
      - ./modules/frontend/packages/duel/.env
    volumes:
      - ./modules/frontend/vitest.workspace.ts:/var/www/frontend/vitest.workspace.ts:ro
      - ./modules/frontend/package.json:/var/www/frontend/package.json:ro
      - ./modules/frontend/package-lock.json:/var/www/frontend/package-lock.json:ro
      - ./modules/frontend/packages:/var/www/frontend/packages:cached
      - /var/www/frontend/node_modules
      - /var/www/frontend/packages/build-config/node_modules
      - /var/www/frontend/packages/eslint-config/node_modules
      - /var/www/frontend/packages/shared/node_modules
      - /var/www/frontend/packages/duel/node_modules
    ports:
      - "3000:5173"
    networks:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3

  roulette-server:
    container_name: duel-roulette-server
    image: duel/roulette-server
    build:
      context: ./modules/roulette-server
      dockerfile: Dockerfile
    working_dir: /var/www/roulette-server
    environment:
      APP_NAME: duel
      NODE_ENV: production
      REDIS_URL: redis://duel-redis:6379
      REDIS_SOCKET_URL: redis://duel-redis-socket:6379
      INTERNAL_API_URL: http://duel-backend/api/v2/socket
      INTERNAL_API_TOKEN: random-token-for-internal-socket-api-requests
    env_file:
      - ./modules/roulette-server/dev.env
      - ./modules/roulette-server/dev.duel.env
    volumes:
      - ./modules/roulette-server/src:/var/www/roulette-server/src:cached
      - ./modules/roulette-server/package.json:/var/www/roulette-server/package.json:ro
      - ./modules/roulette-server/package-lock.json:/var/www/roulette-server/package-lock.json:ro
      - /var/www/roulette-server/node_modules
    ports:
      - "7000:7000"
      - "7100:7100"
    depends_on:
      backend:
        condition: service_healthy
      redis:
        condition: service_healthy
      redis-socket:
        condition: service_healthy
    networks:
      - app
    restart: unless-stopped

  provably-fair-service:
    container_name: duel-provably-fair-service
    image: duel/provably-fair-service
    build:
      context: ./modules/provably-fair-service
      dockerfile: Dockerfile
    working_dir: /var/www/provably-fair-service
    environment:
      APP_NAME: duel
      NODE_ENV: production
      REDIS_URL: redis://duel-redis:6379
      INTERNAL_API_URL: http://duel-backend/api/v2/socket
      INTERNAL_API_TOKEN: random-token-for-internal-socket-api-requests
      DATABASE_URL: mysql://duel_provably_fair:duel@duel-database:3306/duel_provably_fair
      SHADOW_DATABASE_URL: mysql://duel_provably_fair:duel@duel-database:3306/duel_provably_fair_shadow
    env_file:
      - ./modules/provably-fair-service/.env
    volumes:
      - ./modules/provably-fair-service/src:/var/www/provably-fair-service/src:cached
      - ./modules/provably-fair-service/package.json:/var/www/provably-fair-service/package.json:ro
      - ./modules/provably-fair-service/package-lock.json:/var/www/provably-fair-service/package-lock.json:ro
      - /var/www/provably-fair-service/node_modules
    depends_on:
      backend:
        condition: service_healthy
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - app
    restart: unless-stopped

  admin-panel:
    container_name: duel-admin-panel
    image: duel/admin-panel
    build:
      context: ./modules/admin-panel
      dockerfile: Dockerfile
    working_dir: /var/www/admin-panel
    environment:
      APP_NAME: duel
      NODE_ENV: production
      VUE_APP_API_URL: https://staging.skinsarena.gg/api
    env_file:
      - ./modules/admin-panel/dev.env
    volumes:
      - ./modules/admin-panel/src:/var/www/admin-panel/src:cached
      - ./modules/admin-panel/package.json:/var/www/admin-panel/package.json:ro
      - ./modules/admin-panel/package-lock.json:/var/www/admin-panel/package-lock.json:ro
      - /var/www/admin-panel/node_modules
    ports:
      - "8092:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - app
    restart: unless-stopped
