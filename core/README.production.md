# Production Deployment Guide for staging.skinsarena.gg

This guide will help you deploy the entire application stack to a production Ubuntu server with a single command.

## 🚀 Quick Start

### Prerequisites
- Clean Ubuntu 20.04+ server
- Root or sudo access
- Domain `staging.skinsarena.gg` pointing to your server's IP address

### One-Command Deployment
```bash
# Clone the repository
git clone <your-repo-url> duel
cd duel/core

# Run the deployment script (requires sudo for nginx setup)
sudo bash deploy-staging.sh
```

That's it! The script will:
- Install Docker, Docker Compose, Nginx, and other dependencies
- Set up environment files for production
- Configure Nginx reverse proxy with SSL support
- Build and start all services (backend, frontend, roulette, provably-fair, admin panel)
- Configure health checks and monitoring

## 📋 What Gets Deployed

### Core Services
- **Frontend**: Vue.js application (port 3000 → nginx)
- **Backend**: Laravel API (port 8080 → nginx)
- **Database**: MySQL 8.0 with optimized settings
- **Redis**: For caching and sessions
- **Nginx**: Reverse proxy with SSL termination

### Game Services
- **Roulette Server**: WebSocket game server (port 7000 → nginx)
- **Provably Fair Service**: Cryptographic fairness verification
- **Admin Panel**: Management interface (port 8092 → nginx)

### Infrastructure
- **LocalStack**: AWS services simulation
- **Health Checks**: Automated service monitoring
- **SSL**: Let's Encrypt certificate (optional)

## 🌐 Service URLs

After deployment, your services will be available at:

- **Main Site**: https://staging.skinsarena.gg
- **API**: https://staging.skinsarena.gg/api
- **Admin Panel**: https://staging.skinsarena.gg/admin
- **WebSocket**: wss://staging.skinsarena.gg/socket.io
- **Trade WebSocket**: wss://staging.skinsarena.gg/trade-socket.io
- **Health Check**: https://staging.skinsarena.gg/health

## 🛠️ Manual Management

### Using Production Makefile
```bash
# Use the production-specific Makefile
make -f Makefile.production help

# Common commands
make -f Makefile.production ps          # Show running services
make -f Makefile.production logs        # View all logs
make -f Makefile.production restart     # Restart all services
make -f Makefile.production health      # Check service health
make -f Makefile.production update      # Update deployment
```

### Service Management
```bash
# Start/stop individual services
make -f Makefile.production start
make -f Makefile.production stop

# View logs for specific service
make -f Makefile.production logs service=backend
make -f Makefile.production logs service=frontend
make -f Makefile.production logs service=roulette-server

# SSH into containers
make -f Makefile.production ssh-backend
make -f Makefile.production ssh-frontend
make -f Makefile.production ssh-roulette
```

### Database Management
```bash
# Run migrations
make -f Makefile.production migrate

# Backup database
make -f Makefile.production backup-db

# Restore database
make -f Makefile.production restore-db

# Clear caches
make -f Makefile.production cache-clear

# Optimize for production
make -f Makefile.production optimize
```

### SSL Certificate Management
```bash
# Setup SSL (if not done during deployment)
make -f Makefile.production ssl

# Renew SSL certificate
make -f Makefile.production ssl-renew
```

### Nginx Management
```bash
# Test nginx configuration
make -f Makefile.production nginx-test

# Reload nginx
make -f Makefile.production nginx-reload

# View nginx logs
make -f Makefile.production nginx-access-logs
make -f Makefile.production nginx-error-logs
```

## 🔧 Configuration Files

### Environment Files
- `production.env` - Main production environment variables
- `modules/backend/laravel/.env.production` - Laravel-specific settings
- `docker-compose.production.yml` - Production Docker Compose configuration

### Key Configuration Points
1. **Database**: Optimized MySQL settings for production
2. **Redis**: Configured for caching and sessions
3. **Security**: HTTPS, security headers, rate limiting
4. **Performance**: Opcache, gzip compression, caching
5. **Monitoring**: Health checks and logging

## 🔒 Security Features

- **HTTPS**: SSL/TLS encryption with Let's Encrypt
- **Security Headers**: HSTS, XSS protection, content type sniffing protection
- **Rate Limiting**: API and general request rate limiting
- **CORS**: Properly configured cross-origin resource sharing
- **Secure Cookies**: HTTP-only, secure, same-site cookies

## 📊 Monitoring & Health Checks

### Health Check Endpoints
- Main health check: `https://staging.skinsarena.gg/health`
- API health check: `https://staging.skinsarena.gg/api/health`

### Monitoring Commands
```bash
# Real-time monitoring
make -f Makefile.production monitor

# Resource usage
make -f Makefile.production stats

# Generate status report
make -f Makefile.production status-report

# Check service health
make -f Makefile.production health
```

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check logs
   make -f Makefile.production logs
   
   # Check Docker status
   docker system df
   docker system events
   ```

2. **Database connection issues**
   ```bash
   # Check database container
   make -f Makefile.production logs service=database
   
   # Test database connection
   make -f Makefile.production ssh-backend
   php artisan tinker
   DB::connection()->getPdo();
   ```

3. **Nginx configuration issues**
   ```bash
   # Test nginx config
   make -f Makefile.production nginx-test
   
   # Check nginx logs
   make -f Makefile.production nginx-error-logs
   ```

4. **SSL certificate issues**
   ```bash
   # Check certificate status
   sudo certbot certificates
   
   # Renew certificate
   make -f Makefile.production ssl-renew
   ```

### Emergency Procedures
```bash
# Emergency stop all services
make -f Makefile.production emergency-stop

# Clean up and restart
make -f Makefile.production clean
make -f Makefile.production build-start
```

## 🔄 Updates & Maintenance

### Regular Updates
```bash
# Update application
make -f Makefile.production update

# This will:
# 1. Pull latest code
# 2. Rebuild containers
# 3. Restart services
# 4. Optimize caches
```

### Backup Strategy
```bash
# Create database backup
make -f Makefile.production backup-db

# Set up automated backups (add to crontab)
0 2 * * * cd /path/to/duel/core && make -f Makefile.production backup-db
```

## 📞 Support

If you encounter issues:

1. Check the logs: `make -f Makefile.production logs`
2. Verify service health: `make -f Makefile.production health`
3. Generate status report: `make -f Makefile.production status-report`
4. Check nginx configuration: `make -f Makefile.production nginx-test`

## 🎯 Performance Optimization

The production setup includes:
- **Opcache**: PHP bytecode caching
- **Redis**: Fast in-memory caching
- **Gzip**: Response compression
- **CDN-ready**: Static asset optimization
- **Database**: Optimized MySQL configuration
- **Connection pooling**: Efficient database connections

Your application should now be running smoothly at `https://staging.skinsarena.gg`! 🎉
