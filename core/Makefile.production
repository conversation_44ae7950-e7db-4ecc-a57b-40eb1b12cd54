# Production Makefile for staging.skinsarena.gg
# This Makefile contains production-specific commands

## — Production Usage —

## Show help
help:
	@awk '{ \
			if ($$0 ~ /^.PHONY: [a-zA-Z\-\_0-9]+$$/) { \
				helpCommand = substr($$0, index($$0, ":") + 2); \
				if (helpMessage) { \
					printf "\033[36m%-20s\033[0m %s\n", \
						helpCommand, helpMessage; \
					helpMessage = ""; \
				} \
			} else if ($$0 ~ /^[a-zA-Z\-\_0-9.]+:/) { \
				helpCommand = substr($$0, 0, index($$0, ":")); \
				if (helpMessage) { \
					printf "\033[1;34m%-20s\033[0m %s\n", \
						helpCommand, helpMessage; \
					helpMessage = ""; \
				} \
			} else if ($$0 ~ /^##/) { \
				if (helpMessage) { \
					helpMessage = helpMessage"\n                     "substr($$0, 3); \
				} else { \
					helpMessage = substr($$0, 3); \
				} \
			} else { \
				if (helpMessage) { \
					print "\n "helpMessage"\n" \
				} \
				helpMessage = ""; \
			} \
		}' \
		$(MAKEFILE_LIST)

# Production configuration
app=duel
export APP_NAME=$(app)
export COMPOSE_PROJECT_NAME=$(app)-production
export COMPOSE_PROFILES=$(app)
export COMPOSE_FILE=docker-compose.production.yml

# Include production environment
include production.env
export

## Deploy entire application stack for production
deploy:
	@echo "🚀 Starting production deployment..."
	@chmod +x deploy-staging.sh
	@sudo ./deploy-staging.sh

## Build all production containers
build:
	@echo "🔨 Building production containers..."
	docker compose -f $(COMPOSE_FILE) build --no-cache

## Start all production services
start:
	@echo "▶️  Starting production services..."
	docker compose -f $(COMPOSE_FILE) up -d

## Stop all production services
stop:
	@echo "⏹️  Stopping production services..."
	docker compose -f $(COMPOSE_FILE) stop

## Stop and remove all production containers
down:
	@echo "🔽 Stopping and removing production containers..."
	docker compose -f $(COMPOSE_FILE) down

## Restart all production services
restart: down start

## Build and start production services
build-start: build start

## Show running production containers
ps:
	docker compose -f $(COMPOSE_FILE) ps

## Show production service logs
logs:
	docker compose -f $(COMPOSE_FILE) logs -f $(service)

## SSH into production backend container
ssh-backend:
	docker compose -f $(COMPOSE_FILE) exec backend bash

## SSH into production frontend container
ssh-frontend:
	docker compose -f $(COMPOSE_FILE) exec frontend bash

## SSH into production roulette server container
ssh-roulette:
	docker compose -f $(COMPOSE_FILE) exec roulette-server bash

## Run Laravel migrations in production
migrate:
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan migrate --force

## Run Laravel database seeding in production
seed:
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan db:seed --force

## Clear Laravel caches in production
cache-clear:
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan cache:clear
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan config:clear
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan route:clear
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan view:clear

## Optimize Laravel for production
optimize:
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan config:cache
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan route:cache
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan view:cache

## Run Laravel queue workers
queue:
	docker compose -f $(COMPOSE_FILE) exec backend php laravel/artisan queue:work --daemon

## Backup production database
backup-db:
	@echo "📦 Creating database backup..."
	docker compose -f $(COMPOSE_FILE) exec database mysqldump -u$(DB_USERNAME) -p$(DB_PASSWORD) $(DB_DATABASE) > backup_$(shell date +%Y%m%d_%H%M%S).sql

## Restore production database from backup
restore-db:
	@echo "📥 Restoring database from backup..."
	@read -p "Enter backup file path: " backup_file; \
	docker compose -f $(COMPOSE_FILE) exec -T database mysql -u$(DB_USERNAME) -p$(DB_PASSWORD) $(DB_DATABASE) < $$backup_file

## Monitor production services health
health:
	@echo "🏥 Checking service health..."
	@curl -f http://localhost:8080/api/health || echo "❌ Backend health check failed"
	@curl -f http://localhost:3000 || echo "❌ Frontend health check failed"
	@curl -f http://staging.skinsarena.gg/health || echo "❌ Nginx health check failed (via Cloudflare)"

## Update production deployment
update:
	@echo "🔄 Updating production deployment..."
	git pull origin main
	make build
	make down
	make start
	make optimize

## View production resource usage
stats:
	docker compose -f $(COMPOSE_FILE) top
	docker system df

## Clean up production Docker resources
clean:
	@echo "🧹 Cleaning up Docker resources..."
	docker system prune -f
	docker volume prune -f
	docker image prune -f

## SSL not needed - using Cloudflare Zero Trust
ssl-info:
	@echo "ℹ️  SSL is handled by Cloudflare Zero Trust"
	@echo "   No local SSL certificates needed"

## Test nginx configuration
nginx-test:
	sudo nginx -t

## Reload nginx configuration
nginx-reload:
	sudo nginx -s reload

## Restart nginx service
nginx-restart:
	sudo systemctl restart nginx

## View nginx access logs
nginx-access-logs:
	sudo tail -f /var/log/nginx/access.log

## View nginx error logs
nginx-error-logs:
	sudo tail -f /var/log/nginx/error.log

## Monitor production logs in real-time
monitor:
	@echo "📊 Starting production monitoring..."
	docker compose -f $(COMPOSE_FILE) logs -f --tail=100

## Run production security scan
security-scan:
	@echo "🔍 Running security scan..."
	docker run --rm -v $(PWD):/app securecodewarrior/docker-security-scan /app

## Generate production status report
status-report:
	@echo "📋 Production Status Report"
	@echo "=========================="
	@echo "Date: $(shell date)"
	@echo "Services:"
	@make ps
	@echo ""
	@echo "Health Checks:"
	@make health
	@echo ""
	@echo "Resource Usage:"
	@make stats

## Emergency stop all services
emergency-stop:
	@echo "🚨 Emergency stop - killing all containers..."
	docker compose -f $(COMPOSE_FILE) kill
	docker compose -f $(COMPOSE_FILE) down --remove-orphans

.PHONY: help deploy build start stop down restart build-start ps logs ssh-backend ssh-frontend ssh-roulette migrate seed cache-clear optimize queue backup-db restore-db health update stats clean ssl-info nginx-test nginx-reload nginx-restart nginx-access-logs nginx-error-logs monitor security-scan status-report emergency-stop
