#!/bin/bash

# Quick Deploy Script for staging.skinsarena.gg
# This is a simplified launcher for the main deployment script

echo "🚀 Quick Deploy for staging.skinsarena.gg"
echo "========================================="
echo ""

# Check if running on Ubuntu
if ! grep -q "Ubuntu" /etc/os-release 2>/dev/null; then
    echo "⚠️  Warning: This script is designed for Ubuntu. Proceed with caution on other systems."
    read -p "Continue anyway? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    echo "✅ Running as root - can install system packages"
else
    echo "⚠️  Not running as root - will need sudo for system setup"
    echo "   You may be prompted for your password during installation"
fi

echo ""
echo "This script will:"
echo "  ✅ Install Docker, Docker Compose, Nginx, and dependencies"
echo "  ✅ Set up production environment for staging.skinsarena.gg"
echo "  ✅ Configure Nginx reverse proxy with SSL support"
echo "  ✅ Build and start all services:"
echo "     - Frontend (Vue.js)"
echo "     - Backend (Laravel API)"
echo "     - Roulette Server (WebSocket)"
echo "     - Provably Fair Service"
echo "     - Admin Panel"
echo "     - Database (MySQL)"
echo "     - Redis (Caching)"
echo ""

read -p "Ready to deploy? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

echo ""
echo "🚀 Starting deployment..."

# Make sure we're in the right directory
cd "$(dirname "$0")"

# Run the main deployment script
if [[ $EUID -eq 0 ]]; then
    # Already root
    ./deploy-staging.sh
else
    # Need sudo
    sudo ./deploy-staging.sh
fi

# Check if deployment was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo ""
    echo "Your application is now available at:"
    echo "  🌐 Main Site: https://staging.skinsarena.gg"
    echo "  🔧 Admin Panel: https://staging.skinsarena.gg/admin"
    echo "  📊 Health Check: https://staging.skinsarena.gg/health"
    echo ""
    echo "Useful commands:"
    echo "  📋 Check status: make -f Makefile.production ps"
    echo "  📝 View logs: make -f Makefile.production logs"
    echo "  🔄 Restart: make -f Makefile.production restart"
    echo "  📖 Full guide: cat README.production.md"
else
    echo ""
    echo "❌ Deployment failed!"
    echo "Check the logs above for error details."
    echo "You can also check:"
    echo "  - Docker status: docker ps"
    echo "  - Nginx status: sudo systemctl status nginx"
    echo "  - Service logs: make -f Makefile.production logs"
    exit 1
fi
