#!/bin/bash

# Production Deployment Script for staging.skinsarena.gg
# This script sets up the entire application stack with nginx reverse proxy

set -e

echo "🚀 Starting production deployment for staging.skinsarena.gg..."

# Configuration
DOMAIN="staging.skinsarena.gg"
APP_NAME="duel"
NGINX_CONFIG_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
PROJECT_DIR="$(pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root for nginx setup
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. This is required for nginx setup but not recommended for docker operations."
    fi
}

# Install required packages
install_dependencies() {
    print_status "Installing required dependencies..."

    # Update package list
    apt update

    # Install Docker if not present
    if ! command -v docker &> /dev/null; then
        print_status "Installing Docker..."
        apt install -y docker.io
        systemctl start docker
        systemctl enable docker
    fi

    # Install Docker Compose standalone
    if ! command -v docker-compose &> /dev/null; then
        print_status "Installing Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    fi

    # Install nginx if not present
    if ! command -v nginx &> /dev/null; then
        print_status "Installing Nginx..."
        apt install -y nginx
    fi

    # Install other dependencies
    apt install -y make git curl

    print_status "Dependencies installed successfully!"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."

    # Export environment variables
    export APP_NAME="$APP_NAME"
    export COMPOSE_PROJECT_NAME="$APP_NAME"
    export COMPOSE_PROFILES="$APP_NAME"

    # Create production environment file
    cat > "$PROJECT_DIR/production.env" << EOF
# Production Environment Configuration
APP_NAME=$APP_NAME
COMPOSE_PROJECT_NAME=$APP_NAME
COMPOSE_PROFILES=$APP_NAME

# Production ports (internal to docker network)
EXPOSED_PORT_REDIS=6379
EXPOSED_PORT_REDIS_SOCKET=6380
EXPOSED_PORT_DATABASE=3306
EXPOSED_PORT_DATABASE_TRADING=3307
EXPOSED_PORT_API=8080
EXPOSED_PORT_FRONTEND=3000
EXPOSED_PORT_STORYBOOK=6006
EXPOSED_PORT_PMA=8090
EXPOSED_SOCKET_PROXY_PORT=7000
EXPOSED_TRADE_SOCKET_PROXY_PORT=7100
EXPOSED_PORT_ADMIN_PANEL=8092
PMA_HOSTS=database

# Domain configuration
DOMAIN=$DOMAIN
EOF

    # Source the environment
    source "$PROJECT_DIR/production.env"

    print_status "Environment files configured!"
}

# Setup nginx configuration
setup_nginx() {
    print_status "Setting up Nginx reverse proxy..."

    # Create nginx configuration
    cat > "$NGINX_CONFIG_DIR/$DOMAIN" << 'EOF'
# Nginx configuration for staging.skinsarena.gg (Cloudflare Zero Trust)
upstream frontend {
    server 127.0.0.1:3000;
}

upstream backend {
    server 127.0.0.1:8080;
}

upstream websocket {
    server 127.0.0.1:7000;
}

upstream trade_websocket {
    server 127.0.0.1:7100;
}

upstream admin_panel {
    server 127.0.0.1:8092;
}

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

server {
    listen 80;
    server_name staging.skinsarena.gg;

    # Security headers (Cloudflare handles most security, but adding basic ones)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Robots-Tag "noindex, nofollow" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;

    # Client max body size
    client_max_body_size 100M;

    # API routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket connections for game server
    location /socket.io/ {
        proxy_pass http://websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket connections for trade server
    location /trade-socket.io/ {
        proxy_pass http://trade_websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Admin panel
    location /admin/ {
        proxy_pass http://admin_panel/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static assets with caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        limit_req zone=general burst=50 nodelay;
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Frontend application (default)
    location / {
        limit_req zone=general burst=50 nodelay;
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

    # Enable the site
    ln -sf "$NGINX_CONFIG_DIR/$DOMAIN" "$NGINX_ENABLED_DIR/$DOMAIN"

    # Remove default nginx site if it exists
    if [ -f "$NGINX_ENABLED_DIR/default" ]; then
        rm "$NGINX_ENABLED_DIR/default"
    fi

    # Test nginx configuration
    nginx -t

    print_status "Nginx configuration created and tested successfully!"
}

# Build and start services
deploy_services() {
    print_status "Building and starting services..."

    # Source environment
    source "$PROJECT_DIR/production.env"

    # Stop any existing containers
    docker-compose -f docker-compose.production.yml down || true

    # Build all containers
    print_status "Building containers..."
    docker-compose -f docker-compose.production.yml build --no-cache

    # Start all services
    print_status "Starting all services..."
    docker-compose -f docker-compose.production.yml up -d

    print_status "All services started successfully!"
}

# Skip SSL setup - using Cloudflare Zero Trust
setup_ssl() {
    print_status "Skipping SSL setup - using Cloudflare Zero Trust for SSL termination"
}

# Main deployment function
main() {
    print_status "Starting deployment process..."

    check_permissions
    install_dependencies
    setup_environment
    setup_nginx
    deploy_services

    # Restart nginx
    systemctl restart nginx
    systemctl enable nginx

    setup_ssl

    print_status "🎉 Deployment completed successfully!"
    print_status "Your application is now available at: http://$DOMAIN (via Cloudflare Zero Trust)"
    print_status ""
    print_status "Services running on port 80:"
    print_status "- Frontend: http://$DOMAIN"
    print_status "- API: http://$DOMAIN/api"
    print_status "- Admin Panel: http://$DOMAIN/admin"
    print_status "- WebSocket: ws://$DOMAIN/socket.io"
    print_status "- Trade WebSocket: ws://$DOMAIN/trade-socket.io"
    print_status ""
    print_status "To check service status: make ps"
    print_status "To view logs: docker compose logs -f [service-name]"
}

# Run main function
main "$@"
